#!/usr/bin/env python3
"""
Test script for the force delete task API endpoint
"""
import requests
import json
import uuid
import time

# Configuration
BASE_URL = "http://localhost:9977"  # Adjust if your API runs on a different port
USER_ID = "test-user-123"

def test_force_delete_task():
    """Test the force delete task functionality"""
    print("🧪 Testing Force Delete Task API Endpoint")
    print("=" * 50)
    
    # Headers for all requests
    headers = {
        "X-USER-ID": USER_ID,
        "Content-Type": "application/json"
    }
    
    # Step 1: Create a test task using the task manager directly
    print("1. Creating a test task...")
    
    # We'll use the existing task creation mechanism
    # For this test, we'll assume there's already a task in the system
    # or we'll create one through the API if available
    
    # Step 2: List existing tasks to find one to delete
    print("2. Listing existing tasks...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/tasks", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tasks = data.get("data", {}).get("tasks", [])
            print(f"   Found {len(tasks)} tasks")
            
            if tasks:
                # Use the first task for testing
                test_task = tasks[0]
                task_uuid = test_task["uuid"]
                print(f"   Using task: {task_uuid}")
                
                # Step 3: Get task details before deletion
                print("3. Getting task details before deletion...")
                response = requests.get(f"{BASE_URL}/api/tasks/{task_uuid}", headers=headers)
                if response.status_code == 200:
                    task_data = response.json()
                    print(f"   Task found: {task_data['data']['task']['name']}")
                    print(f"   Status: {task_data['data']['task']['status']}")
                else:
                    print(f"   Failed to get task details: {response.status_code}")
                    return
                
                # Step 4: Force delete the task
                print("4. Force deleting the task...")
                response = requests.delete(f"{BASE_URL}/api/tasks/{task_uuid}", headers=headers)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   Response: {json.dumps(result, indent=2)}")
                    
                    # Step 5: Verify task is deleted
                    print("5. Verifying task is deleted...")
                    response = requests.get(f"{BASE_URL}/api/tasks/{task_uuid}", headers=headers)
                    if response.status_code == 404:
                        print("   ✅ Task successfully deleted!")
                    else:
                        print(f"   ❌ Task still exists (status: {response.status_code})")
                        
                else:
                    result = response.json()
                    print(f"   ❌ Delete failed: {json.dumps(result, indent=2)}")
                    
            else:
                print("   No tasks found to test deletion")
                print("   💡 Create a task first using the API or application")
                
        else:
            print(f"   ❌ Failed to list tasks: {response.status_code}")
            if response.text:
                print(f"   Response: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed. Make sure the API server is running.")
        print("   💡 Start the server with: python api.py")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def test_error_cases():
    """Test error cases for the force delete endpoint"""
    print("\n🧪 Testing Error Cases")
    print("=" * 30)
    
    headers = {
        "X-USER-ID": USER_ID,
        "Content-Type": "application/json"
    }
    
    # Test 1: Delete non-existent task
    print("1. Testing deletion of non-existent task...")
    fake_uuid = str(uuid.uuid4())
    
    try:
        response = requests.delete(f"{BASE_URL}/api/tasks/{fake_uuid}", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 404:
            result = response.json()
            print(f"   ✅ Correctly returned 404: {result['msg']}")
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test 2: Delete without user ID header
    print("2. Testing deletion without X-USER-ID header...")
    
    try:
        response = requests.delete(f"{BASE_URL}/api/tasks/{fake_uuid}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print(f"   ✅ Correctly returned 400: {result['msg']}")
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Force Delete Task API Test")
    print("=" * 40)
    print(f"Base URL: {BASE_URL}")
    print(f"User ID: {USER_ID}")
    print()
    
    test_force_delete_task()
    test_error_cases()
    
    print("\n✨ Test completed!")
    print("\n💡 Tips:")
    print("   - Make sure the API server is running")
    print("   - Create some test tasks first if none exist")
    print("   - Check the server logs for detailed error information")
