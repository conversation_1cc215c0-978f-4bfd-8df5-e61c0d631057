syntax = "proto3";

package exmsg.services;

option go_package = "github.com/nhdms/base-go/proto/exmsg/services;services";

import "models/sample.proto";
import "models/common.proto";

service SampleService {
    rpc GetSamples (SampleRequest) returns (SampleResponse);
}

message SampleRequest {
  exmsg.models.Query query = 1;
  int64 id = 2;
  repeated int64 ids = 3;
}

message SampleResponse {
  repeated exmsg.models.Sample sample = 1;
  exmsg.models.SQLResult exec_result = 2;
}

