syntax = "proto3";

package exmsg.services;

option go_package = "github.com/nhdms/base-go/proto/exmsg/services;services";

import "models/user.proto";
import "models/common.proto";

service UserService {
    rpc GetUserByID (UserRequest) returns (UserResponse);
    rpc GetUsersByOptions (GetUsersByOptionsRequest) returns (GetUsersByOptionsResponse);
}

message UserRequest {
  int64 user_id = 1;
}

message UserResponse {
  exmsg.models.User user = 1;
}

message GetUsersByOptionsRequest {
  int64 company_id = 1;
  string sort_field = 2;
  bool is_asc = 3;
  repeated int64 ids = 4;
}

message GetUsersByOptionsResponse {
  repeated exmsg.models.User users = 1;
}