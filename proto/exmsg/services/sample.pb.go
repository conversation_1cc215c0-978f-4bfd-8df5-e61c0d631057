// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.5.1
// source: services/sample.proto

package services

import (
	models "github.com/nhdms/base-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SampleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query *models.Query `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Id    int64         `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Ids   []int64       `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SampleRequest) Reset() {
	*x = SampleRequest{}
	mi := &file_services_sample_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleRequest) ProtoMessage() {}

func (x *SampleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_sample_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleRequest.ProtoReflect.Descriptor instead.
func (*SampleRequest) Descriptor() ([]byte, []int) {
	return file_services_sample_proto_rawDescGZIP(), []int{0}
}

func (x *SampleRequest) GetQuery() *models.Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *SampleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SampleRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type SampleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sample     []*models.Sample  `protobuf:"bytes,1,rep,name=sample,proto3" json:"sample,omitempty"`
	ExecResult *models.SQLResult `protobuf:"bytes,2,opt,name=exec_result,json=execResult,proto3" json:"exec_result,omitempty"`
}

func (x *SampleResponse) Reset() {
	*x = SampleResponse{}
	mi := &file_services_sample_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleResponse) ProtoMessage() {}

func (x *SampleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_sample_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleResponse.ProtoReflect.Descriptor instead.
func (*SampleResponse) Descriptor() ([]byte, []int) {
	return file_services_sample_proto_rawDescGZIP(), []int{1}
}

func (x *SampleResponse) GetSample() []*models.Sample {
	if x != nil {
		return x.Sample
	}
	return nil
}

func (x *SampleResponse) GetExecResult() *models.SQLResult {
	if x != nil {
		return x.ExecResult
	}
	return nil
}

var File_services_sample_proto protoreflect.FileDescriptor

var file_services_sample_proto_rawDesc = []byte{
	0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x73, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x5c, 0x0a, 0x0d, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x29, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x78, 0x0a, 0x0e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x06, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x06, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12,
	0x38, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x53, 0x51, 0x4c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x65,
	0x78, 0x65, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x5c, 0x0a, 0x0d, 0x53, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x1d, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65,
	0x6e, 0x61, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73,
	0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_services_sample_proto_rawDescOnce sync.Once
	file_services_sample_proto_rawDescData = file_services_sample_proto_rawDesc
)

func file_services_sample_proto_rawDescGZIP() []byte {
	file_services_sample_proto_rawDescOnce.Do(func() {
		file_services_sample_proto_rawDescData = protoimpl.X.CompressGZIP(file_services_sample_proto_rawDescData)
	})
	return file_services_sample_proto_rawDescData
}

var file_services_sample_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_sample_proto_goTypes = []any{
	(*SampleRequest)(nil),    // 0: exmsg.services.SampleRequest
	(*SampleResponse)(nil),   // 1: exmsg.services.SampleResponse
	(*models.Query)(nil),     // 2: exmsg.models.Query
	(*models.Sample)(nil),    // 3: exmsg.models.Sample
	(*models.SQLResult)(nil), // 4: exmsg.models.SQLResult
}
var file_services_sample_proto_depIdxs = []int32{
	2, // 0: exmsg.services.SampleRequest.query:type_name -> exmsg.models.Query
	3, // 1: exmsg.services.SampleResponse.sample:type_name -> exmsg.models.Sample
	4, // 2: exmsg.services.SampleResponse.exec_result:type_name -> exmsg.models.SQLResult
	0, // 3: exmsg.services.SampleService.GetSamples:input_type -> exmsg.services.SampleRequest
	1, // 4: exmsg.services.SampleService.GetSamples:output_type -> exmsg.services.SampleResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_services_sample_proto_init() }
func file_services_sample_proto_init() {
	if File_services_sample_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_services_sample_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_sample_proto_goTypes,
		DependencyIndexes: file_services_sample_proto_depIdxs,
		MessageInfos:      file_services_sample_proto_msgTypes,
	}.Build()
	File_services_sample_proto = out.File
	file_services_sample_proto_rawDesc = nil
	file_services_sample_proto_goTypes = nil
	file_services_sample_proto_depIdxs = nil
}
