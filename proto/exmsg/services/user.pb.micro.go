// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/user.proto

package services

import (
	fmt "fmt"
	_ "github.com/nhdms/base-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for UserService service

type UserService interface {
	GetUserByID(ctx context.Context, in *UserRequest, opts ...client.CallOption) (*UserResponse, error)
	GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...client.CallOption) (*GetUsersByOptionsResponse, error)
}

type userService struct {
	c    client.Client
	name string
}

func NewUserService(name string, c client.Client) UserService {
	return &userService{
		c:    c,
		name: name,
	}
}

func (c *userService) GetUserByID(ctx context.Context, in *UserRequest, opts ...client.CallOption) (*UserResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUserByID", in)
	out := new(UserResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userService) GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, opts ...client.CallOption) (*GetUsersByOptionsResponse, error) {
	req := c.c.NewRequest(c.name, "UserService.GetUsersByOptions", in)
	out := new(GetUsersByOptionsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for UserService service

type UserServiceHandler interface {
	GetUserByID(context.Context, *UserRequest, *UserResponse) error
	GetUsersByOptions(context.Context, *GetUsersByOptionsRequest, *GetUsersByOptionsResponse) error
}

func RegisterUserServiceHandler(s server.Server, hdlr UserServiceHandler, opts ...server.HandlerOption) error {
	type userService interface {
		GetUserByID(ctx context.Context, in *UserRequest, out *UserResponse) error
		GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, out *GetUsersByOptionsResponse) error
	}
	type UserService struct {
		userService
	}
	h := &userServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&UserService{h}, opts...))
}

type userServiceHandler struct {
	UserServiceHandler
}

func (h *userServiceHandler) GetUserByID(ctx context.Context, in *UserRequest, out *UserResponse) error {
	return h.UserServiceHandler.GetUserByID(ctx, in, out)
}

func (h *userServiceHandler) GetUsersByOptions(ctx context.Context, in *GetUsersByOptionsRequest, out *GetUsersByOptionsResponse) error {
	return h.UserServiceHandler.GetUsersByOptions(ctx, in, out)
}
