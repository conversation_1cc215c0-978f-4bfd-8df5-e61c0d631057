// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/sample.proto

package services

import (
	fmt "fmt"
	_ "github.com/nhdms/base-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for SampleService service

type SampleService interface {
	GetSamples(ctx context.Context, in *SampleRequest, opts ...client.CallOption) (*SampleResponse, error)
}

type sampleService struct {
	c    client.Client
	name string
}

func NewSampleService(name string, c client.Client) SampleService {
	return &sampleService{
		c:    c,
		name: name,
	}
}

func (c *sampleService) GetSamples(ctx context.Context, in *SampleRequest, opts ...client.CallOption) (*SampleResponse, error) {
	req := c.c.NewRequest(c.name, "SampleService.GetSamples", in)
	out := new(SampleResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for SampleService service

type SampleServiceHandler interface {
	GetSamples(context.Context, *SampleRequest, *SampleResponse) error
}

func RegisterSampleServiceHandler(s server.Server, hdlr SampleServiceHandler, opts ...server.HandlerOption) error {
	type sampleService interface {
		GetSamples(ctx context.Context, in *SampleRequest, out *SampleResponse) error
	}
	type SampleService struct {
		sampleService
	}
	h := &sampleServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&SampleService{h}, opts...))
}

type sampleServiceHandler struct {
	SampleServiceHandler
}

func (h *sampleServiceHandler) GetSamples(ctx context.Context, in *SampleRequest, out *SampleResponse) error {
	return h.SampleServiceHandler.GetSamples(ctx, in, out)
}
