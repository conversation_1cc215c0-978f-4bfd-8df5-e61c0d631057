version: '3.8'

services:
  pyvideotrans:
    build: .
    container_name: pyvideotrans-api
    ports:
      - "9011:9011"
    environment:
      # Database configuration
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=pyvideotrans
      - POSTGRES_USER=pyvideotrans
      - POSTGRES_PASSWORD=pyvideotrans_password
      
      # API Keys (set these in your .env file)
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY:-}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY:-}
      - XAI_API_KEY=${XAI_API_KEY:-}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-}
      
      # Application settings
      - PYTHONUNBUFFERED=1
      - TZ=UTC
    volumes:
      # Persistent data
      - ./data/apidata:/app/apidata
      - ./data/tmp:/app/tmp
      - ./data/logs:/app/logs
      - ./data/models:/app/models
      - ./data/videotrans:/app/videotrans
      
      # Configuration files (optional)
      - ./host.txt:/app/host.txt:ro
      - ./params.json:/app/params.json:ro
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - pyvideotrans-network

  postgres:
    image: postgres:15-alpine
    container_name: pyvideotrans-postgres
    environment:
      - POSTGRES_DB=pyvideotrans
      - POSTGRES_USER=pyvideotrans
      - POSTGRES_PASSWORD=pyvideotrans_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pyvideotrans -d pyvideotrans"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - pyvideotrans-network

volumes:
  postgres_data:
    driver: local

networks:
  pyvideotrans-network:
    driver: bridge
