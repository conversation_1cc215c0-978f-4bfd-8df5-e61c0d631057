#!/usr/bin/env python3
"""
PostgreSQL setup script for pyvideotrans configuration system
"""
import argparse
import json
import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


def create_postgres_config():
    """Interactive setup for PostgreSQL configuration"""
    print("PostgreSQL Configuration Setup")
    print("=" * 40)
    
    config = {}
    
    # Get database connection details
    config['host'] = input("PostgreSQL Host [localhost]: ").strip() or 'localhost'
    
    port_input = input("PostgreSQL Port [5432]: ").strip()
    config['port'] = int(port_input) if port_input else 5432
    
    config['database'] = input("Database Name [pyvideotrans]: ").strip() or 'pyvideotrans'
    config['username'] = input("Username [pyvideotrans]: ").strip() or 'pyvideotrans'
    
    # Get password
    import getpass
    config['password'] = getpass.getpass("Password: ")
    
    return config


def save_postgres_config(config):
    """Save PostgreSQL configuration to file"""
    try:
        config_file = "videotrans/postgres_config.json"
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"Configuration saved to: {config_file}")
        return True
    except Exception as e:
        print(f"Error saving configuration: {e}")
        return False


def test_postgres_connection(config):
    """Test PostgreSQL connection"""
    try:
        from videotrans.database.postgres_config import PostgreSQLConfig
        
        # Create temporary config
        pg_config = PostgreSQLConfig()
        pg_config._config = config
        
        if pg_config.test_connection():
            print("✓ PostgreSQL connection successful!")
            return True
        else:
            print("✗ PostgreSQL connection failed!")
            return False
    except Exception as e:
        print(f"✗ Connection test error: {e}")
        return False


def setup_database(config):
    """Setup PostgreSQL database and tables"""
    try:
        from videotrans.database.postgres_config import PostgreSQLConfig
        from videotrans.database.models import DatabaseManager
        
        # Create temporary config
        pg_config = PostgreSQLConfig()
        pg_config._config = config
        
        # Create database if it doesn't exist
        print("Creating database if it doesn't exist...")
        if not pg_config.create_database_if_not_exists():
            print("Failed to create database")
            return False
        
        # Initialize database manager with PostgreSQL
        print("Initializing database tables...")
        db_manager = DatabaseManager(use_postgresql=True)
        db_manager.initialize_database()
        
        print("✓ Database setup completed!")
        return True
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False


def migrate_from_sqlite():
    """Migrate existing data from SQLite to PostgreSQL"""
    try:
        from videotrans.database.postgres_migration import migrate_to_postgresql
        
        print("Migrating data from SQLite to PostgreSQL...")
        if migrate_to_postgresql():
            print("✓ Migration completed successfully!")
            return True
        else:
            print("✗ Migration failed!")
            return False
    except Exception as e:
        print(f"✗ Migration error: {e}")
        return False


def verify_setup():
    """Verify PostgreSQL setup"""
    try:
        from videotrans.database.config_manager import get_config_manager
        
        print("Verifying PostgreSQL setup...")
        config_manager = get_config_manager()
        
        # Test basic operations
        test_key = "postgres_test_setting"
        test_value = "test_value"
        
        config_manager.set(test_key, test_value)
        retrieved_value = config_manager.get(test_key)
        
        if retrieved_value == test_value:
            print("✓ Configuration system working with PostgreSQL!")
            config_manager.delete(test_key)  # Clean up test setting
            return True
        else:
            print("✗ Configuration system test failed!")
            return False
            
    except Exception as e:
        print(f"✗ Verification error: {e}")
        return False


def show_connection_info():
    """Show current PostgreSQL connection information"""
    try:
        from videotrans.database.postgres_config import postgres_config
        
        print("Current PostgreSQL Configuration:")
        print("=" * 40)
        print(f"Connection: {postgres_config.get_connection_info()}")
        print(f"Database URL: {postgres_config.get_database_url()}")
        
        if postgres_config.test_connection():
            print("Status: ✓ Connected")
        else:
            print("Status: ✗ Connection failed")
            
    except Exception as e:
        print(f"Error getting connection info: {e}")


def main():
    parser = argparse.ArgumentParser(description='PostgreSQL setup for pyvideotrans')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Setup command
    setup_parser = subparsers.add_parser('setup', help='Interactive PostgreSQL setup')
    setup_parser.add_argument('--migrate', action='store_true', 
                             help='Migrate existing SQLite data to PostgreSQL')
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Configure PostgreSQL connection')
    config_parser.add_argument('--host', default='localhost', help='PostgreSQL host')
    config_parser.add_argument('--port', type=int, default=5432, help='PostgreSQL port')
    config_parser.add_argument('--database', default='pyvideotrans', help='Database name')
    config_parser.add_argument('--username', default='pyvideotrans', help='Username')
    config_parser.add_argument('--password', required=True, help='Password')
    
    # Test command
    subparsers.add_parser('test', help='Test PostgreSQL connection')
    
    # Migrate command
    subparsers.add_parser('migrate', help='Migrate SQLite data to PostgreSQL')
    
    # Info command
    subparsers.add_parser('info', help='Show PostgreSQL connection information')
    
    # Verify command
    subparsers.add_parser('verify', help='Verify PostgreSQL setup')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    success = False
    
    if args.command == 'setup':
        # Interactive setup
        config = create_postgres_config()
        
        if save_postgres_config(config):
            if test_postgres_connection(config):
                if setup_database(config):
                    if args.migrate:
                        success = migrate_from_sqlite()
                    else:
                        success = True
                        print("\nSetup completed! Use --migrate to transfer existing data.")
    
    elif args.command == 'config':
        # Command-line configuration
        config = {
            'host': args.host,
            'port': args.port,
            'database': args.database,
            'username': args.username,
            'password': args.password
        }
        
        if save_postgres_config(config):
            success = test_postgres_connection(config)
    
    elif args.command == 'test':
        try:
            from videotrans.database.postgres_config import postgres_config
            success = postgres_config.test_connection()
            if success:
                print("✓ PostgreSQL connection successful!")
            else:
                print("✗ PostgreSQL connection failed!")
        except Exception as e:
            print(f"✗ Connection test error: {e}")
    
    elif args.command == 'migrate':
        success = migrate_from_sqlite()
    
    elif args.command == 'info':
        show_connection_info()
        success = True
    
    elif args.command == 'verify':
        success = verify_setup()
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
