#!/usr/bin/env python3
"""
Interactive getting started script for database configuration system
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))


def show_banner():
    """Show welcome banner"""
    print("=" * 60)
    print("🎉 Welcome to PyVideoTrans Database Configuration System!")
    print("=" * 60)
    print()


def check_system_status():
    """Check and display system status"""
    print("📊 Checking system status...")
    print("-" * 30)
    
    try:
        from videotrans.database.config_manager import get_config_manager
        from videotrans.database.migrations import ConfigMigrator
        
        config_manager = get_config_manager()
        migrator = ConfigMigrator()
        
        # Check database availability
        db_available = config_manager._is_database_available()
        needs_migration = migrator.needs_migration()
        
        print(f"✅ Database available: {db_available}")
        print(f"📦 Needs migration: {needs_migration}")
        
        if db_available:
            settings_count = len(config_manager.get_all())
            print(f"⚙️  Total settings: {settings_count}")
        
        # Check database type
        try:
            from videotrans.database.models import db_manager
            if hasattr(db_manager, 'use_postgresql') and db_manager.use_postgresql:
                print(f"🐘 Database type: PostgreSQL")
            else:
                print(f"🗃️  Database type: SQLite")
        except Exception:
            print(f"❓ Database type: Unknown")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
        return False


def show_quick_examples():
    """Show quick usage examples"""
    print("\n🚀 Quick Usage Examples:")
    print("-" * 30)
    
    print("\n1. View your current settings:")
    print("   python manage_config.py list")
    
    print("\n2. View API keys (masked for security):")
    print("   python manage_config.py list -c 'API Keys'")
    
    print("\n3. Set a new configuration:")
    print("   python manage_config.py set my_setting 'my_value' --type string")
    
    print("\n4. Get a specific setting:")
    print("   python manage_config.py get chatgpt_key")
    
    print("\n5. Create a backup:")
    print("   python manage_config.py backup -o my_backup.json")
    
    print("\n6. Check system status:")
    print("   python manage_config.py status")


def show_code_examples():
    """Show code usage examples"""
    print("\n💻 Code Usage Examples:")
    print("-" * 30)
    
    print("\n1. Using existing code patterns (no changes needed):")
    print("""
from videotrans.configure import config

# Get a setting
api_key = config.params['chatgpt_key']

# Set a setting
config.params['new_setting'] = 'value'

# Update multiple settings
config.params.update({
    'chatgpt_key': 'your-key',
    'voice_role': 'alloy'
})
""")
    
    print("\n2. Using new database features:")
    print("""
from videotrans.database.config_manager import get_config_manager

config_manager = get_config_manager()

# Set with metadata
config_manager.set(
    key='api_key',
    value='secret',
    category='API Keys',
    description='My API key',
    is_sensitive=True
)

# Get settings by category
api_settings = config_manager.get_by_category('API Keys')
""")


def interactive_demo():
    """Run interactive demo"""
    print("\n🎮 Interactive Demo:")
    print("-" * 30)
    
    try:
        from videotrans.database.config_manager import get_config_manager
        config_manager = get_config_manager()
        
        # Show current settings count
        all_settings = config_manager.get_all()
        print(f"\n📊 You currently have {len(all_settings)} settings configured.")
        
        # Show categories
        categories = {}
        for key, value in all_settings.items():
            try:
                # Try to get category info
                category_settings = config_manager.get_by_category('API Keys')
                if key in category_settings:
                    categories.setdefault('API Keys', []).append(key)
            except:
                pass
        
        if categories:
            print(f"\n📁 Settings by category:")
            for category, settings in categories.items():
                print(f"   {category}: {len(settings)} settings")
        
        # Interactive setting demo
        print(f"\n🔧 Let's try setting a demo value...")
        demo_key = "demo_setting"
        demo_value = "Hello from database config!"
        
        config_manager.set(demo_key, demo_value, category="Demo", description="Demo setting for getting started")
        retrieved_value = config_manager.get(demo_key)
        
        if retrieved_value == demo_value:
            print(f"✅ Successfully set and retrieved: {demo_key} = {retrieved_value}")
            
            # Clean up demo setting
            config_manager.delete(demo_key)
            print(f"🧹 Cleaned up demo setting")
        else:
            print(f"❌ Demo failed: expected '{demo_value}', got '{retrieved_value}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False


def show_next_steps():
    """Show recommended next steps"""
    print("\n🎯 Recommended Next Steps:")
    print("-" * 30)
    
    print("\n1. 📋 Explore your current configuration:")
    print("   python manage_config.py list")
    
    print("\n2. 🔑 Set up your API keys:")
    print("   python manage_config.py set chatgpt_key 'your-key' --type string")
    
    print("\n3. 💾 Create a backup:")
    print("   python manage_config.py backup -o config_backup.json")
    
    print("\n4. 🐘 Consider upgrading to PostgreSQL:")
    print("   python setup_postgresql.py setup --migrate")
    
    print("\n5. 📖 Read the full documentation:")
    print("   cat GETTING_STARTED_DB_CONFIG.md")
    print("   cat DATABASE_CONFIG_README.md")


def main():
    """Main function"""
    show_banner()
    
    # Check system status
    if not check_system_status():
        print("\n❌ System check failed. Please check your installation.")
        return 1
    
    # Show examples
    show_quick_examples()
    show_code_examples()
    
    # Run interactive demo
    if interactive_demo():
        print("\n✅ Interactive demo completed successfully!")
    
    # Show next steps
    show_next_steps()
    
    print("\n" + "=" * 60)
    print("🎉 You're ready to use the database configuration system!")
    print("=" * 60)
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
