# Video Server Documentation

Welcome to the comprehensive documentation for the Video Server project - a Go-based microservice framework for video processing, translation, and AI-powered content generation.

## 📚 Documentation Structure

```
docs/
├── README.md                    # This file - main documentation index
├── api/                         # API specifications and documentation
│   ├── video-translation-api.yaml  # OpenAPI 3.0 specification
│   └── swagger-ui.html         # Interactive API documentation
├── architecture/               # System architecture documentation
├── deployment/                 # Deployment guides and configurations
└── examples/                   # Code examples and tutorials
```

## 🎯 Available APIs

### Video Translation API
**Location**: `cmd/apis/video-translation-api/`  
**Documentation**: [`api/video-translation-api.yaml`](./api/video-translation-api.yaml)  
**Interactive Docs**: [`api/swagger-ui.html`](./api/swagger-ui.html)

A comprehensive API service that provides:
- **Text-to-Speech (TTS)** conversion with multiple voices
- **Speech Recognition** and transcription
- **Subtitle Translation** between languages
- **Complete Video Translation** workflows
- **AI Image Generation** using BytePlus ModelArk
- **AI Video Generation** using BytePlus and Minimax models
- **File Upload and Management**
- **Batch Processing** capabilities
- **Task Management** with async processing

#### Quick Start
```bash
# Build and run the API
go build -o bin/video-translation-api cmd/apis/video-translation-api/main.go
./bin/video-translation-api

# Test an endpoint
curl -X POST http://localhost:8080/tts \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"text": "Hello world", "voice": "en-US-AriaNeural"}'
```

## 🏗️ Framework Architecture

This project uses a custom Go microservice framework with:

### Core Components
- **Alice Middleware Chains**: Request processing and authentication
- **gRPC Services**: Backend service communication  
- **AMQP Message Queues**: Async task processing
- **PostgreSQL**: Database integration via `dbtool.ConnectionManager`
- **Clean Architecture**: Separation between API and service layers

### Project Structure
```
video-server/
├── cmd/                        # Application entry points
│   ├── apis/                   # HTTP API services
│   ├── services/               # gRPC services
│   └── consumers/              # Message queue consumers
├── internal/                   # Internal packages
├── pkg/                        # Shared packages
│   ├── app/                    # Application framework
│   ├── transport/              # HTTP transport utilities
│   ├── dbtool/                 # Database tools
│   ├── amqp/                   # Message queue tools
│   └── ...                     # Other utilities
├── proto/                      # Protocol buffer definitions
├── migrations/                 # Database migrations
├── docs/                       # Documentation (this folder)
└── scripts/                    # Build and deployment scripts
```

## 🔧 Development Guide

### Prerequisites
- Go 1.21+
- PostgreSQL 13+
- RabbitMQ (for AMQP)
- Docker (optional)

### Building Services
```bash
# Build all services
make build

# Build specific API
make build-video-translation-api

# Run tests
make test
```

### Adding New APIs
1. Create new API directory: `cmd/apis/your-new-api/`
2. Follow the existing structure:
   ```
   cmd/apis/your-new-api/
   ├── main.go              # Entry point
   ├── app/
   │   └── webserver.go     # Routes and middleware
   └── handlers/            # HTTP handlers
       ├── common.go        # Shared utilities
       └── your_handler.go  # Endpoint handlers
   ```
3. Add OpenAPI specification: `docs/api/your-new-api.yaml`
4. Update this documentation

### Response Format Standards
All APIs follow consistent response patterns:

**Success Response**:
```json
{
  "code": 0,
  "msg": "ok", 
  "data": { /* response data */ }
}
```

**Error Response**:
```json
{
  "code": 1,
  "msg": "error message"
}
```

**In Progress Response**:
```json
{
  "code": -1,
  "msg": "processing",
  "data": { "progress": 50 }
}
```

## 🔐 Authentication

The framework supports multiple authentication methods:

1. **API Key**: `X-API-Key` header
2. **Bearer Token**: `Authorization: Bearer <token>` header
3. **Session**: Session cookies

Configure authentication in your webserver.go:
```go
// Example middleware chain with auth
chain := alice.New(
    middlewares.CORS(),
    middlewares.APIKeyAuth(),
    middlewares.Logging(),
).Then(handler)
```

## 📊 Monitoring and Observability

### Logging
- Structured logging via `pkg/logger`
- Request/response logging middleware
- Error tracking and alerting

### Metrics
- Prometheus metrics integration
- Custom business metrics
- Performance monitoring

### Health Checks
- `/health` endpoint for service health
- Database connectivity checks
- External service dependency checks

## 🚀 Deployment

### Docker
```bash
# Build Docker image
docker build -t video-translation-api -f scripts/Dockerfile .

# Run with docker-compose
docker-compose -f scripts/docker-compose.yml up
```

### Kubernetes
```yaml
# Example deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-translation-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: video-translation-api
  template:
    spec:
      containers:
      - name: api
        image: video-translation-api:latest
        ports:
        - containerPort: 8080
```

## 🤖 AI Integration

### Supported AI Providers

#### BytePlus ModelArk
- **Image Generation**: seedream-3-0-t2i-250415 model
- **Video Generation**: seedance-* models
- **Configuration**: API key and endpoint in config

#### Minimax
- **Video Generation**: hailuo, t2v, i2v models  
- **Features**: Prompt optimization, multiple resolutions
- **Configuration**: API key and endpoint in config

### Adding New AI Providers
1. Create provider client in `pkg/ai/`
2. Implement standard interface
3. Add configuration options
4. Update handlers to support new provider

## 📝 API Documentation Standards

When adding new endpoints:

1. **OpenAPI Specification**: Create/update YAML files in `docs/api/`
2. **Request/Response Examples**: Include realistic examples
3. **Error Codes**: Document all possible error responses
4. **Authentication**: Specify required auth methods
5. **Rate Limits**: Document any rate limiting
6. **Deprecation**: Mark deprecated endpoints clearly

### OpenAPI Template
```yaml
/your-endpoint:
  post:
    tags:
      - Your Category
    summary: Brief description
    description: Detailed description
    operationId: yourOperation
    requestBody:
      required: true
      content:
        application/json:
          schema:
            # Your request schema
    responses:
      '200':
        description: Success
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuccessResponse'
```

## 🔄 Future Roadmap

### Planned Features
- [ ] WebSocket support for real-time updates
- [ ] GraphQL API layer
- [ ] Advanced caching strategies
- [ ] Multi-tenant support
- [ ] Enhanced monitoring dashboard
- [ ] Automated API testing suite

### API Expansions
- [ ] Audio processing APIs
- [ ] Image manipulation APIs  
- [ ] Document processing APIs
- [ ] Real-time streaming APIs

## 📞 Support

- **Documentation Issues**: Create GitHub issues
- **API Questions**: Check the interactive Swagger UI
- **Development Help**: Follow the development guide above
- **Architecture Questions**: Review the framework structure

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Last Updated**: 2024-01-01  
**Version**: 1.0.0
