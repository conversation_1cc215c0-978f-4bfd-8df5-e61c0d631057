import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/video-translation-api",
    },
    {
      type: "category",
      label: "Text Processing",
      link: {
        type: "doc",
        id: "api/text-processing",
      },
      items: [
        {
          type: "doc",
          id: "api/text-to-speech",
          label: "Text to Speech Conversion",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Translation",
      link: {
        type: "doc",
        id: "api/translation",
      },
      items: [
        {
          type: "doc",
          id: "api/translate-subtitles",
          label: "Translate Subtitle Files",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Speech Processing",
      link: {
        type: "doc",
        id: "api/speech-processing",
      },
      items: [
        {
          type: "doc",
          id: "api/speech-recognition",
          label: "Speech Recognition",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Video Processing",
      link: {
        type: "doc",
        id: "api/video-processing",
      },
      items: [
        {
          type: "doc",
          id: "api/translate-video",
          label: "Complete Video Translation",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "AI Generation",
      link: {
        type: "doc",
        id: "api/ai-generation",
      },
      items: [
        {
          type: "doc",
          id: "api/generate-image",
          label: "Generate Images",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/generate-video",
          label: "Generate Videos",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/get-video-status",
          label: "Get Video Generation Status",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Task Management",
      link: {
        type: "doc",
        id: "api/task-management",
      },
      items: [
        {
          type: "doc",
          id: "api/get-task-status",
          label: "Get Task Status",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/get-multiple-task-status",
          label: "Get Multiple Task Status",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/get-multiple-task-status-post",
          label: "Get Multiple Task Status (POST)",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "Batch Processing",
      link: {
        type: "doc",
        id: "api/batch-processing",
      },
      items: [
        {
          type: "doc",
          id: "api/batch-process",
          label: "Batch Process Multiple Files",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "File Management",
      link: {
        type: "doc",
        id: "api/file-management",
      },
      items: [
        {
          type: "doc",
          id: "api/upload-file",
          label: "Upload Files",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/serve-file",
          label: "Serve Files",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "Configuration",
      link: {
        type: "doc",
        id: "api/configuration",
      },
      items: [
        {
          type: "doc",
          id: "api/get-voice-list",
          label: "Get Available Voices",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
