<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Server API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin: 0; background: #fafafa; }
        .swagger-ui .topbar { background-color: #2c3e50; }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 30px 20px; text-align: center; margin-bottom: 20px;
        }
        .custom-header h1 { margin: 0; font-size: 3em; font-weight: 300; }
        .custom-header p { margin: 15px 0 0 0; font-size: 1.3em; opacity: 0.9; }
        .api-info {
            background: white; padding: 20px; margin: 20px;
            border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-info h3 { color: #2c3e50; margin-top: 0; }
        .endpoint-count { display: flex; justify-content: space-around; flex-wrap: wrap; margin: 20px 0; }
        .endpoint-category {
            background: #ecf0f1; padding: 15px; border-radius: 6px;
            text-align: center; margin: 5px; min-width: 120px;
        }
        .endpoint-category .count { font-size: 2em; font-weight: bold; color: #3498db; }
        .endpoint-category .label { font-size: 0.9em; color: #7f8c8d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🎬 Video Server APIs</h1>
        <p>Go-based microservice framework for video processing, translation, and AI generation</p>
    </div>

    <div class="api-info">
        <h3>📊 API Overview</h3>
        <div class="endpoint-count">
            <div class="endpoint-category">
                <div class="count">13</div>
                <div class="label">Total Endpoints</div>
            </div>
            <div class="endpoint-category">
                <div class="count">8</div>
                <div class="label">Categories</div>
            </div>
            <div class="endpoint-category">
                <div class="count">3</div>
                <div class="label">Auth Methods</div>
            </div>
            <div class="endpoint-category">
                <div class="count">2</div>
                <div class="label">AI Providers</div>
            </div>
        </div>
        
        <h3>🚀 Quick Links</h3>
        <ul>
            <li><strong>Development Server:</strong> <code>http://localhost:8080</code></li>
            <li><strong>Main Documentation:</strong> <a href="../README.md">docs/README.md</a></li>
            <li><strong>OpenAPI Spec:</strong> <a href="./video-translation-api.yaml">video-translation-api.yaml</a></li>
        </ul>

        <h3>🔑 Authentication</h3>
        <p>All APIs support multiple authentication methods:</p>
        <ul>
            <li><strong>API Key:</strong> Add <code>X-API-Key</code> header</li>
            <li><strong>Bearer Token:</strong> Add <code>Authorization: Bearer &lt;token&gt;</code> header</li>
            <li><strong>Session:</strong> Use session cookies</li>
        </ul>

        <h3>📋 Endpoint Categories</h3>
        <ul>
            <li><strong>🎵 Text Processing:</strong> TTS conversion with multiple voices</li>
            <li><strong>🌐 Translation:</strong> Subtitle and text translation services</li>
            <li><strong>🎤 Speech Processing:</strong> Audio transcription and recognition</li>
            <li><strong>🎬 Video Processing:</strong> Complete video translation workflows</li>
            <li><strong>🤖 AI Generation:</strong> Image and video generation using AI models</li>
            <li><strong>📋 Task Management:</strong> Async task status tracking</li>
            <li><strong>📦 Batch Processing:</strong> Multiple file operations</li>
            <li><strong>📁 File Management:</strong> Upload, storage, and serving</li>
        </ul>

        <h3>🔄 Response Format</h3>
        <p>All responses follow the consistent pattern:</p>
        <ul>
            <li><strong>Success:</strong> <code>{"code": 0, "msg": "ok", "data": {...}}</code></li>
            <li><strong>Error:</strong> <code>{"code": 1, "msg": "error message"}</code></li>
            <li><strong>Processing:</strong> <code>{"code": -1, "msg": "processing", "data": {...}}</code></li>
        </ul>

        <h3>🤖 AI Features</h3>
        <ul>
            <li><strong>Image Generation:</strong> BytePlus ModelArk with seedream-3-0-t2i-250415</li>
            <li><strong>Video Generation:</strong> BytePlus (seedance models) and Minimax (hailuo, t2v, i2v)</li>
            <li><strong>Multi-format Support:</strong> Various resolutions and durations</li>
            <li><strong>Async Processing:</strong> Task-based workflow with status tracking</li>
        </ul>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: './video-translation-api.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset],
                plugins: [SwaggerUIBundle.plugins.DownloadUrl],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    if (!request.headers['X-API-Key'] && !request.headers['Authorization']) {
                        request.headers['X-API-Key'] = 'your-api-key-here';
                    }
                    return request;
                }
            });
        };
    </script>
</body>
</html>
