openapi: 3.0.3
info:
  title: Video Translation API
  description: |
    Comprehensive video translation and processing API service that provides:
    - Text-to-Speech (TTS) conversion
    - Speech recognition and transcription
    - Subtitle translation
    - Complete video translation workflows
    - Image and video generation
    - File upload and management
    - Batch processing capabilities
    
    All endpoints follow consistent response patterns:
    - Success: `{"code": 0, "msg": "ok", "data": {...}}`
    - Error: `{"code": 1, "msg": "error message"}`
    - In Progress: `{"code": -1, "msg": "processing", "data": {...}}`
  version: 1.0.0
  contact:
    name: Video Translation API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: Development server
  - url: https://api.example.com
    description: Production server

security:
  - ApiKeyAuth: []
  - BearerAuth: []
  - SessionAuth: []

paths:
  /tts:
    post:
      tags:
        - Text Processing
      summary: Text to Speech Conversion
      description: Convert text to speech audio using various TTS engines and voices
      operationId: textToSpeech
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - text
              properties:
                text:
                  type: string
                  description: Text to convert to speech
                  example: "Hello, this is a test message"
                voice:
                  type: string
                  description: Voice ID to use for TTS
                  example: "en-US-AriaNeural"
                rate:
                  type: number
                  description: Speech rate (0.5 to 2.0)
                  default: 1.0
                  minimum: 0.5
                  maximum: 2.0
                pitch:
                  type: number
                  description: Speech pitch (-50 to 50)
                  default: 0
                  minimum: -50
                  maximum: 50
                format:
                  type: string
                  enum: [mp3, wav, ogg]
                  default: mp3
                  description: Output audio format
      responses:
        '200':
          description: TTS conversion successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /translate_srt:
    post:
      tags:
        - Translation
      summary: Translate Subtitle Files
      description: Translate SRT subtitle files from source language to target language
      operationId: translateSubtitles
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - srt_content
                - target_language
              properties:
                srt_content:
                  type: string
                  description: SRT subtitle content to translate
                  example: |
                    1
                    00:00:01,000 --> 00:00:04,000
                    Hello world
                source_language:
                  type: string
                  description: Source language code (auto-detect if not provided)
                  example: "en"
                target_language:
                  type: string
                  description: Target language code
                  example: "zh"
                translator:
                  type: string
                  enum: [google, azure, openai, baidu]
                  default: google
                  description: Translation service to use
      responses:
        '200':
          description: Translation successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          translated_srt:
                            type: string
                            description: Translated SRT content
                          source_language:
                            type: string
                            description: Detected source language
                          target_language:
                            type: string
                            description: Target language used

  /recogn:
    post:
      tags:
        - Speech Processing
      summary: Speech Recognition
      description: Convert audio to text using speech recognition
      operationId: speechRecognition
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - audio_file
              properties:
                audio_file:
                  type: string
                  format: binary
                  description: Audio file to transcribe
                language:
                  type: string
                  description: Language code for recognition
                  example: "en-US"
                model:
                  type: string
                  enum: [whisper, azure, google]
                  default: whisper
                  description: Speech recognition model to use
      responses:
        '200':
          description: Recognition successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          text:
                            type: string
                            description: Transcribed text
                          language:
                            type: string
                            description: Detected language
                          confidence:
                            type: number
                            description: Recognition confidence score

  /trans_video:
    post:
      tags:
        - Video Processing
      summary: Complete Video Translation
      description: Perform complete video translation including speech recognition, translation, and TTS
      operationId: translateVideo
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - video_file
                - target_language
              properties:
                video_file:
                  type: string
                  format: binary
                  description: Video file to translate
                target_language:
                  type: string
                  description: Target language code
                  example: "zh"
                source_language:
                  type: string
                  description: Source language (auto-detect if not provided)
                voice:
                  type: string
                  description: Voice ID for TTS
                subtitle_style:
                  type: string
                  description: Subtitle styling options
                output_format:
                  type: string
                  enum: [mp4, avi, mov]
                  default: mp4
      responses:
        '200':
          description: Video translation started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '202':
          description: Video translation in progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InProgressResponse'

  /voice_list:
    get:
      tags:
        - Configuration
      summary: Get Available Voices
      description: Retrieve list of available TTS voices
      operationId: getVoiceList
      parameters:
        - name: language
          in: query
          description: Filter voices by language code
          schema:
            type: string
            example: "en-US"
        - name: gender
          in: query
          description: Filter voices by gender
          schema:
            type: string
            enum: [male, female, neutral]
      responses:
        '200':
          description: Voice list retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          voices:
                            type: array
                            items:
                              $ref: '#/components/schemas/Voice'

  /upload:
    post:
      tags:
        - File Management
      summary: Upload Files
      description: Upload video, audio, or subtitle files for processing
      operationId: uploadFile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: File to upload
                type:
                  type: string
                  enum: [video, audio, subtitle, image]
                  description: File type category
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          file_id:
                            type: string
                            description: Unique file identifier
                          file_path:
                            type: string
                            description: File storage path
                          file_size:
                            type: integer
                            description: File size in bytes
                          mime_type:
                            type: string
                            description: File MIME type

  /task_status:
    get:
      tags:
        - Task Management
      summary: Get Task Status
      description: Retrieve the status of a specific task
      operationId: getTaskStatus
      parameters:
        - name: task_id
          in: query
          required: true
          description: Task ID to check status for
          schema:
            type: string
            example: "550e8400-e29b-41d4-a716-446655440000"
      responses:
        '200':
          description: Task completed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/TaskStatus'
        '202':
          description: Task in progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InProgressResponse'
        '404':
          description: Task not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /batch_process:
    post:
      tags:
        - Batch Processing
      summary: Batch Process Multiple Files
      description: Process multiple files in a single batch operation
      operationId: batchProcess
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - files
                - operation
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Multiple files to process
                operation:
                  type: string
                  enum: [translate, transcribe, tts, convert]
                  description: Batch operation type
                target_language:
                  type: string
                  description: Target language for translation operations
                voice:
                  type: string
                  description: Voice for TTS operations
                options:
                  type: object
                  description: Additional processing options
      responses:
        '200':
          description: Batch processing started
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          batch_id:
                            type: string
                            description: Batch processing ID
                          task_ids:
                            type: array
                            items:
                              type: string
                            description: Individual task IDs

  /generate_image:
    post:
      tags:
        - AI Generation
      summary: Generate Images
      description: Generate images using AI models (BytePlus ModelArk)
      operationId: generateImage
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - prompt
              properties:
                prompt:
                  type: string
                  description: Text prompt for image generation
                  example: "A beautiful sunset over mountains"
                size:
                  type: string
                  enum: ["1024x1024", "1024x1792", "1792x1024"]
                  default: "1024x1024"
                  description: Image dimensions
                model:
                  type: string
                  default: "seedream-3-0-t2i-250415"
                  description: AI model to use for generation
                guidance_scale:
                  type: number
                  minimum: 1
                  maximum: 20
                  default: 7.5
                  description: How closely to follow the prompt
      responses:
        '200':
          description: Image generated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          image_url:
                            type: string
                            description: Generated image URL
                          prompt:
                            type: string
                            description: Original prompt used
                          size:
                            type: string
                            description: Image dimensions

  /generate_video:
    post:
      tags:
        - AI Generation
      summary: Generate Videos
      description: Generate videos using AI models (BytePlus, Minimax)
      operationId: generateVideo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                prompt:
                  type: string
                  description: Text prompt for video generation
                  example: "A cat playing in a garden"
                image_path:
                  type: string
                  description: Path to image for image-to-video generation
                model:
                  type: string
                  description: AI model to use (auto-detected from name)
                  example: "seedance-1-0-t2v"
                duration:
                  type: number
                  minimum: 1
                  maximum: 30
                  default: 5
                  description: Video duration in seconds
                resolution:
                  type: string
                  enum: ["720p", "1080p", "4K"]
                  default: "1080p"
                  description: Video resolution
                prompt_optimizer:
                  type: boolean
                  default: true
                  description: Enable prompt optimization (Minimax only)
                async_mode:
                  type: boolean
                  default: true
                  description: Process asynchronously
      responses:
        '200':
          description: Video generation started/completed
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          task_id:
                            type: string
                            description: Task ID for async processing
                          video_path:
                            type: string
                            description: Video path for sync processing
                          status:
                            type: string
                            enum: [processing, completed]

  /video_status:
    get:
      tags:
        - AI Generation
      summary: Get Video Generation Status
      description: Check the status of video generation tasks
      operationId: getVideoStatus
      parameters:
        - name: task_id
          in: query
          required: true
          description: Video generation task ID
          schema:
            type: string
            example: "550e8400-e29b-41d4-a716-446655440000"
      responses:
        '200':
          description: Video generation completed
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/VideoStatus'
        '202':
          description: Video generation in progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InProgressResponse'

  /task_status_list:
    get:
      tags:
        - Task Management
      summary: Get Multiple Task Status
      description: Retrieve status for multiple tasks at once
      operationId: getMultipleTaskStatus
      parameters:
        - name: task_id_list
          in: query
          required: true
          description: Comma-separated list of task IDs
          schema:
            type: string
            example: "task1,task2,task3"
      responses:
        '200':
          description: Task statuses retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          tasks:
                            type: object
                            additionalProperties:
                              $ref: '#/components/schemas/TaskStatusInfo'
    post:
      tags:
        - Task Management
      summary: Get Multiple Task Status (POST)
      description: Retrieve status for multiple tasks using POST method
      operationId: getMultipleTaskStatusPost
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - task_id_list
              properties:
                task_id_list:
                  type: array
                  items:
                    type: string
                  example: ["task1", "task2", "task3"]
      responses:
        '200':
          description: Task statuses retrieved
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          tasks:
                            type: object
                            additionalProperties:
                              $ref: '#/components/schemas/TaskStatusInfo'

  /file/{filepath}:
    get:
      tags:
        - File Management
      summary: Serve Files
      description: Serve uploaded or generated files
      operationId: serveFile
      parameters:
        - name: filepath
          in: path
          required: true
          description: File path to serve
          schema:
            type: string
            example: "uploads/video.mp4"
      responses:
        '200':
          description: File served successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    SessionAuth:
      type: apiKey
      in: cookie
      name: session_id

  schemas:
    SuccessResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
          description: Success code (always 0)
        msg:
          type: string
          example: "ok"
          description: Success message
        data:
          type: object
          description: Response data

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          example: 1
          description: Error code (always 1)
        msg:
          type: string
          example: "Invalid request parameters"
          description: Error message

    TaskResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        msg:
          type: string
          example: "ok"
        task_id:
          type: string
          example: "550e8400-e29b-41d4-a716-446655440000"
          description: Unique task identifier

    InProgressResponse:
      type: object
      properties:
        code:
          type: integer
          example: -1
          description: In progress code (always -1)
        msg:
          type: string
          example: "processing"
          description: Progress message
        data:
          type: object
          properties:
            progress:
              type: integer
              minimum: 0
              maximum: 100
              description: Progress percentage

    Voice:
      type: object
      properties:
        id:
          type: string
          example: "en-US-AriaNeural"
        name:
          type: string
          example: "Aria (English US)"
        language:
          type: string
          example: "en-US"
        gender:
          type: string
          enum: [male, female, neutral]
        provider:
          type: string
          example: "azure"

    TaskStatus:
      type: object
      properties:
        task_id:
          type: string
          example: "550e8400-e29b-41d4-a716-446655440000"
        status:
          type: string
          enum: [pending, processing, completed, failed]
          example: "completed"
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 100
        message:
          type: string
          example: "Task completed successfully"
        result:
          type: object
          description: Task result data (varies by task type)
        created_at:
          type: string
          format: date-time
          example: "2024-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-01T12:05:00Z"

    TaskStatusInfo:
      type: object
      properties:
        task_id:
          type: string
          example: "550e8400-e29b-41d4-a716-446655440000"
        type:
          type: string
          enum: [tts, translation, recognition, video_translation, video_generation, image_generation]
          example: "video_generation"
        status:
          type: string
          enum: [not_found, initialized, running, completed, failed, error]
          example: "completed"
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 100
        message:
          type: string
          example: "Video generation completed successfully"
        data:
          type: object
          description: Task-specific data
        error:
          type: string
          description: Error message if task failed

    VideoStatus:
      type: object
      properties:
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 100
        status:
          type: string
          enum: [initialized, running, completed, failed]
          example: "completed"
        video_path:
          type: string
          example: "/tmp/video-generation/task123/output.mp4"
          description: Local path to generated video
        video_url:
          type: string
          example: "http://localhost:8080/apidata/task123/output.mp4"
          description: Accessible URL for the generated video

tags:
  - name: Text Processing
    description: Text-to-speech and text processing operations
  - name: Translation
    description: Language translation services
  - name: Speech Processing
    description: Speech recognition and audio processing
  - name: Video Processing
    description: Video translation and processing workflows
  - name: AI Generation
    description: AI-powered image and video generation
  - name: Task Management
    description: Task status and progress tracking
  - name: Batch Processing
    description: Batch operations for multiple files
  - name: File Management
    description: File upload, storage, and serving
  - name: Configuration
    description: System configuration and settings

externalDocs:
  description: Find more info about Video Translation API
  url: https://docs.example.com/video-translation-api
