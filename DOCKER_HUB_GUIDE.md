# Docker Hub Build and Push Guide

This guide explains how to build your PyVideoTrans Docker image and push it to Docker Hub.

## Prerequisites

1. **Docker Hub Account**: Create account at [hub.docker.com](https://hub.docker.com)
2. **Docker Desktop**: Installed and running
3. **Docker CLI**: Logged in to Docker Hub

## Step 1: Docker Hub Setup

### Create Docker Hub Account
1. Go to [hub.docker.com](https://hub.docker.com)
2. Sign up for a free account
3. Verify your email address

### Login to Docker Hub
```bash
# Login to Docker Hub
docker login

# Enter your Docker Hub username and password
# Username: your-dockerhub-username
# Password: your-dockerhub-password
```

## Step 2: Build the Image

### Basic Build
```bash
# Build the image with a tag
docker build -t your-dockerhub-username/pyvideotrans:latest .

# Example:
docker build -t johndoe/pyvideotrans:latest .
```

### Build with Version Tag
```bash
# Build with specific version
docker build -t your-dockerhub-username/pyvideotrans:v1.0.0 .
docker build -t your-dockerhub-username/pyvideotrans:latest .

# Example:
docker build -t johndoe/pyvideotrans:v1.0.0 .
docker build -t johndoe/pyvideotrans:latest .
```

### Multi-Platform Build (Optional)
```bash
# Create and use buildx builder
docker buildx create --name multiplatform --use
docker buildx inspect --bootstrap

# Build for multiple platforms
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t your-dockerhub-username/pyvideotrans:latest \
  --push .
```

## Step 3: Test the Image Locally

```bash
# Test the built image
docker run -d \
  --name pyvideotrans-test \
  -p 9011:9011 \
  your-dockerhub-username/pyvideotrans:latest

# Check if it's running
docker ps

# Check logs
docker logs pyvideotrans-test

# Stop and remove test container
docker stop pyvideotrans-test
docker rm pyvideotrans-test
```

## Step 4: Push to Docker Hub

### Push Single Tag
```bash
# Push the image
docker push your-dockerhub-username/pyvideotrans:latest

# Example:
docker push johndoe/pyvideotrans:latest
```

### Push Multiple Tags
```bash
# Push specific version
docker push your-dockerhub-username/pyvideotrans:v1.0.0

# Push latest
docker push your-dockerhub-username/pyvideotrans:latest
```

### Push All Tags
```bash
# Push all tags for the repository
docker push --all-tags your-dockerhub-username/pyvideotrans
```

## Step 5: Verify on Docker Hub

1. Go to [hub.docker.com](https://hub.docker.com)
2. Navigate to your repositories
3. Click on your `pyvideotrans` repository
4. Verify the image is uploaded with correct tags

## Automated Build Script

I'll create a build script to automate this process:

### Usage Examples

```bash
# Build and push latest
./build-and-push.sh

# Build and push with version
./build-and-push.sh v1.0.0

# Build for multiple platforms
./build-and-push.sh v1.0.0 --multiplatform
```

## Best Practices

### 1. Image Naming Convention
```bash
# Format: username/repository:tag
your-dockerhub-username/pyvideotrans:latest
your-dockerhub-username/pyvideotrans:v1.0.0
your-dockerhub-username/pyvideotrans:stable
```

### 2. Versioning Strategy
- `latest`: Most recent stable version
- `v1.0.0`: Specific version numbers
- `stable`: Stable release branch
- `dev`: Development version

### 3. Repository Description
Add a good description to your Docker Hub repository:
```
PyVideoTrans - AI-powered video translation and processing API
Features: TTS, speech recognition, subtitle translation, video processing
```

### 4. README on Docker Hub
Docker Hub will automatically display your repository README.md

## Using the Published Image

Once pushed, others can use your image:

```bash
# Pull and run your image
docker pull your-dockerhub-username/pyvideotrans:latest
docker run -d -p 9011:9011 your-dockerhub-username/pyvideotrans:latest

# Or use in docker-compose.yml
services:
  pyvideotrans:
    image: your-dockerhub-username/pyvideotrans:latest
    ports:
      - "9011:9011"
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```bash
   # Re-login to Docker Hub
   docker logout
   docker login
   ```

2. **Image Too Large**
   ```bash
   # Check image size
   docker images your-dockerhub-username/pyvideotrans
   
   # Optimize Dockerfile if needed
   # Use .dockerignore to exclude large files
   ```

3. **Build Fails**
   ```bash
   # Build with verbose output
   docker build --progress=plain -t your-dockerhub-username/pyvideotrans:latest .
   
   # Check build context size
   docker build --no-cache -t your-dockerhub-username/pyvideotrans:latest .
   ```

4. **Push Fails**
   ```bash
   # Check if logged in
   docker info | grep Username
   
   # Check repository exists on Docker Hub
   # Make sure repository name matches exactly
   ```

## Security Considerations

1. **Don't include secrets in image**
   - Use environment variables
   - Mount secrets as files
   - Use Docker secrets in production

2. **Use specific tags in production**
   ```bash
   # Good: specific version
   docker pull johndoe/pyvideotrans:v1.0.0
   
   # Avoid: latest in production
   docker pull johndoe/pyvideotrans:latest
   ```

3. **Scan images for vulnerabilities**
   ```bash
   # Docker Hub provides vulnerability scanning
   # Enable it in your repository settings
   ```

## Next Steps

1. Set up automated builds with GitHub Actions
2. Configure vulnerability scanning
3. Add image signing for security
4. Set up multi-stage builds for smaller images
5. Consider using Docker Hub's automated builds
