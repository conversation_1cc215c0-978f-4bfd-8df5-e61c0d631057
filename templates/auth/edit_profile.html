{% extends "base.html" %}

{% block title %}Edit Profile - PyVideoTrans{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-edit"></i> Edit Profile</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.first_name.label(class="form-label") }}
                        {{ form.first_name(class="form-control") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.last_name.label(class="form-label") }}
                        {{ form.last_name(class="form-control") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">Cancel</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
