{% extends "base.html" %}

{% block title %}Login - PyVideoTrans{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4><i class="fas fa-sign-in-alt"></i> Login</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                        {% if form.username.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
                
                <hr>
                
                <div class="d-grid">
                    <a href="{{ url_for('auth.google_login') }}" class="btn btn-danger google-btn">
                        <i class="fab fa-google"></i> Login with Google
                    </a>
                </div>
                
                <div class="text-center mt-3">
                    <p>Don't have an account? <a href="{{ url_for('auth.register') }}">Register here</a></p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> API Access</h5>
            </div>
            <div class="card-body">
                <p class="card-text">For API access, you can:</p>
                <ul>
                    <li>Use your API key in the <code>X-API-KEY</code> header</li>
                    <li>Use Bearer token authentication with JWT tokens</li>
                    <li>Login via JSON API at <code>POST /auth/login</code></li>
                </ul>
                <p class="text-muted small">
                    <strong>Example:</strong><br>
                    <code>curl -H "X-API-KEY: your-api-key" http://localhost:9011/api/tasks</code>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
