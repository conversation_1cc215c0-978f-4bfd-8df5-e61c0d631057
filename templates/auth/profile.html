{% extends "base.html" %}

{% block title %}Profile - PyVideoTrans{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-user"></i> User Profile</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        {% if user.profile_picture %}
                            <img src="{{ user.profile_picture }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                        {% else %}
                            <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Username:</th>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>
                                    {{ user.email }}
                                    {% if user.email_verified %}
                                        <span class="badge bg-success ms-2">Verified</span>
                                    {% else %}
                                        <span class="badge bg-warning ms-2">Unverified</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Name:</th>
                                <td>{{ (user.first_name + ' ' + user.last_name).strip() or 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <th>Account Type:</th>
                                <td>
                                    {% if user.google_id %}
                                        <span class="badge bg-danger">Google Account</span>
                                    {% endif %}
                                    {% if user.password_hash %}
                                        <span class="badge bg-primary">Local Account</span>
                                    {% endif %}
                                    {% if user.is_admin %}
                                        <span class="badge bg-warning">Admin</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Member Since:</th>
                                <td>{{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown' }}</td>
                            </tr>
                            <tr>
                                <th>Last Login:</th>
                                <td>{{ user.last_login_at.strftime('%B %d, %Y at %I:%M %p') if user.last_login_at else 'Never' }}</td>
                            </tr>
                        </table>
                        
                        <div class="mt-3">
                            <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Profile
                            </a>
                            {% if user.password_hash %}
                                <a href="{{ url_for('auth.change_password') }}" class="btn btn-secondary">
                                    <i class="fas fa-key"></i> Change Password
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-key"></i> API Access</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Your API key for programmatic access:</p>
                <div class="input-group mb-3">
                    <input type="password" class="form-control" id="apiKey" value="{{ user.api_key }}" readonly>
                    <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKey()">
                        <i class="fas fa-eye" id="toggleIcon"></i>
                    </button>
                </div>
                
                <form method="POST" action="{{ url_for('auth.regenerate_api_key') }}" class="d-inline">
                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Are you sure? This will invalidate your current API key.')">
                        <i class="fas fa-sync"></i> Regenerate
                    </button>
                </form>
                
                <hr>
                
                <h6>Usage Examples:</h6>
                <p class="small text-muted">
                    <strong>Header Authentication:</strong><br>
                    <code>X-API-KEY: {{ user.api_key[:8] }}...</code>
                </p>
                <p class="small text-muted">
                    <strong>Bearer Token:</strong><br>
                    Login via API to get JWT token
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Account Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ user.tasks|length if user.tasks else 0 }}</h4>
                        <small class="text-muted">Tasks</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ user.media|length if user.media else 0 }}</h4>
                        <small class="text-muted">Media Files</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleApiKey() {
    const apiKeyInput = document.getElementById('apiKey');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        apiKeyInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}
</script>
{% endblock %}
