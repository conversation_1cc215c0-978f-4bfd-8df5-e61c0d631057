# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files and directories
tmp/
apidata/
logs/
*.log
*.tmp
*.temp

# Cache directories
.cache/
.pytest_cache/
.mypy_cache/

# Documentation
docs/
*.md
README*
CHANGELOG*
LICENSE*

# Configuration examples
*.example
.env.example
params.json.example

# Test files
test_*.py
*_test.py
tests/
run-test.bat

# Batch files (Windows specific)
*.bat

# Backup files
*.backup
*~
*.bak

# Database files (should be external)
*.db
*.sqlite
*.sqlite3

# Model files (should be mounted or downloaded)
models/
*.model
*.bin
*.pt
*.pth

# Large data directories
.taskmaster/
.roo/

# Development files
debug_*.py
*_debug.py
setup_*.py
migrate_*.py
manage_*.py
start_*.py

# Upload examples
upload_example.py
url_upload_example.py
voice_list_example.py

# JSON test files
test.json
*.json.example

# Version files
version.json

# Audio/Video cache
*.wav
*.mp3
*.mp4
*.avi
*.mov
*.srt
