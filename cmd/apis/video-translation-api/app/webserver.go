package app

import (
	"net/http"

	"github.com/justinas/alice"
	"github.com/nhdms/base-go/cmd/apis/video-translation-api/handlers"
	"github.com/nhdms/base-go/pkg/app"
	"github.com/nhdms/base-go/pkg/micro/web"
	middleware "github.com/nhdms/base-go/pkg/middlewares"
	"github.com/spf13/viper"
	"go-micro.dev/v5/client"
)

type Server struct {
	Name     string
	client   client.Client
	producer app.PublisherInterface
}

func NewServer(publisher app.PublisherInterface) *Server {
	return &Server{
		Name:     "video-translation",
		producer: publisher,
	}
}

func (s *Server) SetGRPCClient(client client.Client) {
	s.client = client
}

func (s *Server) GetBasePath() string {
	return "/video-translation"
}

func (s *Server) GetName() string {
	return s.Name
}

func (s *Server) GetRoutes() web.Routes {
	mdws := []alice.Constructor{}

	if viper.GetBool("logging.enable") {
		mdws = append(mdws, middleware.LoggingMiddleware)
	}

	// Authentication middleware for protected endpoints
	// authMdws := append(mdws, middleware.AuthMiddleware) // TODO: Fix AuthMiddleware import

	return []web.Route{
		// TTS - Text to Speech endpoint (第1个接口)
		{
			Name:    "TTS - Generate Voice from Subtitle",
			Method:  http.MethodPost,
			Pattern: "/tts",
			Handler: &handlers.TTSHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 300000, // 5 minutes for TTS processing
		},
		// Subtitle Translation endpoint (第2个接口)
		{
			Name:    "Translate Subtitle",
			Method:  http.MethodPost,
			Pattern: "/translate_srt",
			Handler: &handlers.TranslateSubtitleHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 180000, // 3 minutes for translation
		},
		// Speech Recognition endpoint (第3个接口)
		{
			Name:    "Speech Recognition - Audio/Video to Subtitle",
			Method:  http.MethodPost,
			Pattern: "/recogn",
			Handler: &handlers.SpeechRecognitionHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 600000, // 10 minutes for recognition
		},
		// Complete Video Translation endpoint (第4个接口)
		{
			Name:    "Complete Video Translation",
			Method:  http.MethodPost,
			Pattern: "/trans_video",
			Handler: &handlers.VideoTranslationHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 1800000, // 30 minutes for complete video processing
		},
		// Voice List endpoint (第5个接口)
		{
			Name:    "Get Voice List",
			Method:  http.MethodGet,
			Pattern: "/voice_list",
			Handler: &handlers.VoiceListHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws, // No auth required for voice list
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 30000, // 30 seconds
		},
		// File Upload endpoint (第6个接口)
		{
			Name:    "File Upload",
			Method:  http.MethodPost,
			Pattern: "/upload",
			Handler: &handlers.FileUploadHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 300000, // 5 minutes for file upload
		},
		// Task Status endpoint
		{
			Name:    "Get Task Status",
			Method:  http.MethodPost,
			Pattern: "/task_status",
			Handler: &handlers.TaskStatusHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		// GET version of Task Status
		{
			Name:    "Get Task Status (GET)",
			Method:  http.MethodGet,
			Pattern: "/task_status",
			Handler: &handlers.TaskStatusHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		// Batch Processing endpoint (新增)
		{
			Name:    "Batch Video Processing",
			Method:  http.MethodPost,
			Pattern: "/batch_process",
			Handler: &handlers.BatchProcessHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 1800000, // 30 minutes for batch processing
		},
		// File serving endpoint
		{
			Name:    "Serve Files",
			Method:  http.MethodGet,
			Pattern: "/file/{filepath:.*}",
			Handler: &handlers.FileServerHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws, // No auth for file serving
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 60000, // 1 minute for file serving
		},
		// Generate Image endpoint (第8个接口)
		{
			Name:    "Generate Image",
			Method:  http.MethodPost,
			Pattern: "/generate_image",
			Handler: &handlers.GenerateImageHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 120000, // 2 minutes for image generation
		},
		// Generate Video endpoint (第9个接口)
		{
			Name:    "Generate Video",
			Method:  http.MethodPost,
			Pattern: "/generate_video",
			Handler: &handlers.GenerateVideoHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 1800000, // 30 minutes for video generation
		},
		// Video Status endpoint
		{
			Name:    "Get Video Generation Status",
			Method:  http.MethodGet,
			Pattern: "/video_status",
			Handler: &handlers.VideoStatusHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		// POST version of Video Status
		{
			Name:    "Get Video Generation Status (POST)",
			Method:  http.MethodPost,
			Pattern: "/video_status",
			Handler: &handlers.VideoStatusHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 10000, // 10 seconds
		},
		// Task Status List endpoint
		{
			Name:    "Get Multiple Task Status",
			Method:  http.MethodPost,
			Pattern: "/task_status_list",
			Handler: &handlers.TaskStatusListHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 30000, // 30 seconds for multiple task status
		},
		// GET version of Task Status List
		{
			Name:    "Get Multiple Task Status (GET)",
			Method:  http.MethodGet,
			Pattern: "/task_status_list",
			Handler: &handlers.TaskStatusListHandler{
				BaseHandler: handlers.BaseHandler{
					Producer: s.producer,
					Client:   s.client,
				},
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: true,
			},
			Timeout: 30000, // 30 seconds for multiple task status
		},
		// Health check endpoint
		{
			Name:        "Health Check",
			Method:      http.MethodGet,
			Pattern:     "/health",
			Handler:     &handlers.HealthHandler{},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 5000, // 5 seconds
		},
	}
}
