package handlers

import (
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// SpeechRecognitionHandler - Speech recognition handler (第3个接口)
type SpeechRecognitionHandler struct {
	BaseHandler
}

// SpeechRecognitionRequest - Speech recognition request structure
type SpeechRecognitionRequest struct {
	AudioFile      string `json:"audio_file"`      // 必须参数，音频/视频文件路径
	RecognType     int    `json:"recogn_type"`     // 必须参数，识别类型 0-6
	ModelName      string `json:"model_name"`      // 可选参数，模型名称
	Language       string `json:"language"`        // 可选参数，语言代码
	SplitType      string `json:"split_type"`      // 可选参数，分割类型 all/avg
	IsCuda         bool   `json:"is_cuda"`         // 可选参数，是否使用CUDA
	OutputFormat   string `json:"output_format"`   // 可选参数，输出格式 srt/vtt/txt
	OutputDir      string `json:"output_dir"`      // 可选参数，输出目录
	MaxDuration    int    `json:"max_duration"`    // 可选参数，最大处理时长（秒）
	NoiseReduction bool   `json:"noise_reduction"` // 可选参数，是否降噪
}

// SpeechRecognitionResponse - Speech recognition response
type SpeechRecognitionResponse struct {
	SubtitleFile   string                 `json:"subtitle_file,omitempty"`   // 生成的字幕文件路径
	TextFile       string                 `json:"text_file,omitempty"`       // 生成的文本文件路径
	Duration       int                    `json:"duration,omitempty"`        // 音频时长（秒）
	SegmentCount   int                    `json:"segment_count,omitempty"`   // 识别片段数量
	Confidence     float64                `json:"confidence,omitempty"`      // 平均置信度
	ProcessingTime int                    `json:"processing_time,omitempty"` // 处理时间（秒）
	Stats          map[string]interface{} `json:"stats,omitempty"`           // 识别统计信息
}

func (h *SpeechRecognitionHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "SpeechRecognitionHandler")
	defer func() {
		LogResponse(r, "SpeechRecognitionHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req SpeechRecognitionRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate required parameters
	if strings.TrimSpace(req.AudioFile) == "" {
		RespondError(w, "The parameter audio_file is required")
		return
	}

	// Validate recognition type
	if !ValidateRecognitionType(req.RecognType) {
		RespondError(w, "Invalid recogn_type parameter")
		return
	}

	// Check if audio/video file exists
	if !fileExists(req.AudioFile) {
		RespondError(w, "Audio/video file does not exist")
		return
	}

	// Validate file format
	if !IsAudioFile(req.AudioFile) && !IsVideoFile(req.AudioFile) {
		RespondError(w, "Invalid file format. Supported formats: audio (.mp3, .wav, .flac, .aac, .ogg, .wma, .m4a, .opus) or video (.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v)")
		return
	}

	// Validate language if provided
	if req.Language != "" && !ValidateLanguageCode(req.Language) {
		RespondError(w, "Invalid language code")
		return
	}

	// Set default values
	if req.SplitType == "" {
		req.SplitType = "all"
	}
	if req.OutputFormat == "" {
		req.OutputFormat = "srt"
	}
	if req.ModelName == "" {
		req.ModelName = getDefaultModelName(req.RecognType)
	}

	// Validate split type
	if req.SplitType != "all" && req.SplitType != "avg" {
		RespondError(w, "Invalid split_type. Must be 'all' or 'avg'")
		return
	}

	// Validate output format
	if !isValidOutputFormat(req.OutputFormat) {
		RespondError(w, "Invalid output_format. Must be 'srt', 'vtt', or 'txt'")
		return
	}

	// Check recognition service availability
	if !isRecognitionServiceAvailable(req.RecognType, req.Language) {
		RespondServiceError(w, "Speech recognition service is not available for the specified language")
		return
	}

	// Validate model name for specific recognition types
	if !isValidModelName(req.RecognType, req.ModelName) {
		RespondError(w, fmt.Sprintf("Invalid model_name '%s' for recogn_type %d", req.ModelName, req.RecognType))
		return
	}

	// Check file size and duration limits
	if err := validateAudioFile(req.AudioFile, req.MaxDuration); err != nil {
		RespondError(w, err.Error())
		return
	}

	// Generate task ID
	taskID := GenerateTaskID()

	// Create recognition task
	task := map[string]interface{}{
		"task_id":         taskID,
		"user_id":         userID,
		"task_type":       "speech_recognition",
		"audio_file":      req.AudioFile,
		"recogn_type":     req.RecognType,
		"model_name":      req.ModelName,
		"language":        req.Language,
		"split_type":      req.SplitType,
		"is_cuda":         req.IsCuda,
		"output_format":   req.OutputFormat,
		"output_dir":      req.OutputDir,
		"max_duration":    req.MaxDuration,
		"noise_reduction": req.NoiseReduction,
		"status":          "queued",
		"created_at":      time.Now().Unix(),
	}

	// Publish task to queue
	err := h.publishRecognitionTask(task)
	if err != nil {
		RespondTaskError(w, fmt.Sprintf("Failed to queue recognition task: %s", err.Error()))
		return
	}

	// Return task ID for status tracking
	RespondTaskSuccess(w, taskID)
}

// Helper functions

// getDefaultModelName moved to common.go

func isValidOutputFormat(format string) bool {
	validFormats := map[string]bool{
		"srt": true,
		"vtt": true,
		"txt": true,
		"ass": true,
		"ssa": true,
	}
	return validFormats[format]
}

// isRecognitionServiceAvailable moved to common.go

func isValidModelName(recognType int, modelName string) bool {
	// Validate model names for specific recognition types
	validModels := map[int]map[string]bool{
		0: {"tiny": true, "base": true, "small": true, "medium": true, "large": true}, // faster-whisper
		1: {"tiny": true, "base": true, "small": true, "medium": true, "large": true}, // openai-whisper
		6: {"whisper-1": true},                                                        // OpenAI API
	}

	if models, exists := validModels[recognType]; exists {
		return models[modelName]
	}

	return true // No validation for other types
}

func validateAudioFile(filePath string, maxDuration int) error {
	// Get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Check file size (limit to 500MB)
	maxSize := int64(500 * 1024 * 1024) // 500MB
	if fileInfo.Size() > maxSize {
		return fmt.Errorf("file size too large. Maximum allowed: 500MB")
	}

	// TODO: Check audio duration if maxDuration is specified
	// This would require audio/video analysis libraries

	return nil
}

func (h *SpeechRecognitionHandler) publishRecognitionTask(task map[string]interface{}) error {
	// Simulate publishing task to message queue
	taskID := task["task_id"].(string)
	userID := task["user_id"].(string)
	audioFile := task["audio_file"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the task (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("Recognition task created",
		"task_id", taskID,
		"user_id", userID,
		"task_type", "speech_recognition",
		"file", audioFile,
		"timestamp", time.Now().Unix(),
	)

	return nil
}

// GetSupportedRecognitionServices - Get list of available recognition services
func GetSupportedRecognitionServices() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":          0,
			"name":        "Faster-Whisper",
			"description": "Local Faster-Whisper model",
			"models":      []string{"tiny", "base", "small", "medium", "large"},
			"languages":   []string{"zh-cn", "en", "fr", "de", "ja", "ko", "ru", "es"},
		},
		{
			"id":          1,
			"name":        "OpenAI-Whisper",
			"description": "Local OpenAI-Whisper model",
			"models":      []string{"tiny", "base", "small", "medium", "large"},
			"languages":   []string{"zh-cn", "en", "fr", "de", "ja", "ko", "ru", "es"},
		},
		{
			"id":          2,
			"name":        "Google Speech",
			"description": "Google Speech Recognition API",
			"models":      []string{"default"},
			"languages":   []string{"zh-cn", "en", "fr", "de", "ja", "ko"},
		},
		{
			"id":          6,
			"name":        "OpenAI API",
			"description": "OpenAI Whisper API",
			"models":      []string{"whisper-1"},
			"languages":   []string{"zh-cn", "en", "fr", "de", "ja", "ko", "ru", "es"},
		},
	}
}
