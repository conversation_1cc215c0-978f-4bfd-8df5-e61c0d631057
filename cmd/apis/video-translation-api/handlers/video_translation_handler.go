package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// VideoTranslationHandler - Complete video translation handler (第4个接口)
type VideoTranslationHandler struct {
	BaseHandler
}

// VideoTranslationRequest - Complete video translation request structure
type VideoTranslationRequest struct {
	// File parameters
	VideoFile string `json:"video_file"` // 必须参数，视频文件路径

	// Recognition parameters
	RecognType int    `json:"recogn_type"` // 必须参数，语音识别模式 0-6
	ModelName  string `json:"model_name"`  // 可选参数，识别模型名称
	SplitType  string `json:"split_type"`  // 可选参数，分割类型 all/avg
	IsCuda     bool   `json:"is_cuda"`     // 可选参数，是否启用CUDA加速

	// Translation parameters
	TranslateType  int    `json:"translate_type"`  // 必须参数，翻译渠道
	SourceLanguage string `json:"source_language"` // 可选参数，原始语言代码
	TargetLanguage string `json:"target_language"` // 必须参数，目标语言代码

	// TTS parameters
	TTSType       int    `json:"tts_type"`       // 必须参数，配音渠道 0-17
	VoiceRole     string `json:"voice_role"`     // 必须参数，配音角色名
	VoiceRate     string `json:"voice_rate"`     // 可选参数，语速调节
	Volume        string `json:"volume"`         // 可选参数，音量调节
	Pitch         string `json:"pitch"`          // 可选参数，音调调节
	VoiceAutorate bool   `json:"voice_autorate"` // 可选参数，自动语速
	VideoAutorate bool   `json:"video_autorate"` // 可选参数，视频自动调速

	// Subtitle parameters
	SubtitleType int    `json:"subtitle_type"` // 可选参数，字幕嵌入类型 0-4
	AppendVideo  bool   `json:"append_video"`  // 可选参数，是否延长视频
	OnlyVideo    bool   `json:"only_video"`    // 可选参数，是否只生成视频
	IsSeparate   bool   `json:"is_separate"`   // 可选参数，是否分离音轨
	BackAudio    string `json:"back_audio"`    // 可选参数，背景音频

	// Output parameters
	OutputDir  string `json:"output_dir"`  // 可选参数，输出目录
	OutputName string `json:"output_name"` // 可选参数，输出文件名
}

// VideoTranslationResponse - Video translation response
type VideoTranslationResponse struct {
	VideoFile      string                 `json:"video_file,omitempty"`      // 输出视频文件路径
	SubtitleFile   string                 `json:"subtitle_file,omitempty"`   // 字幕文件路径
	AudioFile      string                 `json:"audio_file,omitempty"`      // 音频文件路径
	Duration       int                    `json:"duration,omitempty"`        // 视频时长（秒）
	ProcessingTime int                    `json:"processing_time,omitempty"` // 处理时间（秒）
	Stats          map[string]interface{} `json:"stats,omitempty"`           // 处理统计信息
}

func (h *VideoTranslationHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "VideoTranslationHandler")
	defer func() {
		LogResponse(r, "VideoTranslationHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req VideoTranslationRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate required parameters
	if strings.TrimSpace(req.VideoFile) == "" {
		RespondError(w, "The parameter video_file is required")
		return
	}

	if strings.TrimSpace(req.TargetLanguage) == "" {
		RespondError(w, "The parameter target_language is required")
	}

	if strings.TrimSpace(req.VoiceRole) == "" {
		RespondError(w, "The parameter voice_role is required")
		return
	}

	// Check if video file exists
	if !fileExists(req.VideoFile) {
		RespondError(w, "Video file does not exist")
		return
	}

	// Validate video file format
	if !IsVideoFile(req.VideoFile) {
		RespondError(w, "Invalid video file format")
		return
	}

	// Validate parameters
	if !ValidateRecognitionType(req.RecognType) {
		RespondError(w, "Invalid recogn_type parameter")
		return
	}

	if !ValidateTranslationType(req.TranslateType) {
		RespondError(w, "Invalid translate_type parameter")
		return
	}

	if !ValidateTTSType(req.TTSType) {
		RespondError(w, "Invalid tts_type parameter")
		return
	}

	if !ValidateLanguageCode(req.TargetLanguage) {
		RespondError(w, "Invalid target_language code")
		return
	}

	if req.SourceLanguage != "" && !ValidateLanguageCode(req.SourceLanguage) {
		RespondError(w, "Invalid source_language code")
		return
	}

	// Set default values
	if req.SplitType == "" {
		req.SplitType = "all"
	}
	if req.ModelName == "" {
		req.ModelName = getDefaultModelName(req.RecognType)
	}
	if req.VoiceRate == "" {
		req.VoiceRate = "+0%"
	}
	if req.Volume == "" {
		req.Volume = "+0%"
	}
	if req.Pitch == "" {
		req.Pitch = "+0Hz"
	}

	// Validate format parameters
	if !isValidRateFormat(req.VoiceRate) {
		RespondError(w, "Invalid voice_rate format")
		return
	}
	if !isValidVolumeFormat(req.Volume) {
		RespondError(w, "Invalid volume format")
		return
	}
	if !isValidPitchFormat(req.Pitch) {
		RespondError(w, "Invalid pitch format")
		return
	}

	// Validate subtitle type
	if req.SubtitleType < 0 || req.SubtitleType > 4 {
		RespondError(w, "Invalid subtitle_type. Must be 0-4")
		return
	}

	// Check service availability
	if !isRecognitionServiceAvailable(req.RecognType, req.SourceLanguage) {
		RespondServiceError(w, "Speech recognition service is not available")
		return
	}

	if req.SourceLanguage != req.TargetLanguage {
		if !isTranslationServiceAvailable(req.TranslateType, req.TargetLanguage) {
			RespondServiceError(w, "Translation service is not available")
			return
		}
	}

	if !isTTSServiceAvailable(req.TTSType) {
		RespondServiceError(w, "TTS service is not available")
		return
	}

	// Generate task ID
	taskID := GenerateTaskID()

	// Create comprehensive video translation task
	task := map[string]interface{}{
		"task_id":         taskID,
		"user_id":         userID,
		"task_type":       "video_translation",
		"video_file":      req.VideoFile,
		"recogn_type":     req.RecognType,
		"model_name":      req.ModelName,
		"split_type":      req.SplitType,
		"is_cuda":         req.IsCuda,
		"translate_type":  req.TranslateType,
		"source_language": req.SourceLanguage,
		"target_language": req.TargetLanguage,
		"tts_type":        req.TTSType,
		"voice_role":      req.VoiceRole,
		"voice_rate":      req.VoiceRate,
		"volume":          req.Volume,
		"pitch":           req.Pitch,
		"voice_autorate":  req.VoiceAutorate,
		"video_autorate":  req.VideoAutorate,
		"subtitle_type":   req.SubtitleType,
		"append_video":    req.AppendVideo,
		"only_video":      req.OnlyVideo,
		"is_separate":     req.IsSeparate,
		"back_audio":      req.BackAudio,
		"output_dir":      req.OutputDir,
		"output_name":     req.OutputName,
		"status":          "queued",
		"created_at":      time.Now().Unix(),
	}

	// Publish task to queue
	err := h.publishVideoTranslationTask(task)
	if err != nil {
		RespondTaskError(w, fmt.Sprintf("Failed to queue video translation task: %s", err.Error()))
		return
	}

	// Return task ID for status tracking
	RespondTaskSuccess(w, taskID)
}

func (h *VideoTranslationHandler) publishVideoTranslationTask(task map[string]interface{}) error {
	// Simulate publishing comprehensive video translation task
	taskID := task["task_id"].(string)
	userID := task["user_id"].(string)
	videoFile := task["video_file"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the task (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("Video translation task created",
		"task_id", taskID,
		"user_id", userID,
		"task_type", "video_translation",
		"file", videoFile,
		"priority", "high",
		"timestamp", time.Now().Unix(),
	)

	return nil
}

// GetVideoInfo - Get video file information
func GetVideoInfo(videoPath string) (map[string]interface{}, error) {
	// Simulate video information extraction
	// In real implementation, use FFmpeg or similar to get video metadata

	info := map[string]interface{}{
		"filename":    filepath.Base(videoPath),
		"path":        videoPath,
		"duration":    0,      // Duration in seconds
		"width":       1920,   // Video width
		"height":      1080,   // Video height
		"fps":         30.0,   // Frames per second
		"bitrate":     0,      // Bitrate
		"codec":       "h264", // Video codec
		"audio_codec": "aac",  // Audio codec
		"file_size":   0,      // File size in bytes
	}

	return info, nil
}

// ValidateVideoTranslationRequest - Comprehensive request validation
func ValidateVideoTranslationRequest(req *VideoTranslationRequest) error {
	// Additional validation logic specific to video translation

	// Check if voice role is compatible with TTS type and language
	if !isVoiceRoleCompatible(req.TTSType, req.VoiceRole, req.TargetLanguage) {
		return fmt.Errorf("voice_role '%s' is not compatible with tts_type %d and language '%s'",
			req.VoiceRole, req.TTSType, req.TargetLanguage)
	}

	// Validate model compatibility with recognition type
	if !isValidModelName(req.RecognType, req.ModelName) {
		return fmt.Errorf("model_name '%s' is not valid for recogn_type %d",
			req.ModelName, req.RecognType)
	}

	return nil
}

func isVoiceRoleCompatible(ttsType int, voiceRole string, language string) bool {
	// Simulate voice role compatibility check
	// In real implementation, check if the voice role is available for the TTS service and language

	// Example compatibility matrix
	compatibility := map[int]map[string][]string{
		0: { // Edge-TTS
			"zh-cn": {"zh-CN-YunjianNeural", "zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural"},
			"en":    {"en-US-JennyNeural", "en-US-GuyNeural", "en-US-AriaNeural"},
		},
		1: { // CosyVoice
			"zh-cn": {"中文女", "中文男", "英文女"},
		},
	}

	if langVoices, exists := compatibility[ttsType]; exists {
		if voices, langExists := langVoices[language]; langExists {
			for _, voice := range voices {
				if voice == voiceRole {
					return true
				}
			}
		}
	}

	return true // Default to true for unknown combinations
}
