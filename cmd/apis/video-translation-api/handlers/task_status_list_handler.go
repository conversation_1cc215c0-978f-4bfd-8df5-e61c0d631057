package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/nhdms/base-go/pkg/logger"
)

// TaskStatusListHandler handles multiple task status requests
type TaskStatusListHandler struct {
	<PERSON><PERSON><PERSON><PERSON>
}

// TaskStatusListRequest represents the request structure for multiple task status
type TaskStatusListRequest struct {
	TaskIDList []string `json:"task_id_list" validate:"required"`
}

// TaskStatusListResponse represents the response structure for multiple task status
type TaskStatusListResponse struct {
	Tasks map[string]interface{} `json:"tasks"`
}

// TaskStatusInfo represents individual task status information
type TaskStatusInfo struct {
	TaskID   string      `json:"task_id"`
	Type     string      `json:"type"`
	Status   string      `json:"status"`
	Progress int         `json:"progress"`
	Message  string      `json:"message"`
	Data     interface{} `json:"data,omitempty"`
	Error    string      `json:"error,omitempty"`
}

// ServeHTTP handles the multiple task status request
func (h *TaskStatusListHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	var taskIDs []string

	// Get task_id_list from different sources based on request method
	switch r.Method {
	case http.MethodGet:
		// For GET requests, expect comma-separated task IDs in query parameter
		taskIDsParam := r.URL.Query().Get("task_id_list")
		if taskIDsParam != "" {
			taskIDs = strings.Split(taskIDsParam, ",")
		}
	case http.MethodPost:
		// Try JSON body first
		if r.Header.Get("Content-Type") == "application/json" {
			var req TaskStatusListRequest
			if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
				RespondError(w, "Invalid JSON format")
				return
			}
			taskIDs = req.TaskIDList
		} else {
			// Try form data
			if err := r.ParseForm(); err == nil {
				taskIDsParam := r.FormValue("task_id_list")
				if taskIDsParam != "" {
					taskIDs = strings.Split(taskIDsParam, ",")
				}
			}
		}
	}

	// Validate task_id_list
	if len(taskIDs) == 0 {
		RespondError(w, "task_id_list parameter is required and must not be empty")
		return
	}

	// Clean up task IDs (trim whitespace)
	for i, taskID := range taskIDs {
		taskIDs[i] = strings.TrimSpace(taskID)
	}

	// Process each task ID
	taskResults := make(map[string]interface{})

	for _, taskID := range taskIDs {
		if taskID == "" {
			continue // Skip empty task IDs
		}

		taskStatus := h.getTaskStatus(taskID)
		taskResults[taskID] = taskStatus
	}

	// Log the batch status check
	logger.DefaultLogger.Infow("Batch task status checked",
		"task_count", len(taskIDs),
		"successful_checks", len(taskResults),
	)

	// Return results
	response := TaskStatusListResponse{
		Tasks: taskResults,
	}

	RespondSuccess(w, response)
}

// getTaskStatus retrieves status for a single task
func (h *TaskStatusListHandler) getTaskStatus(taskID string) interface{} {
	// Check status file
	statusFile := filepath.Join("/tmp/process_info", fmt.Sprintf("%s.json", taskID))

	if !h.fileExists(statusFile) {
		return TaskStatusInfo{
			TaskID:  taskID,
			Status:  "not_found",
			Message: "Task not found",
			Error:   "Task not found",
		}
	}

	// Read status info
	statusData, err := h.readStatusFile(statusFile)
	if err != nil {
		logger.DefaultLogger.Errorw("Failed to read status file", "task_id", taskID, "error", err)
		return TaskStatusInfo{
			TaskID:  taskID,
			Status:  "error",
			Message: "Failed to read task status",
			Error:   err.Error(),
		}
	}

	// Build task status info based on task type
	taskInfo := TaskStatusInfo{
		TaskID:   taskID,
		Type:     statusData.Type,
		Status:   statusData.Status,
		Progress: statusData.Progress,
		Message:  statusData.Text,
	}

	// Infer status if not explicitly set
	if taskInfo.Status == "" {
		if strings.Contains(statusData.Text, "completed") || strings.Contains(statusData.Text, "success") {
			taskInfo.Status = "completed"
		} else if strings.Contains(statusData.Text, "failed") || strings.Contains(statusData.Text, "error") {
			taskInfo.Status = "failed"
		} else if strings.Contains(statusData.Text, "processing") || strings.Contains(statusData.Text, "generating") {
			taskInfo.Status = "running"
		} else {
			taskInfo.Status = "initialized"
		}
	}

	// Set default progress if not specified
	if taskInfo.Progress == 0 {
		switch taskInfo.Status {
		case "completed":
			taskInfo.Progress = 100
		case "failed":
			taskInfo.Progress = 0
		case "running":
			taskInfo.Progress = 50
		default:
			taskInfo.Progress = 10
		}
	}

	// Add task-specific data based on type
	switch statusData.Type {
	case "video_generation":
		taskInfo.Data = h.getVideoGenerationData(taskID)
	case "logs":
		// Check if it's actually a video generation task
		if strings.Contains(statusData.Text, "Video generation") {
			taskInfo.Type = "video_generation"
			taskInfo.Data = h.getVideoGenerationData(taskID)
		}
	}

	return taskInfo
}

// getVideoGenerationData retrieves video generation specific data
func (h *TaskStatusListHandler) getVideoGenerationData(taskID string) map[string]interface{} {
	data := make(map[string]interface{})

	// Check for generated video files
	targetDir := filepath.Join("/tmp/video-generation", taskID)
	videoFiles := h.findVideoFiles(targetDir)

	if len(videoFiles) > 0 {
		data["video_path"] = videoFiles[0]
		data["video_url"] = h.buildVideoURL(taskID, filepath.Base(videoFiles[0]))
		data["has_video"] = true
	} else {
		data["has_video"] = false
	}

	return data
}

// readStatusFile reads and parses the status file
func (h *TaskStatusListHandler) readStatusFile(statusFile string) (*StatusFileData, error) {
	data, err := os.ReadFile(statusFile)
	if err != nil {
		return nil, err
	}

	var statusData StatusFileData
	if err := json.Unmarshal(data, &statusData); err != nil {
		return nil, err
	}

	return &statusData, nil
}

// findVideoFiles finds video files in the target directory
func (h *TaskStatusListHandler) findVideoFiles(targetDir string) []string {
	var videoFiles []string

	if !h.fileExists(targetDir) {
		return videoFiles
	}

	videoExtensions := []string{".mp4", ".avi", ".mov", ".mkv"}

	for _, ext := range videoExtensions {
		pattern := filepath.Join(targetDir, "*"+ext)
		matches, err := filepath.Glob(pattern)
		if err != nil {
			continue
		}
		videoFiles = append(videoFiles, matches...)
	}

	return videoFiles
}

// buildVideoURL builds the accessible video URL
func (h *TaskStatusListHandler) buildVideoURL(taskID, filename string) string {
	// TODO: Implement actual base URL retrieval from configuration
	baseURL := "http://localhost:8080" // Placeholder
	return fmt.Sprintf("%s/apidata/%s/%s", baseURL, taskID, filename)
}

// fileExists checks if a file exists
func (h *TaskStatusListHandler) fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
