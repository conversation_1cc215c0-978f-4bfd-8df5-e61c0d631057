package handlers

import (
	"crypto/md5"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// FileUploadHandler - File upload handler (第6个接口)
type FileUploadHandler struct {
	BaseHandler
}

// FileUploadResponse - File upload response structure
type FileUploadResponse struct {
	FileName     string `json:"filename"`                // 原始文件名
	FilePath     string `json:"filepath"`                // 服务器文件路径
	FileSize     int64  `json:"filesize"`                // 文件大小（字节）
	FileHash     string `json:"filehash"`                // 文件MD5哈希
	FileType     string `json:"filetype"`                // 文件类型
	Duration     int    `json:"duration,omitempty"`      // 媒体文件时长（秒）
	Thumbnail    string `json:"thumbnail,omitempty"`     // 缩略图路径
	UploadTime   int64  `json:"upload_time"`             // 上传时间戳
	IsValid      bool   `json:"is_valid"`                // 文件是否有效
	ErrorMessage string `json:"error_message,omitempty"` // 错误信息
}

func (h *FileUploadHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "FileUploadHandler")
	defer func() {
		LogResponse(r, "FileUploadHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse multipart form
	err := r.ParseMultipartForm(500 << 20) // 500MB max memory
	if err != nil {
		RespondError(w, fmt.Sprintf("Failed to parse multipart form: %s", err.Error()))
		return
	}

	// Get uploaded file
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		RespondError(w, "No file uploaded or invalid file parameter")
		return
	}
	defer file.Close()

	// Validate file size (max 2GB)
	maxSize := int64(2 * 1024 * 1024 * 1024) // 2GB
	if fileHeader.Size > maxSize {
		RespondError(w, "File size too large. Maximum allowed: 2GB")
		return
	}

	// Get file type from form or detect from extension
	fileType := r.FormValue("type")
	if fileType == "" {
		fileType = detectFileType(fileHeader.Filename)
	}

	// Validate file type
	if !isAllowedFileType(fileType, fileHeader.Filename) {
		RespondError(w, "Unsupported file type")
		return
	}

	// Generate unique filename
	fileHash := generateFileHash(file)
	file.Seek(0, 0) // Reset file pointer after hashing

	ext := filepath.Ext(fileHeader.Filename)
	uniqueFilename := fmt.Sprintf("%s_%d%s", fileHash[:16], time.Now().Unix(), ext)

	// Create user upload directory
	uploadDir := filepath.Join("uploads", userID)
	err = os.MkdirAll(uploadDir, 0755)
	if err != nil {
		RespondError(w, fmt.Sprintf("Failed to create upload directory: %s", err.Error()))
		return
	}

	// Save file
	filePath := filepath.Join(uploadDir, uniqueFilename)
	destFile, err := os.Create(filePath)
	if err != nil {
		RespondError(w, fmt.Sprintf("Failed to create file: %s", err.Error()))
		return
	}
	defer destFile.Close()

	// Copy file content
	fileSize, err := io.Copy(destFile, file)
	if err != nil {
		RespondError(w, fmt.Sprintf("Failed to save file: %s", err.Error()))
		return
	}

	// Create response
	response := &FileUploadResponse{
		FileName:   fileHeader.Filename,
		FilePath:   filePath,
		FileSize:   fileSize,
		FileHash:   fileHash,
		FileType:   fileType,
		UploadTime: time.Now().Unix(),
		IsValid:    true,
	}

	// Generate thumbnail for video files
	if fileType == "video" {
		thumbnailPath, err := h.generateThumbnail(filePath)
		if err == nil {
			response.Thumbnail = thumbnailPath
		}

		// Get video duration
		duration, err := h.getVideoDuration(filePath)
		if err == nil {
			response.Duration = duration
		}
	}

	// Get audio duration for audio files
	if fileType == "audio" {
		duration, err := h.getAudioDuration(filePath)
		if err == nil {
			response.Duration = duration
		}
	}

	// Validate file content
	if !h.validateFileContent(filePath, fileType) {
		response.IsValid = false
		response.ErrorMessage = "Invalid file content or corrupted file"
	}

	// Log upload activity
	h.logUploadActivity(userID, response)

	// Return upload result
	RespondSuccess(w, response)
}

func detectFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	if IsVideoFile(filename) {
		return "video"
	} else if IsAudioFile(filename) {
		return "audio"
	} else if IsSubtitleFile(filename) {
		return "subtitle"
	} else if ext == ".txt" {
		return "text"
	} else if ext == ".json" {
		return "json"
	}

	return "unknown"
}

func isAllowedFileType(fileType, filename string) bool {
	allowedTypes := map[string]bool{
		"video":    true,
		"audio":    true,
		"subtitle": true,
		"text":     true,
		"json":     true,
	}

	return allowedTypes[fileType]
}

func generateFileHash(file io.Reader) string {
	hash := md5.New()
	io.Copy(hash, file)
	return fmt.Sprintf("%x", hash.Sum(nil))
}

func (h *FileUploadHandler) generateThumbnail(videoPath string) (string, error) {
	// Simulate thumbnail generation
	// In real implementation, use FFmpeg to generate video thumbnail

	thumbnailDir := filepath.Join(filepath.Dir(videoPath), "thumbnails")
	err := os.MkdirAll(thumbnailDir, 0755)
	if err != nil {
		return "", err
	}

	baseName := strings.TrimSuffix(filepath.Base(videoPath), filepath.Ext(videoPath))
	thumbnailPath := filepath.Join(thumbnailDir, baseName+"_thumb.jpg")

	// Mock thumbnail creation (in real implementation, use FFmpeg)
	// ffmpeg -i input.mp4 -ss 00:00:01 -vframes 1 -q:v 2 thumbnail.jpg

	return thumbnailPath, nil
}

func (h *FileUploadHandler) getVideoDuration(videoPath string) (int, error) {
	// Simulate video duration extraction
	// In real implementation, use FFprobe or similar tool

	// Mock duration (in real implementation, use FFprobe)
	// ffprobe -v quiet -show_entries format=duration -of csv="p=0" input.mp4

	return 120, nil // Mock 2 minutes duration
}

func (h *FileUploadHandler) getAudioDuration(audioPath string) (int, error) {
	// Simulate audio duration extraction
	// In real implementation, use FFprobe or audio analysis library

	return 180, nil // Mock 3 minutes duration
}

func (h *FileUploadHandler) validateFileContent(filePath, fileType string) bool {
	// Basic file validation
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return false
	}

	// Check if file is not empty
	if fileInfo.Size() == 0 {
		return false
	}

	// Additional validation based on file type
	switch fileType {
	case "video":
		return h.validateVideoFile(filePath)
	case "audio":
		return h.validateAudioFile(filePath)
	case "subtitle":
		return h.validateSubtitleFile(filePath)
	default:
		return true
	}
}

func (h *FileUploadHandler) validateVideoFile(filePath string) bool {
	// Basic video file validation
	// In real implementation, use FFprobe to validate video format

	// Check file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	validExts := map[string]bool{
		".mp4": true, ".avi": true, ".mov": true, ".mkv": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
	}

	return validExts[ext]
}

func (h *FileUploadHandler) validateAudioFile(filePath string) bool {
	// Basic audio file validation
	ext := strings.ToLower(filepath.Ext(filePath))
	validExts := map[string]bool{
		".mp3": true, ".wav": true, ".flac": true, ".aac": true,
		".ogg": true, ".wma": true, ".m4a": true, ".opus": true,
	}

	return validExts[ext]
}

func (h *FileUploadHandler) validateSubtitleFile(filePath string) bool {
	// Use existing validation function
	return ValidateSubtitleContent(filePath) == nil
}

func (h *FileUploadHandler) logUploadActivity(userID string, response *FileUploadResponse) {
	// Log upload activity for audit purposes
	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the activity (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("File upload activity",
		"user_id", userID,
		"filename", response.FileName,
		"filepath", response.FilePath,
		"filesize", response.FileSize,
		"filehash", response.FileHash,
		"filetype", response.FileType,
		"upload_time", response.UploadTime,
		"is_valid", response.IsValid,
		"timestamp", time.Now().Unix(),
	)
}

// GetUploadLimits - Get upload limits and restrictions
func GetUploadLimits() map[string]interface{} {
	return map[string]interface{}{
		"max_file_size":            "2GB",
		"max_file_size_bytes":      2 * 1024 * 1024 * 1024,
		"allowed_video_formats":    []string{".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"},
		"allowed_audio_formats":    []string{".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a", ".opus"},
		"allowed_subtitle_formats": []string{".srt", ".vtt", ".ass", ".ssa"},
		"allowed_text_formats":     []string{".txt", ".json"},
		"upload_timeout":           "300 seconds",
		"concurrent_uploads":       3,
	}
}
