package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/nhdms/base-go/pkg/logger"
)

// GenerateVideoHandler handles video generation requests
type GenerateVideoHandler struct {
	BaseHandler
}

// GenerateVideoRequest represents the request structure for video generation
type GenerateVideoRequest struct {
	Prompt       string `json:"prompt,omitempty"`
	ImagePath    string `json:"image_path,omitempty"`
	VideoGenType int    `json:"videogen_type,omitempty"`
	AsyncMode    bool   `json:"async_mode,omitempty"`
	Model        string `json:"model,omitempty"`
	Style        string `json:"style,omitempty"`

	// Minimax specific parameters
	Duration        int    `json:"duration,omitempty"`
	Resolution      string `json:"resolution,omitempty"`
	PromptOptimizer bool   `json:"prompt_optimizer,omitempty"`
}

// GenerateVideoResponse represents the response structure for video generation
type GenerateVideoResponse struct {
	TaskID    string `json:"task_id,omitempty"`
	VideoPath string `json:"video_path,omitempty"`
}

// VideoGenerationTask represents a video generation task configuration
type VideoGenerationTask struct {
	UUID         string `json:"uuid"`
	Prompt       string `json:"prompt"`
	ImagePath    string `json:"image_path"`
	VideoGenType int    `json:"videogen_type"`
	Model        string `json:"model"`
	AsyncMode    bool   `json:"async_mode"`
	TargetDir    string `json:"target_dir"`
	CacheFolder  string `json:"cache_folder"`

	// Minimax specific
	Duration        int    `json:"duration,omitempty"`
	Resolution      string `json:"resolution,omitempty"`
	PromptOptimizer bool   `json:"prompt_optimizer,omitempty"`

	// Task metadata
	CreatedAt time.Time `json:"created_at"`
	Status    string    `json:"status"`
	UserID    string    `json:"user_id,omitempty"`
}

// ServeHTTP handles the video generation request
func (h *GenerateVideoHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Parse request
	var req GenerateVideoRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		RespondError(w, "Invalid JSON format")
		return
	}

	// Validate input - either prompt or image_path must be provided
	if strings.TrimSpace(req.Prompt) == "" && strings.TrimSpace(req.ImagePath) == "" {
		RespondError(w, "Either prompt or image_path must be provided")
		return
	}

	// Set defaults
	if req.AsyncMode == false && r.Header.Get("X-Async-Mode") != "false" {
		req.AsyncMode = true // Default to async mode
	}

	// Parse model and determine provider
	videoGenType, model := h.parseModelAndProvider(req.Model, req.VideoGenType, req.Style)

	// Validate video generation service availability
	if !h.isVideoGenServiceAvailable(videoGenType) {
		RespondError(w, fmt.Sprintf("Video generation service type %d is not available", videoGenType))
		return
	}

	// Get user ID from authentication context
	userID := GetUserID(r)

	// Create task UUID and directories
	taskUUID := uuid.New().String()
	targetDir := filepath.Join("/tmp/video-generation", taskUUID)
	cacheFolder := filepath.Join("/tmp/cache", taskUUID)

	// Create task configuration
	task := VideoGenerationTask{
		UUID:         taskUUID,
		Prompt:       req.Prompt,
		ImagePath:    req.ImagePath,
		VideoGenType: videoGenType,
		Model:        model,
		AsyncMode:    req.AsyncMode,
		TargetDir:    targetDir,
		CacheFolder:  cacheFolder,
		CreatedAt:    time.Now(),
		Status:       "initialized",
		UserID:       userID,
	}

	// Add provider-specific parameters
	if videoGenType == 1 { // Minimax
		task.Duration = req.Duration
		if task.Duration == 0 {
			task.Duration = 6 // Default 6 seconds
		}
		task.Resolution = req.Resolution
		if task.Resolution == "" {
			task.Resolution = "768P" // Default resolution
		}
		task.PromptOptimizer = req.PromptOptimizer
		if !req.PromptOptimizer && r.Header.Get("X-Prompt-Optimizer") != "false" {
			task.PromptOptimizer = true // Default to true
		}
	}

	// Create initial status file
	if err := h.createInitialStatusFile(taskUUID); err != nil {
		logger.DefaultLogger.Errorw("Failed to create status file", "task_id", taskUUID, "error", err)
		RespondError(w, "Failed to initialize task")
		return
	}

	if req.AsyncMode {
		// Async mode - queue the task
		logger.DefaultLogger.Infow("Video generation task queued",
			"task_id", taskUUID,
			"user_id", userID,
			"prompt", req.Prompt[:min(100, len(req.Prompt))],
			"model", model,
			"videogen_type", videoGenType,
		)

		// TODO: In real implementation, publish task to message queue
		// For now, just log the task creation
		logger.DefaultLogger.Infow("Video generation task created",
			"task_id", taskUUID,
			"user_id", userID,
			"task_type", "video_generation",
			"async_mode", true,
			"timestamp", time.Now().Unix(),
		)

		// Return task ID for async tracking
		response := GenerateVideoResponse{
			TaskID: taskUUID,
		}
		RespondSuccess(w, response)
	} else {
		// Sync mode - process immediately (placeholder)
		logger.DefaultLogger.Infow("Video generation task processing synchronously",
			"task_id", taskUUID,
			"user_id", userID,
		)

		// TODO: In real implementation, process the video generation synchronously
		// For now, simulate processing and return a placeholder response

		// Simulate processing time
		time.Sleep(2 * time.Second)

		// Return placeholder video path
		videoPath := filepath.Join(targetDir, "generated_video.mp4")
		response := GenerateVideoResponse{
			TaskID:    taskUUID,
			VideoPath: videoPath,
		}
		RespondSuccess(w, response)
	}
}

// parseModelAndProvider parses model name and determines provider and internal model name
func (h *GenerateVideoHandler) parseModelAndProvider(modelName string, defaultVideoGenType int, style string) (int, string) {
	if modelName == "" {
		// Use defaults when no model specified
		if defaultVideoGenType == 1 { // Minimax
			return 1, "MiniMax-Hailuo-02"
		}
		// Default to BytePlus
		model := "seedance-1-0-lite-t2v-250428"
		if style == "pro" || style == "ultra" {
			model = "seedance-1-0-pro-t2v-250428"
		}
		return 0, model
	}

	modelLower := strings.ToLower(modelName)

	// BytePlus models (seedance-*)
	if strings.HasPrefix(modelLower, "seedance-") {
		if modelLower == "seedance-lite" {
			return 0, "seedance-1-0-lite-t2v-250428"
		} else if modelLower == "seedance-pro" {
			return 0, "seedance-1-0-pro-t2v-250428"
		}
		// Custom seedance model, use as-is
		return 0, modelName
	}

	// Minimax models (minimax-*)
	if strings.HasPrefix(modelLower, "minimax-") {
		modelSuffix := modelName[8:] // Remove 'minimax-' prefix

		// Map common patterns
		minimaxModelMap := map[string]string{
			"hailuo-02":    "MiniMax-Hailuo-02",
			"hailuo":       "MiniMax-Hailuo-02",
			"t2v-director": "T2V-01-Director",
			"i2v-director": "I2V-01-Director",
			"s2v":          "S2V-01",
			"i2v":          "I2V-01",
			"i2v-live":     "I2V-01-live",
			"t2v":          "T2V-01",
		}

		if internalModel, exists := minimaxModelMap[strings.ToLower(modelSuffix)]; exists {
			return 1, internalModel
		}
		return 1, fmt.Sprintf("MiniMax-%s", modelSuffix)
	}

	// Direct model names (backward compatibility)
	return defaultVideoGenType, modelName
}

// isVideoGenServiceAvailable checks if the video generation service is available
func (h *GenerateVideoHandler) isVideoGenServiceAvailable(videoGenType int) bool {
	// TODO: Implement actual service availability check
	// This would typically check configuration, API keys, service health, etc.
	return true
}

// createInitialStatusFile creates an initial status file for the task
func (h *GenerateVideoHandler) createInitialStatusFile(taskID string) error {
	// TODO: Implement actual status file creation
	// This would typically create a status file in a shared location
	logger.DefaultLogger.Infow("Status file created", "task_id", taskID)
	return nil
}
