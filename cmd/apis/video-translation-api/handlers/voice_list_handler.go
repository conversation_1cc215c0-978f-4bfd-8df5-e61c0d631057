package handlers

import (
	"net/http"
	"strconv"
	"time"
)

// VoiceListHandler - Voice list handler (第5个接口)
type VoiceListHandler struct {
	BaseHandler
}

// VoiceInfo - Voice information structure
type VoiceInfo struct {
	Name        string   `json:"name"`                  // 语音名称
	Gender      string   `json:"gender"`                // 性别 Male/Female
	Language    string   `json:"language"`              // 语言代码
	LanguageName string  `json:"language_name"`         // 语言名称
	Country     string   `json:"country,omitempty"`     // 国家
	Age         string   `json:"age,omitempty"`         // 年龄段
	Style       []string `json:"style,omitempty"`       // 语音风格
	SampleRate  int      `json:"sample_rate,omitempty"` // 采样率
	Quality     string   `json:"quality,omitempty"`     // 音质等级
}

// VoiceListResponse - Voice list response structure
type VoiceListResponse struct {
	TTSType     int         `json:"tts_type"`              // TTS类型
	TTSName     string      `json:"tts_name"`              // TTS服务名称
	Language    string      `json:"language,omitempty"`    // 筛选的语言
	TotalVoices int         `json:"total_voices"`          // 总语音数量
	Voices      []VoiceInfo `json:"voices"`                // 语音列表
}

func (h *VoiceListHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "VoiceListHandler")
	defer func() {
		LogResponse(r, "VoiceListHandler", start, http.StatusOK)
	}()

	// Only allow GET method
	if r.Method != http.MethodGet {
		RespondError(w, "Method not allowed")
		return
	}

	// Parse query parameters
	ttsTypeStr := r.URL.Query().Get("tts_type")
	language := r.URL.Query().Get("language")
	gender := r.URL.Query().Get("gender")

	// Validate TTS type parameter
	if ttsTypeStr == "" {
		RespondError(w, "The parameter tts_type is required")
		return
	}

	ttsType, err := strconv.Atoi(ttsTypeStr)
	if err != nil {
		RespondError(w, "Invalid tts_type parameter")
		return
	}

	if !ValidateTTSType(ttsType) {
		RespondError(w, "Invalid tts_type parameter")
		return
	}

	// Validate language if provided
	if language != "" && !ValidateLanguageCode(language) {
		RespondError(w, "Invalid language code")
		return
	}

	// Validate gender if provided
	if gender != "" && gender != "Male" && gender != "Female" {
		RespondError(w, "Invalid gender. Must be 'Male' or 'Female'")
		return
	}

	// Get voice list
	voiceList := h.getVoiceList(ttsType, language, gender)

	// Return voice list
	RespondSuccess(w, voiceList)
}

func (h *VoiceListHandler) getVoiceList(ttsType int, language, gender string) *VoiceListResponse {
	response := &VoiceListResponse{
		TTSType:  ttsType,
		TTSName:  getTTSServiceName(ttsType),
		Language: language,
		Voices:   []VoiceInfo{},
	}

	// Get voices based on TTS type
	allVoices := getAllVoicesForTTSType(ttsType)

	// Filter voices by language and gender
	for _, voice := range allVoices {
		// Filter by language
		if language != "" && voice.Language != language {
			continue
		}

		// Filter by gender
		if gender != "" && voice.Gender != gender {
			continue
		}

		response.Voices = append(response.Voices, voice)
	}

	response.TotalVoices = len(response.Voices)
	return response
}

func getTTSServiceName(ttsType int) string {
	serviceNames := map[int]string{
		0:  "Edge-TTS",
		1:  "CosyVoice",
		2:  "ChatTTS",
		3:  "302.AI",
		4:  "FishTTS",
		5:  "Azure-TTS",
		6:  "GPT-SoVITS",
		7:  "Clone-Voice",
		8:  "OpenAI TTS",
		9:  "Elevenlabs.io",
		10: "Google TTS",
		11: "自定义TTS API",
		12: "Bark-TTS",
		13: "Coqui-TTS",
		14: "VITS",
		15: "FastSpeech2",
		16: "Tacotron2",
		17: "WaveNet",
	}

	if name, exists := serviceNames[ttsType]; exists {
		return name
	}
	return "Unknown TTS Service"
}

func getAllVoicesForTTSType(ttsType int) []VoiceInfo {
	switch ttsType {
	case 0: // Edge-TTS
		return []VoiceInfo{
			// Chinese voices
			{
				Name: "zh-CN-YunjianNeural", Gender: "Male", Language: "zh-cn", LanguageName: "简体中文",
				Country: "China", Age: "Adult", Style: []string{"general", "calm"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "zh-CN-XiaoxiaoNeural", Gender: "Female", Language: "zh-cn", LanguageName: "简体中文",
				Country: "China", Age: "Adult", Style: []string{"general", "cheerful"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "zh-CN-YunxiNeural", Gender: "Male", Language: "zh-cn", LanguageName: "简体中文",
				Country: "China", Age: "Adult", Style: []string{"general", "calm", "cheerful"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "zh-CN-XiaoyiNeural", Gender: "Female", Language: "zh-cn", LanguageName: "简体中文",
				Country: "China", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			// Traditional Chinese voices
			{
				Name: "zh-TW-YunJheNeural", Gender: "Male", Language: "zh-tw", LanguageName: "繁体中文",
				Country: "Taiwan", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "zh-TW-HsiaoChenNeural", Gender: "Female", Language: "zh-tw", LanguageName: "繁体中文",
				Country: "Taiwan", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			// English voices
			{
				Name: "en-US-JennyNeural", Gender: "Female", Language: "en", LanguageName: "English",
				Country: "United States", Age: "Adult", Style: []string{"general", "cheerful"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "en-US-GuyNeural", Gender: "Male", Language: "en", LanguageName: "English",
				Country: "United States", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "en-US-AriaNeural", Gender: "Female", Language: "en", LanguageName: "English",
				Country: "United States", Age: "Adult", Style: []string{"general", "cheerful", "chat"}, SampleRate: 24000, Quality: "high",
			},
			// Japanese voices
			{
				Name: "ja-JP-NanamiNeural", Gender: "Female", Language: "ja", LanguageName: "日本語",
				Country: "Japan", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "ja-JP-KeitaNeural", Gender: "Male", Language: "ja", LanguageName: "日本語",
				Country: "Japan", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			// Korean voices
			{
				Name: "ko-KR-SunHiNeural", Gender: "Female", Language: "ko", LanguageName: "한국어",
				Country: "South Korea", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "ko-KR-InJoonNeural", Gender: "Male", Language: "ko", LanguageName: "한국어",
				Country: "South Korea", Age: "Adult", Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
		}

	case 1: // CosyVoice
		return []VoiceInfo{
			{
				Name: "中文女", Gender: "Female", Language: "zh-cn", LanguageName: "简体中文",
				Style: []string{"natural"}, SampleRate: 22050, Quality: "high",
			},
			{
				Name: "中文男", Gender: "Male", Language: "zh-cn", LanguageName: "简体中文",
				Style: []string{"natural"}, SampleRate: 22050, Quality: "high",
			},
			{
				Name: "英文女", Gender: "Female", Language: "en", LanguageName: "English",
				Style: []string{"natural"}, SampleRate: 22050, Quality: "high",
			},
		}

	case 2: // ChatTTS
		return []VoiceInfo{
			{
				Name: "seed_1042_restored_emb", Gender: "Female", Language: "zh-cn", LanguageName: "简体中文",
				Style: []string{"conversational"}, SampleRate: 24000, Quality: "medium",
			},
			{
				Name: "seed_1234_restored_emb", Gender: "Male", Language: "zh-cn", LanguageName: "简体中文",
				Style: []string{"conversational"}, SampleRate: 24000, Quality: "medium",
			},
		}

	case 8: // OpenAI TTS
		return []VoiceInfo{
			{
				Name: "alloy", Gender: "Male", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "echo", Gender: "Male", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "fable", Gender: "Male", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "onyx", Gender: "Male", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "nova", Gender: "Female", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
			{
				Name: "shimmer", Gender: "Female", Language: "en", LanguageName: "English",
				Style: []string{"general"}, SampleRate: 24000, Quality: "high",
			},
		}

	default:
		// Return empty list for unknown TTS types
		return []VoiceInfo{}
	}
}

// GetTTSServices - Get list of all available TTS services
func GetTTSServices() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":          0,
			"name":        "Edge-TTS",
			"description": "Microsoft Edge Text-to-Speech",
			"languages":   []string{"zh-cn", "zh-tw", "en", "ja", "ko", "fr", "de", "es"},
			"free":        true,
		},
		{
			"id":          1,
			"name":        "CosyVoice",
			"description": "Alibaba CosyVoice TTS",
			"languages":   []string{"zh-cn", "en"},
			"free":        false,
		},
		{
			"id":          2,
			"name":        "ChatTTS",
			"description": "ChatTTS Conversational Voice",
			"languages":   []string{"zh-cn", "en"},
			"free":        true,
		},
		{
			"id":          8,
			"name":        "OpenAI TTS",
			"description": "OpenAI Text-to-Speech API",
			"languages":   []string{"en", "zh-cn", "ja", "ko", "fr", "de", "es"},
			"free":        false,
		},
		{
			"id":          10,
			"name":        "Google TTS",
			"description": "Google Cloud Text-to-Speech",
			"languages":   []string{"zh-cn", "en", "ja", "ko", "fr", "de", "es"},
			"free":        false,
		},
	}
}
