package handlers

import (
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// BatchProcessHandler - Batch processing handler (新增接口)
type BatchProcessHandler struct {
	BaseHandler
}

// BatchProcessRequest - Batch processing request structure
type BatchProcessRequest struct {
	// File parameters
	Files     []string `json:"files"`      // 必须参数，视频文件路径列表
	BatchName string   `json:"batch_name"` // 可选参数，批处理任务名称

	// Recognition parameters
	RecognType int    `json:"recogn_type"` // 必须参数，语音识别模式 0-6
	ModelName  string `json:"model_name"`  // 可选参数，识别模型名称
	SplitType  string `json:"split_type"`  // 可选参数，分割类型 all/avg
	IsCuda     bool   `json:"is_cuda"`     // 可选参数，是否启用CUDA加速

	// Translation parameters
	TranslateType  int    `json:"translate_type"`  // 必须参数，翻译渠道
	SourceLanguage string `json:"source_language"` // 可选参数，原始语言代码
	TargetLanguage string `json:"target_language"` // 必须参数，目标语言代码

	// TTS parameters
	TTSType       int    `json:"tts_type"`       // 必须参数，配音渠道 0-17
	VoiceRole     string `json:"voice_role"`     // 必须参数，配音角色名
	VoiceRate     string `json:"voice_rate"`     // 可选参数，语速调节
	Volume        string `json:"volume"`         // 可选参数，音量调节
	Pitch         string `json:"pitch"`          // 可选参数，音调调节
	VoiceAutorate bool   `json:"voice_autorate"` // 可选参数，自动语速
	VideoAutorate bool   `json:"video_autorate"` // 可选参数，视频自动调速

	// Subtitle parameters
	SubtitleType int    `json:"subtitle_type"` // 可选参数，字幕嵌入类型 0-4
	AppendVideo  bool   `json:"append_video"`  // 可选参数，是否延长视频
	OnlyVideo    bool   `json:"only_video"`    // 可选参数，是否只生成视频
	IsSeparate   bool   `json:"is_separate"`   // 可选参数，是否分离音轨
	BackAudio    string `json:"back_audio"`    // 可选参数，背景音频

	// Batch parameters
	MaxConcurrent int  `json:"max_concurrent"` // 可选参数，最大并发处理数
	Priority      int  `json:"priority"`       // 可选参数，批处理优先级
	AutoRetry     bool `json:"auto_retry"`     // 可选参数，是否自动重试失败任务
}

func (h *BatchProcessHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "BatchProcessHandler")
	defer func() {
		LogResponse(r, "BatchProcessHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req BatchProcessRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate required parameters
	if len(req.Files) == 0 {
		RespondError(w, "The parameter files is required and must be a non-empty array")
		return
	}

	if strings.TrimSpace(req.TargetLanguage) == "" {
		RespondError(w, "The parameter target_language is required")
		return
	}

	if strings.TrimSpace(req.VoiceRole) == "" {
		RespondError(w, "The parameter voice_role is required")
		return
	}

	// Validate file limit (max 50 files per batch)
	if len(req.Files) > 50 {
		RespondError(w, "Maximum 50 files allowed per batch")
		return
	}

	// Validate all files exist and are video files
	for i, filePath := range req.Files {
		if strings.TrimSpace(filePath) == "" {
			RespondError(w, fmt.Sprintf("File path at index %d is empty", i))
			return
		}

		if !fileExists(filePath) {
			RespondError(w, fmt.Sprintf("File does not exist: %s", filePath))
			return
		}

		if !IsVideoFile(filePath) {
			RespondError(w, fmt.Sprintf("Invalid video file format: %s", filePath))
			return
		}
	}

	// Validate parameters (reuse validation from video translation handler)
	if !ValidateRecognitionType(req.RecognType) {
		RespondError(w, "Invalid recogn_type parameter")
		return
	}

	if !ValidateTranslationType(req.TranslateType) {
		RespondError(w, "Invalid translate_type parameter")
		return
	}

	if !ValidateTTSType(req.TTSType) {
		RespondError(w, "Invalid tts_type parameter")
		return
	}

	if !ValidateLanguageCode(req.TargetLanguage) {
		RespondError(w, "Invalid target_language code")
		return
	}

	if req.SourceLanguage != "" && !ValidateLanguageCode(req.SourceLanguage) {
		RespondError(w, "Invalid source_language code")
		return
	}

	// Set default values
	if req.BatchName == "" {
		req.BatchName = fmt.Sprintf("Batch_%s", time.Now().Format("20060102_150405"))
	}
	if req.SplitType == "" {
		req.SplitType = "all"
	}
	if req.ModelName == "" {
		req.ModelName = getDefaultModelName(req.RecognType)
	}
	if req.VoiceRate == "" {
		req.VoiceRate = "+0%"
	}
	if req.Volume == "" {
		req.Volume = "+0%"
	}
	if req.Pitch == "" {
		req.Pitch = "+0Hz"
	}
	if req.MaxConcurrent <= 0 {
		req.MaxConcurrent = 3 // Default concurrent processing
	}

	// Validate format parameters
	if !isValidRateFormat(req.VoiceRate) {
		RespondError(w, "Invalid voice_rate format")
		return
	}
	if !isValidVolumeFormat(req.Volume) {
		RespondError(w, "Invalid volume format")
		return
	}
	if !isValidPitchFormat(req.Pitch) {
		RespondError(w, "Invalid pitch format")
		return
	}

	// Check service availability
	if !isRecognitionServiceAvailable(req.RecognType, req.SourceLanguage) {
		RespondServiceError(w, "Speech recognition service is not available")
		return
	}

	if req.SourceLanguage != req.TargetLanguage {
		if !isTranslationServiceAvailable(req.TranslateType, req.TargetLanguage) {
			RespondServiceError(w, "Translation service is not available")
			return
		}
	}

	if !isTTSServiceAvailable(req.TTSType) {
		RespondServiceError(w, "TTS service is not available")
		return
	}

	// Generate batch ID and task IDs
	batchID := GenerateBatchID()
	taskIDs := make([]string, len(req.Files))

	// Create individual tasks for each file
	for i, filePath := range req.Files {
		taskID := GenerateTaskID()
		taskIDs[i] = taskID

		// Create task configuration
		task := map[string]interface{}{
			"task_id":         taskID,
			"user_id":         userID,
			"task_type":       "video_translation",
			"batch_id":        batchID,
			"batch_name":      req.BatchName,
			"batch_index":     i + 1,
			"batch_total":     len(req.Files),
			"video_file":      filePath,
			"recogn_type":     req.RecognType,
			"model_name":      req.ModelName,
			"split_type":      req.SplitType,
			"is_cuda":         req.IsCuda,
			"translate_type":  req.TranslateType,
			"source_language": req.SourceLanguage,
			"target_language": req.TargetLanguage,
			"tts_type":        req.TTSType,
			"voice_role":      req.VoiceRole,
			"voice_rate":      req.VoiceRate,
			"volume":          req.Volume,
			"pitch":           req.Pitch,
			"voice_autorate":  req.VoiceAutorate,
			"video_autorate":  req.VideoAutorate,
			"subtitle_type":   req.SubtitleType,
			"append_video":    req.AppendVideo,
			"only_video":      req.OnlyVideo,
			"is_separate":     req.IsSeparate,
			"back_audio":      req.BackAudio,
			"priority":        req.Priority,
			"auto_retry":      req.AutoRetry,
			"status":          "queued",
			"created_at":      time.Now().Unix(),
		}

		// Publish individual task
		err := h.publishBatchTask(task)
		if err != nil {
			RespondTaskError(w, fmt.Sprintf("Failed to queue batch task %d: %s", i+1, err.Error()))
			return
		}
	}

	// Create batch metadata
	batchMetadata := map[string]interface{}{
		"batch_id":       batchID,
		"batch_name":     req.BatchName,
		"user_id":        userID,
		"task_ids":       taskIDs,
		"total_files":    len(req.Files),
		"max_concurrent": req.MaxConcurrent,
		"priority":       req.Priority,
		"auto_retry":     req.AutoRetry,
		"settings":       req,
		"status":         "queued",
		"created_at":     time.Now().Unix(),
	}

	// Publish batch metadata
	h.publishBatchMetadata(batchMetadata)

	// Return batch processing result
	RespondBatchSuccess(w, batchID, taskIDs, req.BatchName)
}

func (h *BatchProcessHandler) publishBatchTask(task map[string]interface{}) error {
	// Publish individual task to processing queue
	taskID := task["task_id"].(string)
	userID := task["user_id"].(string)
	videoFile := task["video_file"].(string)
	batchID := task["batch_id"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the task (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("Batch video translation task created",
		"task_id", taskID,
		"batch_id", batchID,
		"user_id", userID,
		"task_type", "video_translation",
		"file", videoFile,
		"priority", "batch",
		"timestamp", time.Now().Unix(),
	)

	return nil
}

func (h *BatchProcessHandler) publishBatchMetadata(metadata map[string]interface{}) error {
	// Publish batch metadata for tracking and management
	batchID := metadata["batch_id"].(string)
	userID := metadata["user_id"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the metadata (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("Batch processing metadata created",
		"batch_id", batchID,
		"user_id", userID,
		"total_files", metadata["total_files"],
		"timestamp", time.Now().Unix(),
	)

	return nil
}

// GetBatchProcessingLimits - Get batch processing limits
func GetBatchProcessingLimits() map[string]interface{} {
	return map[string]interface{}{
		"max_files_per_batch":      50,
		"max_concurrent_batches":   5,
		"max_concurrent_tasks":     10,
		"max_file_size_per_file":   "2GB",
		"max_total_size_per_batch": "20GB",
		"supported_video_formats":  []string{".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"},
		"batch_timeout":            "24 hours",
		"auto_retry_attempts":      3,
		"priority_levels":          []int{1, 2, 3, 4, 5}, // 1 = highest, 5 = lowest
	}
}

// ValidateBatchRequest - Additional batch-specific validation
func ValidateBatchRequest(req *BatchProcessRequest) error {
	// Check total file size
	var totalSize int64
	for _, filePath := range req.Files {
		if fileInfo, err := os.Stat(filePath); err == nil {
			totalSize += fileInfo.Size()
		}
	}

	maxTotalSize := int64(20 * 1024 * 1024 * 1024) // 20GB
	if totalSize > maxTotalSize {
		return fmt.Errorf("total batch size exceeds limit: %d bytes (max: %d bytes)", totalSize, maxTotalSize)
	}

	// Validate concurrent processing limit
	if req.MaxConcurrent > 10 {
		return fmt.Errorf("max_concurrent exceeds limit: %d (max: 10)", req.MaxConcurrent)
	}

	// Validate priority
	if req.Priority < 1 || req.Priority > 5 {
		return fmt.Errorf("invalid priority: %d (must be 1-5)", req.Priority)
	}

	return nil
}
