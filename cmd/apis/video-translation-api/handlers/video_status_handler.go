package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/nhdms/base-go/pkg/logger"
)

// VideoStatusHandler handles video generation status requests
type VideoStatusHandler struct {
	BaseHandler
}

// VideoStatusRequest represents the request structure for video status
type VideoStatusRequest struct {
	TaskID string `json:"task_id" validate:"required"`
}

// VideoStatusResponse represents the response structure for video status
type VideoStatusResponse struct {
	Progress  int    `json:"progress,omitempty"`
	Status    string `json:"status,omitempty"`
	VideoPath string `json:"video_path,omitempty"`
	VideoURL  string `json:"video_url,omitempty"`
}

// StatusFileData represents the structure of status file data
type StatusFileData struct {
	Type     string `json:"type"`
	Text     string `json:"text"`
	Progress int    `json:"progress,omitempty"`
	Status   string `json:"status,omitempty"`
}

// ServeHTTP handles the video status request
func (h *VideoStatusHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	var taskID string

	// Get task_id from different sources based on request method
	switch r.Method {
	case http.MethodGet:
		taskID = r.URL.Query().Get("task_id")
	case http.MethodPost:
		// Try form data first
		if err := r.ParseForm(); err == nil {
			taskID = r.FormValue("task_id")
		}

		// If not found in form, try JSON body
		if taskID == "" && r.Header.Get("Content-Type") == "application/json" {
			var req VideoStatusRequest
			if err := json.NewDecoder(r.Body).Decode(&req); err == nil {
				taskID = req.TaskID
			}
		}
	}

	// Validate task_id
	if strings.TrimSpace(taskID) == "" {
		RespondError(w, "task_id parameter is required")
		return
	}

	// Check status file
	statusFile := filepath.Join("/tmp/process_info", fmt.Sprintf("%s.json", taskID))
	if !h.fileExists(statusFile) {
		RespondError(w, "Task not found")
		return
	}

	// Read status info
	statusData, err := h.readStatusFile(statusFile)
	if err != nil {
		logger.DefaultLogger.Errorw("Failed to read status file", "task_id", taskID, "error", err)
		RespondError(w, "Failed to read task status")
		return
	}

	// Check if it's a video generation task
	if statusData.Type != "logs" && statusData.Type != "video_generation" {
		RespondError(w, "Not a video generation task")
		return
	}

	// Check if video file exists in target directory
	targetDir := filepath.Join("/tmp/video-generation", taskID)
	videoFiles := h.findVideoFiles(targetDir)

	// Determine task status based on status file and video file existence
	response := h.buildStatusResponse(statusData, videoFiles, taskID)

	// Log status check
	logger.DefaultLogger.Infow("Video status checked",
		"task_id", taskID,
		"status", response.Status,
		"progress", response.Progress,
		"has_video", response.VideoPath != "",
	)

	// Return appropriate response based on status
	if response.Status == "completed" {
		RespondSuccess(w, response)
	} else if response.Status == "failed" {
		RespondError(w, statusData.Text)
	} else {
		// Task is still running
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusAccepted) // 202 for in-progress
		json.NewEncoder(w).Encode(APIResponse{
			Code: -1, // -1 indicates in progress
			Msg:  statusData.Text,
			Data: response,
		})
	}
}

// readStatusFile reads and parses the status file
func (h *VideoStatusHandler) readStatusFile(statusFile string) (*StatusFileData, error) {
	data, err := os.ReadFile(statusFile)
	if err != nil {
		return nil, err
	}

	var statusData StatusFileData
	if err := json.Unmarshal(data, &statusData); err != nil {
		return nil, err
	}

	return &statusData, nil
}

// findVideoFiles finds video files in the target directory
func (h *VideoStatusHandler) findVideoFiles(targetDir string) []string {
	var videoFiles []string

	if !h.fileExists(targetDir) {
		return videoFiles
	}

	videoExtensions := []string{".mp4", ".avi", ".mov", ".mkv"}

	for _, ext := range videoExtensions {
		pattern := filepath.Join(targetDir, "*"+ext)
		matches, err := filepath.Glob(pattern)
		if err != nil {
			continue
		}
		videoFiles = append(videoFiles, matches...)
	}

	return videoFiles
}

// buildStatusResponse builds the appropriate status response
func (h *VideoStatusHandler) buildStatusResponse(statusData *StatusFileData, videoFiles []string, taskID string) *VideoStatusResponse {
	response := &VideoStatusResponse{
		Progress: statusData.Progress,
		Status:   statusData.Status,
	}

	// If status is not explicitly set, infer from text content
	if response.Status == "" {
		if strings.Contains(statusData.Text, "completed") || strings.Contains(statusData.Text, "success") {
			response.Status = "completed"
		} else if strings.Contains(statusData.Text, "failed") || strings.Contains(statusData.Text, "error") {
			response.Status = "failed"
		} else if strings.Contains(statusData.Text, "processing") || strings.Contains(statusData.Text, "generating") {
			response.Status = "running"
		} else {
			response.Status = "running" // Default to running
		}
	}

	// If video file exists and status indicates completion, mark as completed
	if len(videoFiles) > 0 && strings.Contains(statusData.Text, "Video generation completed:") {
		response.Status = "completed"
		response.VideoPath = videoFiles[0]
		response.VideoURL = h.buildVideoURL(taskID, filepath.Base(videoFiles[0]))
		response.Progress = 100
	}

	// Set default progress if not specified
	if response.Progress == 0 {
		switch response.Status {
		case "completed":
			response.Progress = 100
		case "failed":
			response.Progress = 0
		case "running":
			response.Progress = 50 // Default progress for running tasks
		default:
			response.Progress = 10 // Default progress for initialized tasks
		}
	}

	return response
}

// buildVideoURL builds the accessible video URL
func (h *VideoStatusHandler) buildVideoURL(taskID, filename string) string {
	// TODO: Implement actual base URL retrieval from configuration
	baseURL := "http://localhost:8080" // Placeholder
	return fmt.Sprintf("%s/apidata/%s/%s", baseURL, taskID, filename)
}

// fileExists checks if a file exists
func (h *VideoStatusHandler) fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
