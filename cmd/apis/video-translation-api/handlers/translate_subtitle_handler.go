package handlers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// TranslateSubtitleHandler - Subtitle translation handler (第2个接口)
type TranslateSubtitleHandler struct {
	BaseHandler
}

// TranslateSubtitleRequest - Subtitle translation request structure
type TranslateSubtitleRequest struct {
	SubtitleFile   string `json:"subtitle_file"`   // 必须参数，字幕文件路径
	SourceLanguage string `json:"source_language"` // 必须参数，源语言代码
	TargetLanguage string `json:"target_language"` // 必须参数，目标语言代码
	TranslateType  int    `json:"translate_type"`  // 必须参数，翻译渠道类型
	OnlyTranslate  bool   `json:"only_translate"`  // 可选参数，是否只翻译不生成新文件
	OutputDir      string `json:"output_dir"`      // 可选参数，输出目录
	PreserveFormat bool   `json:"preserve_format"` // 可选参数，是否保持原格式
	BatchSize      int    `json:"batch_size"`      // 可选参数，批处理大小
}

// TranslateSubtitleResponse - Subtitle translation response
type TranslateSubtitleResponse struct {
	TranslatedFile   string                 `json:"translated_file,omitempty"`   // 翻译后的字幕文件路径
	OriginalLines    int                    `json:"original_lines,omitempty"`    // 原始字幕行数
	TranslatedLines  int                    `json:"translated_lines,omitempty"`  // 翻译后字幕行数
	ProcessingTime   int                    `json:"processing_time,omitempty"`   // 处理时间（秒）
	TranslationStats map[string]interface{} `json:"translation_stats,omitempty"` // 翻译统计信息
}

func (h *TranslateSubtitleHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "TranslateSubtitleHandler")
	defer func() {
		LogResponse(r, "TranslateSubtitleHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req TranslateSubtitleRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate required parameters
	if strings.TrimSpace(req.SubtitleFile) == "" {
		RespondError(w, "The parameter subtitle_file is required")
		return
	}

	if strings.TrimSpace(req.SourceLanguage) == "" {
		RespondError(w, "The parameter source_language is required")
		return
	}

	if strings.TrimSpace(req.TargetLanguage) == "" {
		RespondError(w, "The parameter target_language is required")
		return
	}

	// Validate language codes
	if !ValidateLanguageCode(req.SourceLanguage) {
		RespondError(w, "Invalid source_language code")
		return
	}

	if !ValidateLanguageCode(req.TargetLanguage) {
		RespondError(w, "Invalid target_language code")
		return
	}

	// Check if source and target languages are the same
	if req.SourceLanguage == req.TargetLanguage {
		RespondError(w, "Source and target languages cannot be the same")
		return
	}

	// Validate translation type
	if !ValidateTranslationType(req.TranslateType) {
		RespondError(w, "Invalid translate_type parameter")
		return
	}

	// Check if subtitle file exists
	if !fileExists(req.SubtitleFile) {
		RespondError(w, "Subtitle file does not exist")
		return
	}

	// Validate subtitle file format
	if !IsSubtitleFile(req.SubtitleFile) {
		RespondError(w, "Invalid subtitle file format. Supported formats: .srt, .vtt, .ass, .ssa")
		return
	}

	// Check translation service availability
	if !isTranslationServiceAvailable(req.TranslateType, req.TargetLanguage) {
		RespondServiceError(w, "Translation service is not available for the specified language pair")
		return
	}

	// Set default values
	if req.BatchSize <= 0 {
		req.BatchSize = 10 // Default batch size
	}

	// Generate task ID
	taskID := GenerateTaskID()

	// Create translation task
	task := map[string]interface{}{
		"task_id":         taskID,
		"user_id":         userID,
		"task_type":       "translate_subtitle",
		"subtitle_file":   req.SubtitleFile,
		"source_language": req.SourceLanguage,
		"target_language": req.TargetLanguage,
		"translate_type":  req.TranslateType,
		"only_translate":  req.OnlyTranslate,
		"output_dir":      req.OutputDir,
		"preserve_format": req.PreserveFormat,
		"batch_size":      req.BatchSize,
		"status":          "queued",
		"created_at":      time.Now().Unix(),
	}

	// Publish task to queue
	err := h.publishTranslationTask(task)
	if err != nil {
		RespondTaskError(w, fmt.Sprintf("Failed to queue translation task: %s", err.Error()))
		return
	}

	// Return task ID for status tracking
	RespondTaskSuccess(w, taskID)
}

// Helper functions moved to common.go

func (h *TranslateSubtitleHandler) publishTranslationTask(task map[string]interface{}) error {
	// Simulate publishing task to message queue
	// In real implementation, publish to AMQP/Redis queue for processing

	taskID := task["task_id"].(string)
	userID := task["user_id"].(string)
	subtitleFile := task["subtitle_file"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the task (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("Translation task created",
		"task_id", taskID,
		"user_id", userID,
		"task_type", "translate_subtitle",
		"file", subtitleFile,
		"timestamp", time.Now().Unix(),
	)

	return nil
}

// ValidateSubtitleContent - Validate subtitle file content
func ValidateSubtitleContent(filePath string) error {
	// Read and validate subtitle file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read subtitle file: %w", err)
	}

	if len(content) == 0 {
		return fmt.Errorf("subtitle file is empty")
	}

	// Basic validation based on file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".srt":
		return validateSRTContent(string(content))
	case ".vtt":
		return validateVTTContent(string(content))
	case ".ass", ".ssa":
		return validateASSContent(string(content))
	default:
		return fmt.Errorf("unsupported subtitle format: %s", ext)
	}
}

func validateSRTContent(content string) error {
	// Basic SRT format validation
	lines := strings.Split(content, "\n")
	if len(lines) < 3 {
		return fmt.Errorf("invalid SRT format: too few lines")
	}

	// Check for basic SRT structure (sequence number, timestamp, text)
	hasSequence := false
	hasTimestamp := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Check for sequence number (just digits)
		if strings.Contains(line, "-->") {
			hasTimestamp = true
		} else if len(line) > 0 && isNumeric(line) {
			hasSequence = true
		}
	}

	if !hasSequence || !hasTimestamp {
		return fmt.Errorf("invalid SRT format: missing sequence numbers or timestamps")
	}

	return nil
}

func validateVTTContent(content string) error {
	// Basic VTT format validation
	if !strings.HasPrefix(content, "WEBVTT") {
		return fmt.Errorf("invalid VTT format: missing WEBVTT header")
	}
	return nil
}

func validateASSContent(content string) error {
	// Basic ASS/SSA format validation
	if !strings.Contains(content, "[Script Info]") {
		return fmt.Errorf("invalid ASS/SSA format: missing [Script Info] section")
	}
	return nil
}

func isNumeric(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return len(s) > 0
}

// GetSupportedTranslationServices - Get list of available translation services
func GetSupportedTranslationServices() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":          0,
			"name":        "Google Translate",
			"description": "Google Translation API",
			"languages":   []string{"zh-cn", "zh-tw", "en", "fr", "de", "ja", "ko", "ru", "es"},
		},
		{
			"id":          1,
			"name":        "Baidu Translate",
			"description": "Baidu Translation API",
			"languages":   []string{"zh-cn", "en", "ja", "ko", "fr", "de", "ru", "es"},
		},
		{
			"id":          2,
			"name":        "DeepL",
			"description": "DeepL Translation API",
			"languages":   []string{"en", "de", "fr", "es", "it", "pt", "ru", "ja", "zh-cn"},
		},
		// Add more translation services as needed
	}
}
