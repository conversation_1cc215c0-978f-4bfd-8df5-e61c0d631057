package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// TTSHandler - Text to Speech handler (第1个接口)
type TTSHandler struct {
	BaseHandler
}

// TTSRequest - TTS request structure following Python API
type TTSRequest struct {
	Text        string `json:"text"`         // 必须参数，要合成语音的文本内容
	VoiceRole   string `json:"voice_role"`   // 必须参数，配音角色名称
	VoiceRate   string `json:"voice_rate"`   // 可选参数，语速调节，格式：+数字% 或 -数字%
	VoiceVolume string `json:"voice_volume"` // 可选参数，音量调节，格式：+数字% 或 -数字%
	VoicePitch  string `json:"voice_pitch"`  // 可选参数，音调调节，格式：+数字Hz 或 -数字Hz
	TTSType     int    `json:"tts_type"`     // 必须参数，TTS类型 0-17
	Language    string `json:"language"`     // 可选参数，语言代码
	Filename    string `json:"filename"`     // 可选参数，输出文件名
	IsPreview   bool   `json:"is_preview"`   // 可选参数，是否为预览模式
}

// TTSResponse - TTS response structure
type TTSResponse struct {
	AudioFile string `json:"audio_file,omitempty"` // 生成的音频文件路径
	Duration  int    `json:"duration,omitempty"`   // 音频时长（秒）
	FileSize  int64  `json:"file_size,omitempty"`  // 文件大小（字节）
}

func (h *TTSHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "TTSHandler")
	defer func() {
		LogResponse(r, "TTSHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req TTSRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate required parameters
	if strings.TrimSpace(req.Text) == "" {
		RespondError(w, "The parameter text is required")
		return
	}

	if strings.TrimSpace(req.VoiceRole) == "" {
		RespondError(w, "The parameter voice_role is required")
		return
	}

	// Validate TTS type
	if !ValidateTTSType(req.TTSType) {
		RespondError(w, "Invalid tts_type parameter")
		return
	}

	// Set default values
	if req.VoiceRate == "" {
		req.VoiceRate = "+0%"
	}
	if req.VoiceVolume == "" {
		req.VoiceVolume = "+0%"
	}
	if req.VoicePitch == "" {
		req.VoicePitch = "+0Hz"
	}

	// Validate rate, volume, pitch formats
	if !isValidRateFormat(req.VoiceRate) {
		RespondError(w, "Invalid voice_rate format. Use +数字% or -数字%")
		return
	}
	if !isValidVolumeFormat(req.VoiceVolume) {
		RespondError(w, "Invalid voice_volume format. Use +数字% or -数字%")
		return
	}
	if !isValidPitchFormat(req.VoicePitch) {
		RespondError(w, "Invalid voice_pitch format. Use +数字Hz or -数字Hz")
		return
	}

	// Validate language if provided
	if req.Language != "" && !ValidateLanguageCode(req.Language) {
		RespondError(w, "Invalid language code")
		return
	}

	// Check TTS service availability (simulate service check)
	if !isTTSServiceAvailable(req.TTSType) {
		RespondServiceError(w, "TTS service is not available or not configured")
		return
	}

	// Generate task ID
	taskID := GenerateTaskID()

	// Create TTS task (simulate task creation)
	task := map[string]interface{}{
		"task_id":      taskID,
		"user_id":      userID,
		"task_type":    "tts",
		"text":         req.Text,
		"voice_role":   req.VoiceRole,
		"voice_rate":   req.VoiceRate,
		"voice_volume": req.VoiceVolume,
		"voice_pitch":  req.VoicePitch,
		"tts_type":     req.TTSType,
		"language":     req.Language,
		"filename":     req.Filename,
		"is_preview":   req.IsPreview,
		"status":       "queued",
		"created_at":   time.Now().Unix(),
	}

	// Publish task to queue (simulate message publishing)
	err := h.publishTTSTask(task)
	if err != nil {
		RespondTaskError(w, fmt.Sprintf("Failed to queue TTS task: %s", err.Error()))
		return
	}

	// Return task ID for status tracking
	RespondTaskSuccess(w, taskID)
}

// Helper functions moved to common.go

func (h *TTSHandler) publishTTSTask(task map[string]interface{}) error {
	// Simulate publishing task to message queue
	// In real implementation, publish to AMQP/Redis queue for processing

	// Log the task creation
	taskID := task["task_id"].(string)
	userID := task["user_id"].(string)
	text := task["text"].(string)

	// Use PublishDirectToQueue method from PublisherInterface
	// For now, just log the task (in real implementation, publish to queue)
	logger.DefaultLogger.Infow("TTS task created",
		"task_id", taskID,
		"user_id", userID,
		"task_type", "tts",
		"text", text,
		"timestamp", time.Now().Unix(),
	)

	// Simulate task status file creation (following Python API pattern)
	// In real implementation, save task status to database or file system
	return nil
}

// GetTTSVoices - Get available voices for TTS type
func GetTTSVoices(ttsType int, language string) []map[string]interface{} {
	// Simulate voice list based on TTS type and language
	// In real implementation, query actual TTS service for available voices

	voices := []map[string]interface{}{}

	switch ttsType {
	case 0: // Edge-TTS
		if language == "zh-cn" || language == "" {
			voices = append(voices, map[string]interface{}{
				"name":     "zh-CN-YunjianNeural",
				"gender":   "Male",
				"language": "zh-cn",
			})
			voices = append(voices, map[string]interface{}{
				"name":     "zh-CN-XiaoxiaoNeural",
				"gender":   "Female",
				"language": "zh-cn",
			})
		}
		if language == "en" || language == "" {
			voices = append(voices, map[string]interface{}{
				"name":     "en-US-JennyNeural",
				"gender":   "Female",
				"language": "en",
			})
		}
	case 1: // CosyVoice
		voices = append(voices, map[string]interface{}{
			"name":     "中文女",
			"gender":   "Female",
			"language": "zh-cn",
		})
		// Add more TTS types as needed
	}

	return voices
}
