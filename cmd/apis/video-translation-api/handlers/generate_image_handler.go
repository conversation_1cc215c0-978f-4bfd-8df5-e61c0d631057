package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/logger"
)

// GenerateImageHandler handles image generation requests
type GenerateImageHandler struct {
	BaseHandler
}

// GenerateImageRequest represents the request structure for image generation
type GenerateImageRequest struct {
	Prompt string `json:"prompt" validate:"required"`
	Size   string `json:"size,omitempty"`
}

// GenerateImageResponse represents the response structure for image generation
type GenerateImageResponse struct {
	ImageURL string `json:"image_url"`
}

// BytePlusImageRequest represents the request structure for BytePlus ModelArk API
type BytePlusImageRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	ResponseFormat string `json:"response_format"`
	Size           string `json:"size"`
	GuidanceScale  int    `json:"guidance_scale"`
	Watermark      bool   `json:"watermark"`
}

// BytePlusImageResponse represents the response structure from BytePlus ModelArk API
type BytePlusImageResponse struct {
	Data []struct {
		URL string `json:"url"`
	} `json:"data"`
	Error *struct {
		Message string `json:"message"`
	} `json:"error,omitempty"`
}

// ServeHTTP handles the image generation request
func (h *GenerateImageHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Parse request
	var req GenerateImageRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		RespondError(w, "Invalid JSON format")
		return
	}

	// Validate required fields
	if strings.TrimSpace(req.Prompt) == "" {
		RespondError(w, "Prompt is required")
		return
	}

	// Set default size if not provided
	if req.Size == "" {
		req.Size = "1024x1024"
	}

	// Validate size format (optional validation)
	validSizes := []string{"1024x1024", "1024x1792", "1792x1024", "512x512", "768x768"}
	isValidSize := false
	for _, size := range validSizes {
		if req.Size == size {
			isValidSize = true
			break
		}
	}
	if !isValidSize {
		logger.DefaultLogger.Warnw("Non-standard image size requested", "size", req.Size)
	}

	// Get API key from configuration (this would need to be implemented)
	apiKey := h.getAPIKey("byteplus_modelark")
	if apiKey == "" {
		RespondError(w, "BytePlus ModelArk API key not configured. Please set byteplus_modelark_key in configuration.")
		return
	}

	// Prepare BytePlus API request
	bytePlusReq := BytePlusImageRequest{
		Model:          "seedream-3-0-t2i-250415",
		Prompt:         req.Prompt,
		ResponseFormat: "url",
		Size:           req.Size,
		GuidanceScale:  3,
		Watermark:      false,
	}

	// Convert to JSON
	reqBody, err := json.Marshal(bytePlusReq)
	if err != nil {
		logger.DefaultLogger.Errorw("Failed to marshal BytePlus request", "error", err)
		RespondError(w, "Failed to prepare API request")
		return
	}

	// Make API request to BytePlus
	apiURL := "https://ark.ap-southeast.bytepluses.com/api/v3/images/generations"
	httpReq, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	if err != nil {
		logger.DefaultLogger.Errorw("Failed to create HTTP request", "error", err)
		RespondError(w, "Failed to create API request")
		return
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+apiKey)
	httpReq.Header.Set("User-Agent", "VideoTranslationAPI/1.0")

	// Execute request
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		logger.DefaultLogger.Errorw("BytePlus API request failed", "error", err)
		RespondError(w, fmt.Sprintf("Network error: %s", err.Error()))
		return
	}
	defer resp.Body.Close()

	// Parse response
	var bytePlusResp BytePlusImageResponse
	if err := json.NewDecoder(resp.Body).Decode(&bytePlusResp); err != nil {
		logger.DefaultLogger.Errorw("Failed to parse BytePlus response", "error", err)
		RespondError(w, "Failed to parse API response")
		return
	}

	// Check for API errors
	if resp.StatusCode != http.StatusOK {
		errorMsg := fmt.Sprintf("API request failed with status %d", resp.StatusCode)
		if bytePlusResp.Error != nil && bytePlusResp.Error.Message != "" {
			errorMsg = bytePlusResp.Error.Message
		}
		logger.DefaultLogger.Errorw("BytePlus API error", "status", resp.StatusCode, "error", errorMsg)
		RespondError(w, errorMsg)
		return
	}

	// Validate response format
	if len(bytePlusResp.Data) == 0 {
		RespondError(w, "Invalid response format from API")
		return
	}

	// Get image URL
	imageURL := bytePlusResp.Data[0].URL
	if imageURL == "" {
		RespondError(w, "No image URL in response")
		return
	}

	// Log successful generation
	logger.DefaultLogger.Infow("Image generated successfully",
		"prompt", req.Prompt[:min(100, len(req.Prompt))],
		"size", req.Size,
		"image_url", imageURL,
	)

	// Return success response
	response := GenerateImageResponse{
		ImageURL: imageURL,
	}

	RespondSuccess(w, response)
}

// getAPIKey retrieves API key from configuration
// This is a placeholder - in real implementation, this would fetch from your config system
func (h *GenerateImageHandler) getAPIKey(service string) string {
	// TODO: Implement actual configuration retrieval
	// This would typically fetch from your configuration management system
	// For now, return empty string to indicate missing configuration
	return ""
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
