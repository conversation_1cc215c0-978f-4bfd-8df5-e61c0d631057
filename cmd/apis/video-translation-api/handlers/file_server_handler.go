package handlers

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// FileServerHandler - File serving handler
type FileServerHandler struct {
	BaseHandler
}

func (h *FileServerHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "FileServerHandler")
	defer func() {
		LogResponse(r, "FileServerHandler", start, http.StatusOK)
	}()

	// Only allow GET method
	if r.Method != http.MethodGet {
		RespondError(w, "Method not allowed")
		return
	}

	// Extract file path from URL
	filePath := strings.TrimPrefix(r.URL.Path, "/video-translation/file/")
	if filePath == "" {
		RespondError(w, "File path is required")
		return
	}

	// Security: prevent directory traversal
	if strings.Contains(filePath, "..") || strings.Contains(filePath, "~") {
		RespondError(w, "Invalid file path")
		return
	}

	// Check if file exists
	if !fileExists(filePath) {
		RespondNoResults(w, "File not found")
		return
	}

	// Get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		RespondError(w, "Failed to get file information")
		return
	}

	// Check if it's a directory
	if fileInfo.IsDir() {
		RespondError(w, "Cannot serve directories")
		return
	}

	// Set appropriate content type
	contentType := getContentType(filePath)
	w.Header().Set("Content-Type", contentType)

	// Set content disposition for downloads
	filename := filepath.Base(filePath)
	w.Header().Set("Content-Disposition", `attachment; filename="`+filename+`"`)

	// Set cache headers
	w.Header().Set("Cache-Control", "public, max-age=3600") // 1 hour cache

	// Serve the file
	http.ServeFile(w, r, filePath)
}

func getContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	
	contentTypes := map[string]string{
		// Video formats
		".mp4":  "video/mp4",
		".avi":  "video/x-msvideo",
		".mov":  "video/quicktime",
		".mkv":  "video/x-matroska",
		".wmv":  "video/x-ms-wmv",
		".flv":  "video/x-flv",
		".webm": "video/webm",
		".m4v":  "video/x-m4v",
		
		// Audio formats
		".mp3":  "audio/mpeg",
		".wav":  "audio/wav",
		".flac": "audio/flac",
		".aac":  "audio/aac",
		".ogg":  "audio/ogg",
		".wma":  "audio/x-ms-wma",
		".m4a":  "audio/mp4",
		".opus": "audio/opus",
		
		// Subtitle formats
		".srt": "text/plain",
		".vtt": "text/vtt",
		".ass": "text/plain",
		".ssa": "text/plain",
		
		// Text formats
		".txt":  "text/plain",
		".json": "application/json",
		
		// Image formats
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".gif":  "image/gif",
		".webp": "image/webp",
	}
	
	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}
	
	return "application/octet-stream"
}

// HealthHandler - Health check handler
type HealthHandler struct{}

func (h *HealthHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "HealthHandler")
	defer func() {
		LogResponse(r, "HealthHandler", start, http.StatusOK)
	}()

	// Only allow GET method
	if r.Method != http.MethodGet {
		RespondError(w, "Method not allowed")
		return
	}

	// Basic health check
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0",
		"services": map[string]interface{}{
			"api":      "healthy",
			"database": "healthy", // In real implementation, check database connection
			"queue":    "healthy", // In real implementation, check message queue
			"storage":  "healthy", // In real implementation, check file storage
		},
		"uptime": time.Since(start).Seconds(),
	}

	RespondSuccess(w, health)
}

// InfoHandler - API information handler
type InfoHandler struct{}

func (h *InfoHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "InfoHandler")
	defer func() {
		LogResponse(r, "InfoHandler", start, http.StatusOK)
	}()

	// Only allow GET method
	if r.Method != http.MethodGet {
		RespondError(w, "Method not allowed")
		return
	}

	info := map[string]interface{}{
		"api_name":    "Video Translation API",
		"version":     "1.0.0",
		"description": "Complete video translation service with speech recognition, translation, and TTS",
		"endpoints": []map[string]interface{}{
			{
				"path":        "/tts",
				"method":      "POST",
				"description": "Text to Speech synthesis",
			},
			{
				"path":        "/translate_srt",
				"method":      "POST",
				"description": "Subtitle translation",
			},
			{
				"path":        "/recogn",
				"method":      "POST",
				"description": "Speech recognition",
			},
			{
				"path":        "/trans_video",
				"method":      "POST",
				"description": "Complete video translation",
			},
			{
				"path":        "/voice_list",
				"method":      "GET",
				"description": "Get available voices",
			},
			{
				"path":        "/upload",
				"method":      "POST",
				"description": "File upload",
			},
			{
				"path":        "/task_status",
				"method":      "GET/POST",
				"description": "Get task status",
			},
			{
				"path":        "/batch_process",
				"method":      "POST",
				"description": "Batch video processing",
			},
		},
		"supported_formats": map[string]interface{}{
			"video":    []string{".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm", ".m4v"},
			"audio":    []string{".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a", ".opus"},
			"subtitle": []string{".srt", ".vtt", ".ass", ".ssa"},
		},
		"supported_languages": []string{
			"zh-cn", "zh-tw", "en", "fr", "de", "ja", "ko", "ru", "es", "th",
			"it", "pt", "vi", "ar", "tr", "hi", "hu", "uk", "id", "ms",
			"kk", "cs", "pl", "nl", "sv",
		},
		"limits": map[string]interface{}{
			"max_file_size":        "2GB",
			"max_batch_files":      50,
			"max_concurrent_tasks": 10,
			"request_timeout":      "30 minutes",
		},
		"timestamp": time.Now().Unix(),
	}

	RespondSuccess(w, info)
}

// StatsHandler - API statistics handler
type StatsHandler struct {
	BaseHandler
}

func (h *StatsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "StatsHandler")
	defer func() {
		LogResponse(r, "StatsHandler", start, http.StatusOK)
	}()

	// Only allow GET method
	if r.Method != http.MethodGet {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context (optional for stats)
	userID := GetUserID(r)

	// Mock statistics (in real implementation, query from database)
	stats := map[string]interface{}{
		"total_tasks":      1250,
		"completed_tasks":  1100,
		"failed_tasks":     50,
		"processing_tasks": 100,
		"queued_tasks":     0,
		"task_types": map[string]interface{}{
			"video_translation":  800,
			"speech_recognition": 200,
			"translate_subtitle": 150,
			"tts":               100,
		},
		"popular_languages": map[string]interface{}{
			"zh-cn": 500,
			"en":    400,
			"ja":    200,
			"ko":    150,
		},
		"average_processing_time": map[string]interface{}{
			"video_translation":  "15 minutes",
			"speech_recognition": "5 minutes",
			"translate_subtitle": "2 minutes",
			"tts":               "1 minute",
		},
		"timestamp": time.Now().Unix(),
	}

	// Add user-specific stats if user is authenticated
	if userID != "" {
		stats["user_stats"] = map[string]interface{}{
			"user_id":        userID,
			"total_tasks":    25,
			"completed_tasks": 20,
			"failed_tasks":   2,
			"processing_tasks": 3,
		}
	}

	RespondSuccess(w, stats)
}
