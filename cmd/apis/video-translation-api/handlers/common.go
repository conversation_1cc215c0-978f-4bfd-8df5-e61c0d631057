package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/nhdms/base-go/pkg/app"
	"github.com/nhdms/base-go/pkg/logger"
	transhttp "github.com/nhdms/base-go/pkg/transport"
	"go-micro.dev/v5/client"
)

// Common response structures following Python API patterns
type APIResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

type TaskResponse struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	TaskID string `json:"task_id,omitempty"`
}

type BatchResponse struct {
	Code       int      `json:"code"`
	Msg        string   `json:"msg"`
	BatchID    string   `json:"batch_id,omitempty"`
	TaskIDs    []string `json:"task_ids,omitempty"`
	BatchName  string   `json:"batch_name,omitempty"`
	TotalFiles int      `json:"total_files,omitempty"`
}

// Common handler struct
type BaseHandler struct {
	Producer app.PublisherInterface
	Client   client.Client
}

// Helper functions following Python API patterns

// RespondSuccess - Success response with code 0
func RespondSuccess(w http.ResponseWriter, data interface{}) {
	response := APIResponse{
		Code: 0,
		Msg:  "ok",
		Data: data,
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}

// RespondTaskSuccess - Task creation success response
func RespondTaskSuccess(w http.ResponseWriter, taskID string) {
	response := TaskResponse{
		Code:   0,
		Msg:    "ok",
		TaskID: taskID,
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}

// RespondBatchSuccess - Batch processing success response
func RespondBatchSuccess(w http.ResponseWriter, batchID string, taskIDs []string, batchName string) {
	response := BatchResponse{
		Code:       0,
		Msg:        "ok",
		BatchID:    batchID,
		TaskIDs:    taskIDs,
		BatchName:  batchName,
		TotalFiles: len(taskIDs),
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}

// RespondError - Error response with code 1
func RespondError(w http.ResponseWriter, message string) {
	response := APIResponse{
		Code: 1,
		Msg:  message,
	}
	transhttp.RespondJSON(w, http.StatusBadRequest, response)
}

// RespondServiceError - Service unavailable error with code 5
func RespondServiceError(w http.ResponseWriter, message string) {
	response := APIResponse{
		Code: 5,
		Msg:  message,
	}
	transhttp.RespondJSON(w, http.StatusServiceUnavailable, response)
}

// RespondTaskError - Task error with code 3
func RespondTaskError(w http.ResponseWriter, message string) {
	response := APIResponse{
		Code: 3,
		Msg:  message,
	}
	transhttp.RespondJSON(w, http.StatusInternalServerError, response)
}

// RespondNoResults - No results error with code 4
func RespondNoResults(w http.ResponseWriter, message string) {
	response := APIResponse{
		Code: 4,
		Msg:  message,
	}
	transhttp.RespondJSON(w, http.StatusNotFound, response)
}

// ParseJSONBody - Parse JSON request body
func ParseJSONBody(r *http.Request, target interface{}) error {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return fmt.Errorf("failed to read request body: %w", err)
	}
	defer r.Body.Close()

	if len(body) == 0 {
		return fmt.Errorf("request body is empty")
	}

	err = json.Unmarshal(body, target)
	if err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	return nil
}

// GetUserID - Extract user ID from request context (following Python API pattern)
func GetUserID(r *http.Request) string {
	// Check X-USER-ID header (legacy support)
	if userID := r.Header.Get("X-USER-ID"); userID != "" {
		return strings.TrimSpace(userID)
	}

	// Check Authorization header for JWT
	if auth := r.Header.Get("Authorization"); auth != "" {
		if strings.HasPrefix(auth, "Bearer ") {
			// TODO: Parse JWT token and extract user ID
			// For now, return a placeholder
			return "user_from_jwt"
		}
	}

	// Check X-API-KEY header
	if apiKey := r.Header.Get("X-API-KEY"); apiKey != "" {
		// TODO: Look up user by API key
		// For now, return a placeholder
		return "user_from_api_key"
	}

	return ""
}

// GenerateTaskID - Generate UUID for task tracking
func GenerateTaskID() string {
	// Simple timestamp-based ID for now
	// In production, use proper UUID generation
	return fmt.Sprintf("task_%d_%d", time.Now().UnixNano(), time.Now().Unix())
}

// GenerateBatchID - Generate UUID for batch tracking
func GenerateBatchID() string {
	return fmt.Sprintf("batch_%d", time.Now().UnixNano())
}

// ValidateLanguageCode - Validate language codes (following Python API pattern)
func ValidateLanguageCode(code string) bool {
	validCodes := map[string]bool{
		"zh-cn": true, "zh-tw": true, "en": true, "fr": true, "de": true,
		"ja": true, "ko": true, "ru": true, "es": true, "th": true,
		"it": true, "pt": true, "vi": true, "ar": true, "tr": true,
		"hi": true, "hu": true, "uk": true, "id": true, "ms": true,
		"kk": true, "cs": true, "pl": true, "nl": true, "sv": true,
	}
	return validCodes[code]
}

// ValidateTTSType - Validate TTS type (following Python API pattern)
func ValidateTTSType(ttsType int) bool {
	return ttsType >= 0 && ttsType <= 17 // Based on Python API documentation
}

// ValidateRecognitionType - Validate recognition type
func ValidateRecognitionType(recognType int) bool {
	return recognType >= 0 && recognType <= 6 // Based on Python API documentation
}

// ValidateTranslationType - Validate translation type
func ValidateTranslationType(transType int) bool {
	return transType >= 0 && transType <= 20 // Assuming reasonable range
}

// IsVideoFile - Check if file is a video file
func IsVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	videoExts := map[string]bool{
		".mp4": true, ".avi": true, ".mov": true, ".mkv": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
		".3gp": true, ".ogv": true, ".ts": true, ".mts": true,
	}
	return videoExts[ext]
}

// IsAudioFile - Check if file is an audio file
func IsAudioFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	audioExts := map[string]bool{
		".mp3": true, ".wav": true, ".flac": true, ".aac": true,
		".ogg": true, ".wma": true, ".m4a": true, ".opus": true,
	}
	return audioExts[ext]
}

// IsSubtitleFile - Check if file is a subtitle file
func IsSubtitleFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".srt" || ext == ".vtt" || ext == ".ass" || ext == ".ssa"
}

// ParseIntParam - Parse integer parameter with default value
func ParseIntParam(value string, defaultValue int) int {
	if value == "" {
		return defaultValue
	}
	if parsed, err := strconv.Atoi(value); err == nil {
		return parsed
	}
	return defaultValue
}

// ParseBoolParam - Parse boolean parameter with default value
func ParseBoolParam(value string, defaultValue bool) bool {
	if value == "" {
		return defaultValue
	}
	if parsed, err := strconv.ParseBool(value); err == nil {
		return parsed
	}
	return defaultValue
}

// LogRequest - Log request details (following Python API pattern)
func LogRequest(r *http.Request, handler string) {
	start := time.Now()
	logger.DefaultLogger.Debugw("Processing request",
		"handler", handler,
		"method", r.Method,
		"url", r.URL.Path,
		"user_agent", r.Header.Get("User-Agent"),
		"remote_addr", r.RemoteAddr,
		"started_at", start,
	)
}

// LogResponse - Log response details
func LogResponse(r *http.Request, handler string, start time.Time, statusCode int) {
	duration := time.Since(start)
	logger.DefaultLogger.Debugw("Request completed",
		"handler", handler,
		"method", r.Method,
		"url", r.URL.Path,
		"status_code", statusCode,
		"duration_ms", duration.Milliseconds(),
		"duration", duration.String(),
	)
}

// Additional utility functions

// fileExists - Check if file exists
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// getDefaultModelName - Get default model name for recognition type
func getDefaultModelName(recognType int) string {
	defaultModels := map[int]string{
		0: "base",      // Faster-Whisper
		1: "base",      // OpenAI-Whisper
		2: "default",   // Google Speech
		3: "whisper-1", // OpenAI API
		4: "base",      // 302.AI
		5: "base",      // zh_recogn
		6: "base",      // doubao
	}

	if model, exists := defaultModels[recognType]; exists {
		return model
	}
	return "base"
}

// isValidRateFormat - Validate voice rate format (+/-N%)
func isValidRateFormat(rate string) bool {
	if rate == "" {
		return true
	}
	// Should match pattern like "+50%", "-25%", "+0%"
	if len(rate) < 3 {
		return false
	}
	if rate[0] != '+' && rate[0] != '-' {
		return false
	}
	if rate[len(rate)-1] != '%' {
		return false
	}
	// Check if middle part is a number
	_, err := strconv.Atoi(rate[1 : len(rate)-1])
	return err == nil
}

// isValidVolumeFormat - Validate volume format (+/-NdB or +/-N%)
func isValidVolumeFormat(volume string) bool {
	if volume == "" {
		return true
	}
	if len(volume) < 3 {
		return false
	}
	if volume[0] != '+' && volume[0] != '-' {
		return false
	}

	// Check for dB or % suffix
	if strings.HasSuffix(volume, "dB") {
		_, err := strconv.Atoi(volume[1 : len(volume)-2])
		return err == nil
	} else if strings.HasSuffix(volume, "%") {
		_, err := strconv.Atoi(volume[1 : len(volume)-1])
		return err == nil
	}

	return false
}

// isValidPitchFormat - Validate pitch format (+/-NHz)
func isValidPitchFormat(pitch string) bool {
	if pitch == "" {
		return true
	}
	if len(pitch) < 4 {
		return false
	}
	if pitch[0] != '+' && pitch[0] != '-' {
		return false
	}
	if !strings.HasSuffix(pitch, "Hz") {
		return false
	}
	// Check if middle part is a number
	_, err := strconv.Atoi(pitch[1 : len(pitch)-2])
	return err == nil
}

// Service availability checks (mock implementations)
func isRecognitionServiceAvailable(recognType int, language string) bool {
	// Mock service availability check
	// In real implementation, check service health and language support
	return true
}

func isTranslationServiceAvailable(translateType int, language string) bool {
	// Mock service availability check
	return true
}

func isTTSServiceAvailable(ttsType int) bool {
	// Mock service availability check
	return true
}
