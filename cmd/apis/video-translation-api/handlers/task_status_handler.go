package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"time"
)

// TaskStatusHandler - Task status handler
type TaskStatusHandler struct {
	BaseHandler
}

// TaskStatusRequest - Task status request structure
type TaskStatusRequest struct {
	TaskID string `json:"task_id"` // 必须参数，任务ID
}

// TaskStatusResponse - Task status response structure
type TaskStatusResponse struct {
	TaskID         string                 `json:"task_id"`                   // 任务ID
	Status         string                 `json:"status"`                    // 任务状态
	Progress       int                    `json:"progress"`                  // 进度百分比 0-100
	Message        string                 `json:"message,omitempty"`         // 状态消息
	StartTime      int64                  `json:"start_time,omitempty"`      // 开始时间戳
	EndTime        int64                  `json:"end_time,omitempty"`        // 结束时间戳
	ProcessingTime int                    `json:"processing_time,omitempty"` // 处理时间（秒）
	Result         map[string]interface{} `json:"result,omitempty"`          // 处理结果
	Error          string                 `json:"error,omitempty"`           // 错误信息
	Logs           []string               `json:"logs,omitempty"`            // 处理日志
}

func (h *TaskStatusHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "TaskStatusHandler")
	defer func() {
		LogResponse(r, "TaskStatusHandler", start, http.StatusOK)
	}()

	var taskID string

	// Handle both GET and POST methods
	switch r.Method {
	case http.MethodPost:
		// Parse JSON body for POST requests
		var req TaskStatusRequest
		if err := ParseJSONBody(r, &req); err != nil {
			RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
			return
		}
		taskID = req.TaskID

	case http.MethodGet:
		// Parse query parameter for GET requests
		taskID = r.URL.Query().Get("task_id")

	default:
		RespondError(w, "Method not allowed")
		return
	}

	// Validate task ID
	if strings.TrimSpace(taskID) == "" {
		RespondError(w, "The parameter task_id is required")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Get task status
	status, err := h.getTaskStatus(taskID, userID)
	if err != nil {
		if err.Error() == "task not found" {
			RespondNoResults(w, "Task not found")
			return
		}
		RespondTaskError(w, fmt.Sprintf("Failed to get task status: %s", err.Error()))
		return
	}

	// Return task status
	RespondSuccess(w, status)
}

func (h *TaskStatusHandler) getTaskStatus(taskID, userID string) (*TaskStatusResponse, error) {
	// Simulate task status retrieval
	// In real implementation, query database or file system for task status
	
	// Mock task status based on task ID pattern
	status := &TaskStatusResponse{
		TaskID: taskID,
	}

	// Simulate different task states
	if strings.Contains(taskID, "completed") {
		status.Status = "completed"
		status.Progress = 100
		status.Message = "Task completed successfully"
		status.StartTime = time.Now().Add(-10*time.Minute).Unix()
		status.EndTime = time.Now().Unix()
		status.ProcessingTime = 600 // 10 minutes
		status.Result = map[string]interface{}{
			"output_file": "/path/to/output/file.mp4",
			"duration":    120,
			"file_size":   1024000,
		}
	} else if strings.Contains(taskID, "failed") {
		status.Status = "failed"
		status.Progress = 50
		status.Message = "Task failed"
		status.Error = "Processing error occurred"
		status.StartTime = time.Now().Add(-5*time.Minute).Unix()
		status.EndTime = time.Now().Unix()
		status.ProcessingTime = 300
	} else if strings.Contains(taskID, "processing") {
		status.Status = "processing"
		status.Progress = 75
		status.Message = "Processing video translation..."
		status.StartTime = time.Now().Add(-8*time.Minute).Unix()
		status.Logs = []string{
			"Started speech recognition",
			"Recognition completed",
			"Started translation",
			"Translation in progress...",
		}
	} else {
		// Default to queued status
		status.Status = "queued"
		status.Progress = 0
		status.Message = "Task is queued for processing"
		status.StartTime = time.Now().Unix()
	}

	return status, nil
}

// BatchStatusHandler - Batch status handler for multiple tasks
type BatchStatusHandler struct {
	BaseHandler
}

// BatchStatusRequest - Batch status request structure
type BatchStatusRequest struct {
	TaskIDs []string `json:"task_ids"` // 任务ID列表
	BatchID string   `json:"batch_id"` // 批处理ID（可选）
}

// BatchStatusResponse - Batch status response structure
type BatchStatusResponse struct {
	BatchID     string                          `json:"batch_id,omitempty"`     // 批处理ID
	TotalTasks  int                             `json:"total_tasks"`            // 总任务数
	Completed   int                             `json:"completed"`              // 已完成任务数
	Failed      int                             `json:"failed"`                 // 失败任务数
	Processing  int                             `json:"processing"`             // 处理中任务数
	Queued      int                             `json:"queued"`                 // 排队中任务数
	Progress    int                             `json:"progress"`               // 整体进度百分比
	Tasks       map[string]*TaskStatusResponse  `json:"tasks"`                  // 各任务状态详情
}

func (h *BatchStatusHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	LogRequest(r, "BatchStatusHandler")
	defer func() {
		LogResponse(r, "BatchStatusHandler", start, http.StatusOK)
	}()

	// Only allow POST method
	if r.Method != http.MethodPost {
		RespondError(w, "Method not allowed")
		return
	}

	// Get user ID from request context
	userID := GetUserID(r)
	if userID == "" {
		RespondError(w, "User authentication required")
		return
	}

	// Parse request body
	var req BatchStatusRequest
	if err := ParseJSONBody(r, &req); err != nil {
		RespondError(w, fmt.Sprintf("Invalid request body: %s", err.Error()))
		return
	}

	// Validate parameters
	if len(req.TaskIDs) == 0 && req.BatchID == "" {
		RespondError(w, "Either task_ids or batch_id is required")
		return
	}

	// Get batch status
	batchStatus, err := h.getBatchStatus(req.TaskIDs, req.BatchID, userID)
	if err != nil {
		RespondTaskError(w, fmt.Sprintf("Failed to get batch status: %s", err.Error()))
		return
	}

	// Return batch status
	RespondSuccess(w, batchStatus)
}

func (h *BatchStatusHandler) getBatchStatus(taskIDs []string, batchID, userID string) (*BatchStatusResponse, error) {
	// If batch ID is provided, get task IDs from batch
	if batchID != "" && len(taskIDs) == 0 {
		// In real implementation, query database for batch task IDs
		taskIDs = []string{"task_1", "task_2", "task_3"} // Mock data
	}

	response := &BatchStatusResponse{
		BatchID:    batchID,
		TotalTasks: len(taskIDs),
		Tasks:      make(map[string]*TaskStatusResponse),
	}

	// Get status for each task
	for _, taskID := range taskIDs {
		taskStatus, err := (&TaskStatusHandler{BaseHandler: h.BaseHandler}).getTaskStatus(taskID, userID)
		if err != nil {
			continue // Skip failed task status queries
		}

		response.Tasks[taskID] = taskStatus

		// Count task states
		switch taskStatus.Status {
		case "completed":
			response.Completed++
		case "failed":
			response.Failed++
		case "processing":
			response.Processing++
		case "queued":
			response.Queued++
		}
	}

	// Calculate overall progress
	if response.TotalTasks > 0 {
		response.Progress = (response.Completed * 100) / response.TotalTasks
	}

	return response, nil
}

// GetTaskTypes - Get supported task types
func GetTaskTypes() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"type":        "tts",
			"name":        "Text to Speech",
			"description": "Generate voice from text",
		},
		{
			"type":        "translate_subtitle",
			"name":        "Subtitle Translation",
			"description": "Translate subtitle files",
		},
		{
			"type":        "speech_recognition",
			"name":        "Speech Recognition",
			"description": "Convert audio/video to subtitle",
		},
		{
			"type":        "video_translation",
			"name":        "Video Translation",
			"description": "Complete video translation pipeline",
		},
		{
			"type":        "batch_process",
			"name":        "Batch Processing",
			"description": "Process multiple videos with same settings",
		},
	}
}

// GetTaskStatuses - Get possible task statuses
func GetTaskStatuses() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"status":      "queued",
			"description": "Task is queued for processing",
			"color":       "#FFA500",
		},
		{
			"status":      "processing",
			"description": "Task is currently being processed",
			"color":       "#0066CC",
		},
		{
			"status":      "completed",
			"description": "Task completed successfully",
			"color":       "#00AA00",
		},
		{
			"status":      "failed",
			"description": "Task failed with error",
			"color":       "#CC0000",
		},
		{
			"status":      "cancelled",
			"description": "Task was cancelled by user",
			"color":       "#666666",
		},
	}
}
