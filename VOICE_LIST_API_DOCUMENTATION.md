# Voice List API Documentation

## Overview

The `/voice_list` endpoint provides a way to retrieve available voices for different TTS (Text-to-Speech) services used in video dubbing. This endpoint returns an array of voice objects with `id` and `name` fields for the specified service.

## Endpoint Details

- **URL**: `http://127.0.0.1:9011/voice_list`
- **Method**: `GET`
- **Parameters**: URL query parameters

## Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `tts_type` | integer | Yes | TTS service type (0-17) |
| `language` | string | No | Language filter (e.g., 'zh', 'en', 'ja') |

## TTS Service Types

| ID | Service Name | Description |
|----|--------------|-------------|
| 0 | Edge-TTS | Microsoft Edge TTS (Free) |
| 1 | CosyVoice | CosyVoice (Local) |
| 2 | ChatTTS | ChatTTS (Local) |
| 3 | 302.AI | 302.AI TTS Service |
| 4 | FishTTS | FishTTS (Local) |
| 5 | Azure-TTS | Microsoft Azure TTS |
| 6 | GPT-SoVITS | GPT-SoVITS (Local) |
| 7 | clone-voice | Clone Voice (Local) |
| 8 | OpenAI TTS | OpenAI Text-to-Speech |
| 9 | Elevenlabs.io | ElevenLabs TTS |
| 10 | Google TTS | Google Text-to-Speech |
| 11 | Custom TTS API | Custom TTS API |
| 12 | VolcEngine TTS | ByteDance VolcEngine TTS |
| 13 | F5-TTS | F5-TTS (Local) |
| 14 | kokoro-TTS | kokoro-TTS (Local) |
| 15 | Google Cloud TTS | Google Cloud TTS |
| 16 | Gemini TTS | Google Gemini TTS |
| 17 | AusyncLab TTS | AusyncLab TTS service |

## Response Format

### Success Response
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "service_name": "Edge-TTS",
    "service_id": 0,
    "voices": [
      {"id": "zh-CN-XiaoxiaoNeural", "name": "zh-CN-XiaoxiaoNeural"},
      {"id": "zh-CN-YunxiNeural", "name": "zh-CN-YunxiNeural"},
      {"id": "en-US-AriaNeural", "name": "en-US-AriaNeural"},
      {"id": "en-US-JennyNeural", "name": "en-US-JennyNeural"},
      {"id": "ja-JP-NanamiNeural", "name": "ja-JP-NanamiNeural"}
    ]
  }
}
```

### Error Response
```json
{
  "code": 1,
  "msg": "Error message describing what went wrong"
}
```

## Language Filtering

When the `language` parameter is provided, the endpoint filters voices based on:

- Voice names containing the language code (e.g., "zh-CN", "en-US")
- Voice names starting with the language prefix
- Special language mappings:
  - `zh`: Chinese voices (contains "zh-", "chinese", "中文")
  - `en`: English voices (contains "en-", "english")
  - `ja`: Japanese voices (contains "ja-", "japanese", "日语")
  - `ko`: Korean voices (contains "ko-", "korean", "韩语")

## Example Usage

### Basic Request (All Voices)
```python
import requests

response = requests.get("http://127.0.0.1:9011/voice_list", params={
    "tts_type": 0  # Edge-TTS
})

data = response.json()
if data["code"] == 0:
    voices = data["data"]["voices"]
    print(f"Found {len(voices)} voices")
    print(f"Sample voices: {[voice['name'] for voice in voices[:5]]}")
    # Access voice ID and name
    for voice in voices[:3]:
        print(f"Voice ID: {voice['id']}, Name: {voice['name']}")
```

### Filtered Request (Chinese Voices Only)
```python
import requests

response = requests.get("http://127.0.0.1:9011/voice_list", params={
    "tts_type": 0,  # Edge-TTS
    "language": "zh"  # Chinese only
})

data = response.json()
if data["code"] == 0:
    chinese_voices = data["data"]["voices"]
    print(f"Found {len(chinese_voices)} Chinese voices")
    # Extract voice names for display
    voice_names = [voice['name'] for voice in chinese_voices]
    print(f"Chinese voice names: {voice_names}")
```

### Simple URL Request
```python
import requests

# Direct URL with parameters
response = requests.get("http://127.0.0.1:9011/voice_list?tts_type=0&language=zh")
data = response.json()
```

### Error Handling
```python
import requests

response = requests.get("http://127.0.0.1:9011/voice_list", params={
    "tts_type": 999  # Invalid service type
})

data = response.json()
if data["code"] != 0:
    print(f"Error: {data['msg']}")
```

## Common Error Codes

| Code | Description |
|------|-------------|
| 1 | Missing or invalid `tts_type` parameter |
| 1 | TTS service type out of valid range (0-17) |
| 1 | Error retrieving voice list from service |

## Notes

1. **Object Format**: The endpoint returns an array of voice objects with `id` and `name` fields, not grouped by language
2. **Duplicate Removal**: Duplicate voice names are automatically removed while preserving order
3. **Service Dependencies**: Some services may require additional configuration or API keys
4. **Dynamic Loading**: Voice lists are loaded dynamically from each service's configuration
5. **Caching**: Some services cache voice lists locally for better performance

## Integration with Video Dubbing

This endpoint is designed to work with the video dubbing workflow:

1. Call `/voice_list` to get available voices for a TTS service
2. Select an appropriate voice from the returned list
3. Use the selected voice in `/tts` or `/trans_video` endpoints

## Testing

Use the provided test scripts to verify the endpoint:

- `test_voice_list_endpoint.py`: Comprehensive testing
- `test_flat_voice_list.py`: Format verification
- `voice_list_example.py`: Usage examples
