#!/usr/bin/env python3
"""
Test script for unified model naming pattern
"""

import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_parsing():
    """Test the unified model naming pattern"""
    print("=" * 60)
    print("Testing Unified Model Naming Pattern")
    print("=" * 60)
    
    def parse_model_and_provider(model_name, default_videogen_type):
        """Parse model name and determine provider and internal model name"""
        if not model_name:
            return default_videogen_type, None
            
        model_lower = model_name.lower()
        
        # BytePlus models (seedance-*)
        if model_lower.startswith('seedance-'):
            provider_type = 0  # BytePlus
            if model_lower == 'seedance-lite':
                internal_model = 'seedance-1-0-lite-t2v-250428'
            elif model_lower == 'seedance-pro':
                internal_model = 'seedance-1-0-lite-t2v-pro-250428'
            else:
                # Custom seedance model, use as-is
                internal_model = model_name
            return provider_type, internal_model
            
        # Minimax models (minimax-*)
        elif model_lower.startswith('minimax-'):
            provider_type = 1  # Minimax
            model_suffix = model_name[8:]  # Remove 'minimax-' prefix
            
            # Map common patterns
            minimax_model_map = {
                'hailuo-02': 'MiniMax-Hailuo-02',
                'hailuo': 'MiniMax-Hailuo-02',
                't2v-director': 'T2V-01-Director',
                'i2v-director': 'I2V-01-Director',
                's2v': 'S2V-01',
                'i2v': 'I2V-01',
                'i2v-live': 'I2V-01-live',
                't2v': 'T2V-01'
            }
            
            internal_model = minimax_model_map.get(model_suffix.lower(), f'MiniMax-{model_suffix}')
            return provider_type, internal_model
            
        # Direct model names (backward compatibility)
        else:
            return default_videogen_type, model_name
    
    # Test cases
    test_cases = [
        # BytePlus models
        ("seedance-lite", 0, (0, "seedance-1-0-lite-t2v-250428")),
        ("seedance-pro", 0, (0, "seedance-1-0-lite-t2v-pro-250428")),
        ("Seedance-Lite", 0, (0, "seedance-1-0-lite-t2v-250428")),  # Case insensitive
        ("seedance-custom", 0, (0, "seedance-custom")),  # Custom model
        
        # Minimax models
        ("minimax-hailuo-02", 0, (1, "MiniMax-Hailuo-02")),
        ("minimax-hailuo", 0, (1, "MiniMax-Hailuo-02")),
        ("minimax-t2v-director", 0, (1, "T2V-01-Director")),
        ("minimax-i2v-director", 0, (1, "I2V-01-Director")),
        ("minimax-s2v", 0, (1, "S2V-01")),
        ("minimax-i2v", 0, (1, "I2V-01")),
        ("minimax-i2v-live", 0, (1, "I2V-01-live")),
        ("minimax-t2v", 0, (1, "T2V-01")),
        ("Minimax-Hailuo-02", 0, (1, "MiniMax-Hailuo-02")),  # Case insensitive
        ("minimax-custom", 0, (1, "MiniMax-custom")),  # Custom model
        
        # Backward compatibility
        ("MiniMax-Hailuo-02", 1, (1, "MiniMax-Hailuo-02")),  # Direct model name
        ("custom-model", 0, (0, "custom-model")),  # Unknown pattern
        
        # Empty/None cases
        ("", 0, (0, None)),
        ("", 1, (1, None)),
    ]
    
    print("\n1. Testing model parsing logic...")
    all_passed = True
    
    for i, (input_model, default_type, expected) in enumerate(test_cases, 1):
        result = parse_model_and_provider(input_model, default_type)
        
        if result == expected:
            print(f"✅ Test {i:2d}: '{input_model}' -> {result}")
        else:
            print(f"❌ Test {i:2d}: '{input_model}' -> {result} (expected {expected})")
            all_passed = False
    
    if all_passed:
        print("✅ All model parsing tests passed!")
    else:
        print("❌ Some model parsing tests failed!")
        return False
    
    # Test API request simulation
    print("\n2. Testing API request simulation...")
    
    def simulate_api_request(request_data):
        """Simulate the updated /generate_video API endpoint logic"""
        # Get initial parameters
        videogen_type = int(request_data.get('videogen_type', 0))
        client_model = request_data.get('model', '')
        
        # Parse model and detect provider
        detected_videogen_type, parsed_model = parse_model_and_provider(client_model, videogen_type)
        
        # Update videogen_type if detected from model name
        if client_model and detected_videogen_type != videogen_type:
            videogen_type = detected_videogen_type
            
        # Set final model
        if parsed_model:
            model = parsed_model
        else:
            # Use defaults when no model specified
            if videogen_type == 0:  # BytePlus
                model = 'seedance-1-0-lite-t2v-250428'
            elif videogen_type == 1:  # Minimax
                model = 'MiniMax-Hailuo-02'
        
        return {
            'videogen_type': videogen_type,
            'model': model,
            'client_model': client_model
        }
    
    api_test_cases = [
        # Auto-detection from model name
        ({"model": "seedance-lite"}, {"videogen_type": 0, "model": "seedance-1-0-lite-t2v-250428"}),
        ({"model": "minimax-hailuo-02"}, {"videogen_type": 1, "model": "MiniMax-Hailuo-02"}),
        ({"model": "minimax-t2v-director"}, {"videogen_type": 1, "model": "T2V-01-Director"}),
        
        # Explicit videogen_type with matching model
        ({"videogen_type": 0, "model": "seedance-pro"}, {"videogen_type": 0, "model": "seedance-1-0-lite-t2v-pro-250428"}),
        ({"videogen_type": 1, "model": "minimax-i2v"}, {"videogen_type": 1, "model": "I2V-01"}),
        
        # Model overrides videogen_type
        ({"videogen_type": 0, "model": "minimax-hailuo"}, {"videogen_type": 1, "model": "MiniMax-Hailuo-02"}),
        ({"videogen_type": 1, "model": "seedance-lite"}, {"videogen_type": 0, "model": "seedance-1-0-lite-t2v-250428"}),
        
        # No model specified - use defaults
        ({"videogen_type": 0}, {"videogen_type": 0, "model": "seedance-1-0-lite-t2v-250428"}),
        ({"videogen_type": 1}, {"videogen_type": 1, "model": "MiniMax-Hailuo-02"}),
        ({}, {"videogen_type": 0, "model": "seedance-1-0-lite-t2v-250428"}),  # All defaults
    ]
    
    for i, (request, expected_partial) in enumerate(api_test_cases, 1):
        result = simulate_api_request(request)
        
        # Check expected fields
        match = all(result.get(k) == v for k, v in expected_partial.items())
        
        if match:
            print(f"✅ API Test {i:2d}: {request} -> videogen_type={result['videogen_type']}, model='{result['model']}'")
        else:
            print(f"❌ API Test {i:2d}: {request}")
            print(f"   Result: {result}")
            print(f"   Expected: {expected_partial}")
            all_passed = False
    
    if all_passed:
        print("✅ All API simulation tests passed!")
    else:
        print("❌ Some API simulation tests failed!")
        return False
    
    print("\n3. Testing real-world usage examples...")
    
    examples = [
        {
            "name": "BytePlus Lite",
            "request": {"prompt": "A dog running", "model": "seedance-lite"},
            "expected_provider": "BytePlus",
            "expected_internal": "seedance-1-0-lite-t2v-250428"
        },
        {
            "name": "BytePlus Pro", 
            "request": {"prompt": "Cinematic shot", "model": "seedance-pro"},
            "expected_provider": "BytePlus",
            "expected_internal": "seedance-1-0-lite-t2v-pro-250428"
        },
        {
            "name": "Minimax Hailuo",
            "request": {"prompt": "Woman drinking coffee", "model": "minimax-hailuo-02", "duration": 6},
            "expected_provider": "Minimax", 
            "expected_internal": "MiniMax-Hailuo-02"
        },
        {
            "name": "Minimax I2V Director",
            "request": {"prompt": "Animate this image", "model": "minimax-i2v-director", "image_path": "/path/to/image.jpg"},
            "expected_provider": "Minimax",
            "expected_internal": "I2V-01-Director"
        }
    ]
    
    for example in examples:
        result = simulate_api_request(example["request"])
        provider_name = "BytePlus" if result["videogen_type"] == 0 else "Minimax"
        
        if (provider_name == example["expected_provider"] and 
            result["model"] == example["expected_internal"]):
            print(f"✅ {example['name']}: {example['request']['model']} -> {provider_name} ({result['model']})")
        else:
            print(f"❌ {example['name']}: Expected {example['expected_provider']} ({example['expected_internal']}), got {provider_name} ({result['model']})")
            all_passed = False
    
    if all_passed:
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("=" * 60)
        print("\nUnified model naming is working correctly!")
        print("\nSupported patterns:")
        print("• BytePlus: seedance-lite, seedance-pro")
        print("• Minimax: minimax-hailuo-02, minimax-t2v-director, minimax-i2v-director, etc.")
        print("• Auto-detection: Provider is automatically detected from model name")
        print("• Backward compatibility: Direct model names still work")
        return True
    else:
        print("\n❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = test_model_parsing()
    sys.exit(0 if success else 1)
