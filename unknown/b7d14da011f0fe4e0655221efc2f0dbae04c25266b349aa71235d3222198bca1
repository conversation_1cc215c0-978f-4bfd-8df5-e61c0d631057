#!/usr/bin/env python3
"""
Test better colors for video rendering
"""

import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/pyvideotrans')

from videotrans.task.as_s import Subtitle, SubtitleStyle, VideoFormat, generate_bilingual_ass

def test_better_colors():
    """Test with much better colors for video rendering"""
    
    # Much better colors for video - darker and more saturated
    style = SubtitleStyle(
        font_family="NotoSansCJK-Regular",
        font_size=30,
        primary_color="&H001616DA",
        primary_stroke_width=1,
        primary_stroke_color="&H00000000",
        primary_margin_v=500,
        primary_background_color="&H00000080",  # Dark red/maroon - much better for video
        show_primary_background=True,
        secondary_background_color="&H00006400",  # Dark green - much better for video
        secondary_color="&H001CE10E",
        secondary_font_family="NotoSansCJK-Regular",
        secondary_font_size=23,
        secondary_stroke_color="&H000505E1",
        secondary_stroke_width=1,
        secondary_margin_v=77,
        shadow_color="#000000",
        show_primary_shadow=False,
        show_primary_stroke=False,
        show_secondary_background=True,
        show_secondary_shadow=False,
        show_secondary_stroke=False
    )
    
    # Test data
    src_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "They think eBay is pig Latin for bee."),
        Subtitle("00:00:04.000", "00:00:06.000", "This is a test subtitle.")
    ]

    trans_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Họ nghĩ eBay là tiếng lóng của con ong."),
        Subtitle("00:00:04.000", "00:00:06.000", "Đây là phụ đề thử nghiệm.")
    ]

    # Generate ASS
    video_format = VideoFormat(width=1920, height=1080)
    ass_content = generate_bilingual_ass(src_subs, trans_subs, style, video_format)
    
    print("Better Colors for Video Rendering:")
    print("=" * 60)
    print(ass_content)
    print("=" * 60)
    
    # Save for testing
    with open('/tmp/better_colors.ass', 'w', encoding='utf-8') as f:
        f.write(ass_content)
    
    print("\nRecommended Color Configuration:")
    print("=" * 40)
    print('"original": {')
    print('    "backgroundColor": "&H00000080",  // Dark red/maroon')
    print('    // ... other settings stay the same')
    print('},')
    print('"translation": {')
    print('    "backgroundColor": "&H00006400",  // Dark green')
    print('    // ... other settings stay the same')
    print('}')
    
    print("\nAlternative Color Options:")
    print("=" * 40)
    
    alternatives = [
        ("Dark Navy Blue", "&H00800000", "RGB(0, 0, 128)"),
        ("Dark Forest Green", "&H00004000", "RGB(0, 64, 0)"),
        ("Dark Purple", "&H00600060", "RGB(96, 0, 96)"),
        ("Dark Orange", "&H000060C0", "RGB(192, 96, 0)"),
        ("Dark Brown", "&H00003060", "RGB(96, 48, 0)"),
        ("Dark Teal", "&H00606000", "RGB(0, 96, 96)"),
    ]
    
    for name, color, rgb in alternatives:
        print(f"{name}: {color} ({rgb})")
    
    print(f"\nASS file saved to: /tmp/better_colors.ass")
    print("These darker colors should appear much more vibrant in video!")

if __name__ == "__main__":
    test_better_colors()
