# PyVideoTrans Authentication System

This document describes the comprehensive authentication system implemented for PyVideoTrans, supporting both web-based and API-based authentication with multiple methods.

## Features

- **Multiple Authentication Methods**:
  - Username/Password authentication
  - Google OAuth 2.0 authentication
  - API Key authentication
  - JWT Token authentication
  - Session-based authentication (for web interface)

- **Security Features**:
  - Password hashing with bcrypt
  - CSRF protection
  - Secure session management
  - API key generation and management
  - JWT token support

- **User Management**:
  - User registration and login
  - Profile management
  - Password changes
  - API key regeneration
  - Admin user support

## Quick Start

### 1. Install Dependencies

The authentication system requires additional dependencies. Install them with:

```bash
pip install flask-login flask-wtf flask-bcrypt PyJWT google-auth google-auth-oauthlib google-auth-httplib2
```

Or install from the updated requirements.txt:

```bash
pip install -r requirements.txt
```

### 2. Run Database Migration

Run the migration script to create the authentication tables:

```bash
python migrate_auth.py
```

This will:
- Create the users table and related indexes
- Create a default admin user (username: `admin`, password: `admin123`)
- Display the admin user's API key

### 3. Configure Environment Variables

Set up your environment variables in `.env` file:

```bash
# Required
SECRET_KEY="your-secure-secret-key-here"

# Optional - for Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 4. Start the Server

```bash
python api.py
```

### 5. Access the System

- **Web Interface**: http://localhost:9011/
- **API Documentation**: See API Authentication section below

## Authentication Methods

### 1. Session-Based Authentication (Web Interface)

For browser-based access, users can log in through the web interface:

- **Login**: http://localhost:9011/auth/login
- **Register**: http://localhost:9011/auth/register
- **Profile**: http://localhost:9011/auth/profile

### 2. API Key Authentication

Each user has a unique API key for programmatic access:

```bash
curl -H "X-API-KEY: your-api-key-here" http://localhost:9011/api/tasks
```

### 3. JWT Token Authentication

Get a JWT token by logging in via API:

```bash
# Login to get token
curl -X POST http://localhost:9011/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Use token for API calls
curl -H "Authorization: Bearer your-jwt-token" http://localhost:9011/api/tasks
```

### 4. Google OAuth Authentication

Users can log in with their Google accounts:

1. Set up Google OAuth credentials in Google Cloud Console
2. Configure `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
3. Users can click "Login with Google" on the login page

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET/POST | `/auth/login` | Login page/API |
| GET/POST | `/auth/register` | Registration page/API |
| GET | `/auth/logout` | Logout |
| GET | `/auth/profile` | User profile |
| GET/POST | `/auth/profile/edit` | Edit profile |
| GET/POST | `/auth/change-password` | Change password |
| POST | `/auth/api-key/regenerate` | Regenerate API key |
| GET | `/auth/google` | Google OAuth login |
| GET | `/auth/google/callback` | Google OAuth callback |

### API Authentication Examples

#### JSON Login (Get JWT Token)

```bash
curl -X POST http://localhost:9011/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

Response:
```json
{
  "success": true,
  "message": "Login successful",
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "admin@localhost",
    "first_name": "Admin",
    "last_name": "User"
  },
  "api_key": "your-api-key-here"
}
```

#### JSON Registration

```bash
curl -X POST http://localhost:9011/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword",
    "first_name": "John",
    "last_name": "Doe"
  }'
```

## Protected Endpoints

All main API endpoints now require authentication:

- `/tts` - Text-to-speech conversion
- `/translate_srt` - Subtitle translation
- `/recogn` - Speech recognition
- `/trans_video` - Video translation
- `/upload` - File upload
- `/api/tasks/*` - Task management
- `/api/media/*` - Media library

## Backward Compatibility

The system maintains backward compatibility with the previous `X-USER-ID` header system, but this is deprecated. Existing clients can continue to work, but should migrate to proper authentication.

## User Management

### Default Admin User

After migration, a default admin user is created:
- **Username**: `admin`
- **Email**: `admin@localhost`
- **Password**: `admin123`
- **Role**: Admin

**⚠️ Important**: Change the default password immediately after first login!

### Creating Additional Users

Users can register through:
1. Web interface: http://localhost:9011/auth/register
2. JSON API: `POST /auth/register`
3. Google OAuth: Click "Login with Google"

### User Roles

- **Regular User**: Can access their own tasks and media
- **Admin User**: Has additional privileges (future feature)

## Security Considerations

1. **Change Default Credentials**: Always change the default admin password
2. **Secure Secret Key**: Use a strong, random SECRET_KEY in production
3. **HTTPS**: Use HTTPS in production environments
4. **API Key Security**: Keep API keys secure and regenerate if compromised
5. **Environment Variables**: Store sensitive configuration in environment variables

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Run `python migrate_auth.py` to check dependencies
2. **Database Errors**: Ensure database is accessible and tables are created
3. **Google OAuth Issues**: Verify client ID/secret and redirect URLs
4. **Session Issues**: Check SECRET_KEY configuration

### Debug Mode

Enable debug logging by setting the log level in your configuration.

## Migration from Previous System

If you're upgrading from the previous X-USER-ID system:

1. Run the migration script: `python migrate_auth.py`
2. Update your client code to use proper authentication
3. The old X-USER-ID header will continue to work but is deprecated

## API Client Examples

### Python Example

```python
import requests

# Method 1: API Key
headers = {"X-API-KEY": "your-api-key"}
response = requests.get("http://localhost:9011/api/tasks", headers=headers)

# Method 2: JWT Token
# First login to get token
login_data = {"username": "admin", "password": "admin123"}
login_response = requests.post("http://localhost:9011/auth/login", json=login_data)
token = login_response.json()["token"]

# Use token
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:9011/api/tasks", headers=headers)
```

### JavaScript Example

```javascript
// Method 1: API Key
fetch('http://localhost:9011/api/tasks', {
  headers: {
    'X-API-KEY': 'your-api-key'
  }
});

// Method 2: JWT Token
// First login
const loginResponse = await fetch('http://localhost:9011/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'admin123' })
});
const { token } = await loginResponse.json();

// Use token
fetch('http://localhost:9011/api/tasks', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Support

For issues or questions about the authentication system, please check:

1. This documentation
2. The migration script output
3. Server logs for error messages
4. Environment variable configuration
