#!/usr/bin/env python3
"""
Simple test to verify task_id fix in Minimax _check_status method
"""

import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_minimax_task_id_fix():
    """Test that Minimax _check_status always includes task_id"""
    print("=" * 50)
    print("Testing Minimax task_id fix")
    print("=" * 50)
    
    try:
        from videotrans.videogen._minimax import MinimaxVideoGen
        
        # Mock API key
        with patch('videotrans.configure.enhanced_config.enhanced_config.get_api_key') as mock_get_api_key:
            mock_get_api_key.return_value = "test_api_key_12345"
            
            # Create service
            service = MinimaxVideoGen(prompt="test prompt")
            test_task_id = "test_task_123456"
            
            print("\n1. Testing successful response...")
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "status": "Success",
                "file_id": "test_file_123"
            }
            
            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)
                
            print(f"   Result: {result}")
            assert "task_id" in result, "task_id missing!"
            assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
            print("   ✅ Success case: task_id included")
            
            print("\n2. Testing processing response...")
            mock_response.json.return_value = {"status": "Processing"}
            
            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)
                
            print(f"   Result: {result}")
            assert "task_id" in result, "task_id missing!"
            assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
            print("   ✅ Processing case: task_id included")
            
            print("\n3. Testing error response...")
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"
            
            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)
                
            print(f"   Result: {result}")
            assert "task_id" in result, "task_id missing!"
            assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
            print("   ✅ Error case: task_id included")
            
            print("\n" + "=" * 50)
            print("🎉 MINIMAX TASK_ID FIX VERIFIED!")
            print("=" * 50)
            print("✅ task_id is now always included in responses")
            print("✅ All test scenarios passed")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimax_task_id_fix()
    sys.exit(0 if success else 1)
