#!/usr/bin/env python3
"""
Setup script for BytePlus ModelArk Image Generation
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_byteplus_imagegen():
    """Setup BytePlus ModelArk Image Generation"""
    
    print("🖼️  BytePlus ModelArk Image Generation Setup")
    print("=" * 60)
    
    try:
        from videotrans.configure.enhanced_config import enhanced_config
        
        # Check if API key is already configured
        existing_key = enhanced_config.get_api_key('byteplus_modelark')
        if existing_key:
            print(f"✅ BytePlus ModelArk API key is already configured")
            print(f"   Current key: {existing_key[:10]}...{existing_key[-4:] if len(existing_key) > 14 else existing_key}")
            
            choice = input("\n🔄 Do you want to update the API key? (y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("   Keeping existing configuration.")
                return test_configuration()
        
        # Get API key from user
        print("\n📝 Please enter your BytePlus ModelArk API key:")
        print("   You can get it from: https://console.byteplus.com/")
        print("   The key should look like: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
        
        api_key = input("\n🔑 API Key: ").strip()
        
        if not api_key:
            print("❌ API key cannot be empty")
            return False
        
        if len(api_key) < 20:
            print("❌ API key seems too short. Please check and try again.")
            return False
        
        # Set the API key
        success = enhanced_config.set_api_key('byteplus_modelark', api_key, 
                                            "API key for BytePlus ModelArk services (video and image generation)")
        
        if success:
            print("✅ API key configured successfully!")
        else:
            print("❌ Failed to configure API key")
            return False
        
        return test_configuration()
        
    except ImportError as e:
        print(f"❌ Cannot import configuration module: {e}")
        print("   Please make sure you're running this from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False

def test_configuration():
    """Test the configuration"""
    
    print("\n🧪 Testing Configuration")
    print("=" * 40)
    
    try:
        from videotrans.configure.enhanced_config import enhanced_config
        
        # Test API key retrieval
        api_key = enhanced_config.get_api_key('byteplus_modelark')
        if not api_key:
            print("❌ API key not found")
            return False
        
        print("✅ API key is properly configured")
        
        # Test API endpoint (simple connectivity test)
        import requests
        
        print("🌐 Testing API connectivity...")
        
        # Simple test request
        api_url = "https://ark.ap-southeast.bytepluses.com/api/v3/images/generations"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}',
            'User-Agent': 'PyVideoTrans/1.0'
        }
        
        # Test with a simple prompt
        payload = {
            "model": "seedream-3-0-t2i-250415",
            "prompt": "A simple test image",
            "response_format": "url",
            "size": "1024x1024",
            "guidance_scale": 3,
            "watermark": True
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                print("✅ API connectivity test successful!")
                result = response.json()
                if 'data' in result and result['data']:
                    print("✅ Image generation test successful!")
                    image_url = result['data'][0].get('url', '') if isinstance(result['data'], list) else result['data'].get('url', '')
                    if image_url:
                        print(f"   🔗 Test image URL: {image_url}")
                else:
                    print("⚠️  API responded but no image data received")
            elif response.status_code == 401:
                print("❌ Authentication failed - please check your API key")
                return False
            elif response.status_code == 429:
                print("⚠️  Rate limit exceeded - API key is valid but quota reached")
                print("✅ Configuration is correct")
            else:
                print(f"⚠️  API returned status {response.status_code}")
                print("   This might be temporary - configuration appears correct")
                
        except requests.exceptions.RequestException as e:
            print(f"⚠️  Network error during test: {e}")
            print("   Configuration appears correct, but network connectivity issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def show_usage_examples():
    """Show usage examples"""
    
    print("\n📖 Usage Examples")
    print("=" * 60)
    
    print("\n1. 🐍 Python API Usage:")
    print("""
import requests

# Generate an image
response = requests.post("http://127.0.0.1:9011/generate_image", json={
    "prompt": "A beautiful sunset over mountains with vibrant colors",
    "size": "1024x1024"
})

result = response.json()
if result['code'] == 0:
    print(f"Image URL: {result['image_url']}")
else:
    print(f"Error: {result['msg']}")
""")
    
    print("\n2. 🌐 cURL Usage:")
    print("""
curl -X POST http://127.0.0.1:9011/generate_image \\
  -H "Content-Type: application/json" \\
  -d '{
    "prompt": "A beautiful sunset over mountains with vibrant colors",
    "size": "1024x1024"
  }'
""")
    
    print("\n3. 📏 Supported Image Sizes:")
    sizes = ["1024x1024", "1024x1792", "1792x1024", "512x512", "768x768"]
    for size in sizes:
        print(f"   • {size}")
    
    print("\n4. 🧪 Test the API:")
    print("   python test_generate_image.py")

if __name__ == "__main__":
    print("🚀 Starting BytePlus ModelArk Image Generation Setup...")
    
    success = setup_byteplus_imagegen()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ Setup completed successfully!")
        print("=" * 60)
        
        show_usage_examples()
        
        print("\n🎉 You can now use image generation in PyVideoTrans!")
        print("\n📚 API Documentation:")
        print("   • Endpoint: POST /generate_image")
        print("   • Parameters: prompt (required), size (optional)")
        print("   • Response: {\"code\": 0, \"msg\": \"ok\", \"image_url\": \"...\"}")
        
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")
        sys.exit(1)
