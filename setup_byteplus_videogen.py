#!/usr/bin/env python3
"""
BytePlus ModelArk Video Generation Setup Script

This script helps you configure BytePlus ModelArk video generation service
for the PyVideoTrans application.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from videotrans.configure.enhanced_config import enhanced_config
    from videotrans import videogen
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please make sure you're running this script from the project root directory.")
    sys.exit(1)


def setup_byteplus_config():
    """Interactive setup for BytePlus ModelArk configuration"""
    print("=" * 60)
    print("BytePlus ModelArk Video Generation Setup")
    print("=" * 60)
    print()
    
    print("This script will help you configure BytePlus ModelArk for video generation.")
    print("You'll need a BytePlus ModelArk API key to proceed.")
    print()
    print("To get your API key:")
    print("1. Visit https://console.byteplus.com/ark/")
    print("2. Sign up or log in to your account")
    print("3. Navigate to API Key Management")
    print("4. Create a new API key")
    print()
    
    # Get API key
    while True:
        api_key = input("Enter your BytePlus ModelArk API key: ").strip()
        if not api_key:
            print("API key cannot be empty. Please try again.")
            continue
        
        if len(api_key) < 20:
            print("API key seems too short. Please check and try again.")
            continue
        
        break
    
    # Set API key
    try:
        success = enhanced_config.set_api_key('byteplus_modelark', api_key, 'BytePlus ModelArk API key for video generation')
        if success:
            print("✅ API key saved successfully!")
        else:
            print("❌ Failed to save API key. Please check the key format.")
            return False
    except Exception as e:
        print(f"❌ Error saving API key: {e}")
        return False
    
    # Configure video generation settings
    print("\nConfiguring video generation settings...")
    
    # Default model
    model = input("Enter model name (default: seedance-1-0-lite-t2v): ").strip()
    if not model:
        model = "seedance-1-0-lite-t2v"
    
    # Callback URL (optional)
    callback_url = input("Enter callback URL (optional, press Enter to skip): ").strip()
    
    # Save video generation configuration
    videogen_config = {
        'model': model,
        'callback_url': callback_url,
        'timeout': 300,
        'max_retries': 3
    }
    
    try:
        enhanced_config.set_videogen_config('byteplus', videogen_config)
        print("✅ Video generation configuration saved!")
    except Exception as e:
        print(f"❌ Error saving video generation config: {e}")
        return False
    
    # Test configuration
    print("\nTesting configuration...")
    try:
        is_configured = videogen.is_allow_videogen(videogen_type=0, return_str=True)
        if is_configured is True:
            print("✅ BytePlus ModelArk video generation is properly configured!")
        else:
            print(f"❌ Configuration test failed: {is_configured}")
            return False
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("=" * 60)
    print()
    print("You can now use video generation in PyVideoTrans:")
    print()
    print("API Endpoint: POST /generate_video")
    print("Example request:")
    print("""
{
    "prompt": "A little dog is running in the sunshine --ratio 16:9",
    "videogen_type": 0,
    "async_mode": true
}
""")
    print()
    print("For more information, see the API documentation.")
    
    return True


def check_current_config():
    """Check current BytePlus configuration"""
    print("Current BytePlus ModelArk Configuration:")
    print("-" * 40)
    
    # Check API key
    api_key = enhanced_config.get_api_key('byteplus_modelark')
    if api_key:
        print(f"✅ API Key: {'*' * (len(api_key) - 8) + api_key[-8:]}")
    else:
        print("❌ API Key: Not configured")
    
    # Check video generation config
    videogen_config = enhanced_config.get_videogen_config('byteplus')
    if videogen_config:
        print("✅ Video Generation Config:")
        for key, value in videogen_config.items():
            if value:
                print(f"   {key}: {value}")
    else:
        print("❌ Video Generation Config: Not configured")
    
    # Test service availability
    try:
        is_available = videogen.is_allow_videogen(videogen_type=0, return_str=True)
        if is_available is True:
            print("✅ Service Status: Available")
        else:
            print(f"❌ Service Status: {is_available}")
    except Exception as e:
        print(f"❌ Service Status: Error - {e}")


def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--check':
        check_current_config()
        return
    
    print("BytePlus ModelArk Video Generation Setup")
    print()
    print("Options:")
    print("1. Setup new configuration")
    print("2. Check current configuration")
    print("3. Exit")
    print()
    
    while True:
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == '1':
            setup_byteplus_config()
            break
        elif choice == '2':
            check_current_config()
            break
        elif choice == '3':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


if __name__ == '__main__':
    main()
