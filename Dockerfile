# Multi-stage Dockerfile for PyVideoTrans API
FROM python:3.10-slim-bullseye as builder

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libffi-dev \
    libssl-dev \
    libpq-dev \
    libasound2-dev \
    libportaudio2 \
    libportaudiocpp0 \
    portaudio19-dev \
    libsndfile1-dev \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libswresample-dev \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel
RUN pip install --upgrade pip setuptools wheel

# Copy requirements first for better caching
COPY requirements.txt /tmp/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Runtime stage
FROM python:3.10-slim-bullseye

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    DEBIAN_FRONTEND=noninteractive

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libpq5 \
    libasound2 \
    libportaudio2 \
    libportaudiocpp0 \
    libsndfile1 \
    libavcodec58 \
    libavformat58 \
    libavutil56 \
    libswscale5 \
    libswresample3 \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create application user
RUN groupadd -r pyvideotrans && useradd -r -g pyvideotrans pyvideotrans

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /app/apidata \
    /app/tmp \
    /app/logs \
    /app/models \
    /app/videotrans \
    && chown -R pyvideotrans:pyvideotrans /app

# Copy application files
COPY --chown=pyvideotrans:pyvideotrans . /app/

# Copy FFmpeg binaries if they exist
COPY --chown=pyvideotrans:pyvideotrans ffmpeg/ /app/ffmpeg/

# Set permissions for executable files
RUN chmod +x /app/ffmpeg/* 2>/dev/null || true

# Create configuration directories
RUN mkdir -p /app/videotrans/config_backup \
    && chown -R pyvideotrans:pyvideotrans /app/videotrans

# Switch to non-root user
USER pyvideotrans

# Expose port (default 9011, configurable via host.txt)
EXPOSE 9011

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9011/api/config/settings || exit 1

# Set entrypoint
ENTRYPOINT ["python", "api.py"]
