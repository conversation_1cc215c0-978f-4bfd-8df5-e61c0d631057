#!/usr/bin/env python3
"""
Color conversion fix based on the JavaScript solution found
"""

def rgb_to_ass_color(r, g, b, alpha=0):
    """
    Convert RGB values to ASS color format
    
    Args:
        r: Red value (0-255)
        g: Green value (0-255) 
        b: Blue value (0-255)
        alpha: Alpha value (0-255, default 0 for opaque)
    
    Returns:
        ASS color string in format &HAABBGGRR
    """
    # Ensure values are in valid range
    r = max(0, min(255, r))
    g = max(0, min(255, g))
    b = max(0, min(255, b))
    alpha = max(0, min(255, alpha))
    
    # ASS format: &HAABBGGRR (Alpha-Blue-Green-Red)
    return f"&H{alpha:02X}{b:02X}{g:02X}{r:02X}"

def hex_to_ass_color(hex_color):
    """
    Convert hex color to ASS format
    
    Args:
        hex_color: Hex color string like "#E63946" or "#80E63946" (with alpha)
    
    Returns:
        ASS color string in format &HAABBGGRR
    """
    # Remove # if present
    hex_color = hex_color.lstrip('#')
    
    if len(hex_color) == 6:
        # RGB format
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        alpha = 0  # Opaque
    elif len(hex_color) == 8:
        # ARGB format
        alpha = int(hex_color[0:2], 16)
        r = int(hex_color[2:4], 16)
        g = int(hex_color[4:6], 16)
        b = int(hex_color[6:8], 16)
    else:
        raise ValueError(f"Invalid hex color format: {hex_color}")
    
    return rgb_to_ass_color(r, g, b, alpha)

# Test the solution with your original example
if __name__ == "__main__":
    # Your JavaScript example: #E63946
    test_hex = "#E63946"
    result = hex_to_ass_color(test_hex)
    print(f"Hex {test_hex} -> ASS {result}")
    
    # Verify: #E63946 = R(230), G(57), B(70)
    # Should be: &H00463957 (BGR order)
    expected = "&H00463957"
    print(f"Expected: {expected}")
    print(f"Match: {result == expected}")
    
    # Test with your original lime green issue
    lime_green_hex = "#D8E51F"  # RGB(216, 229, 31)
    lime_result = hex_to_ass_color(lime_green_hex)
    print(f"\nLime green {lime_green_hex} -> ASS {lime_result}")
    
    # Should be: &H001FE5D8 (BGR order: 31, 229, 216)
    expected_lime = "&H001FE5D8"
    print(f"Expected: {expected_lime}")
    print(f"Match: {lime_result == expected_lime}")
    
    # Test with alpha channel
    semi_transparent = "#80D8E51F"  # 50% transparent lime green
    alpha_result = hex_to_ass_color(semi_transparent)
    print(f"\nSemi-transparent {semi_transparent} -> ASS {alpha_result}")
    print(f"Expected: &H801FE5D8")
    print(f"Match: {alpha_result == '&H801FE5D8'}")
