#!/usr/bin/env python3
"""
Test script to verify task_id consistency across all video generation providers
"""

import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_id_consistency():
    """Test that task_id is always included in _check_status responses for all providers"""
    print("=" * 70)
    print("Testing task_id consistency across all video generation providers")
    print("=" * 70)

    try:
        from videotrans.videogen._minimax import MinimaxVideoGen
        from videotrans.videogen._byteplus import BytePlusVideoGen

        test_task_id = "test_task_123456"

        # Mock API key configuration for both providers
        with patch('videotrans.configure.enhanced_config.enhanced_config.get_api_key') as mock_get_api_key:
            mock_get_api_key.return_value = "test_api_key_12345"

            # Test both providers
            providers = [
                ("Minimax", MinimaxVideoGen),
                ("BytePlus", BytePlusVideoGen)
            ]

        for provider_name, provider_class in providers:
            print(f"\n🧪 Testing {provider_name} Provider")
            print("-" * 40)

            # Create service instance
            service = provider_class(prompt="test prompt")

            # Test 1: Successful response
            print(f"1. Testing {provider_name} successful response...")
            mock_response = Mock()
            mock_response.status_code = 200

            if provider_name == "Minimax":
                mock_response.json.return_value = {
                    "status": "Success",
                    "file_id": "test_file_123"
                }
            else:  # BytePlus
                mock_response.json.return_value = {
                    "status": "succeeded",
                    "content": {"video_url": "https://example.com/video.mp4"}
                }

            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)

            assert "task_id" in result, f"{provider_name}: task_id missing from successful response"
            assert result["task_id"] == test_task_id, f"{provider_name}: task_id mismatch"
            print(f"   ✅ Success: task_id = {result['task_id']}")

            # Test 2: Processing response
            print(f"2. Testing {provider_name} processing response...")
            if provider_name == "Minimax":
                mock_response.json.return_value = {"status": "Processing"}
            else:  # BytePlus
                mock_response.json.return_value = {"status": "running"}

            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)

            assert "task_id" in result, f"{provider_name}: task_id missing from processing response"
            assert result["task_id"] == test_task_id, f"{provider_name}: task_id mismatch"
            print(f"   ✅ Processing: task_id = {result['task_id']}")

            # Test 3: Failed response
            print(f"3. Testing {provider_name} failed response...")
            if provider_name == "Minimax":
                mock_response.json.return_value = {
                    "status": "Fail",
                    "error": "Generation failed"
                }
            else:  # BytePlus
                mock_response.json.return_value = {
                    "status": "failed",
                    "error": "Generation failed"
                }

            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)

            assert "task_id" in result, f"{provider_name}: task_id missing from failed response"
            assert result["task_id"] == test_task_id, f"{provider_name}: task_id mismatch"
            print(f"   ✅ Failed: task_id = {result['task_id']}")

            # Test 4: HTTP error
            print(f"4. Testing {provider_name} HTTP error response...")
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"

            with patch('requests.get', return_value=mock_response):
                result = service._check_status(test_task_id)

            assert "task_id" in result, f"{provider_name}: task_id missing from HTTP error response"
            assert result["task_id"] == test_task_id, f"{provider_name}: task_id mismatch"
            print(f"   ✅ HTTP Error: task_id = {result['task_id']}")

            # Test 5: Network error
            print(f"5. Testing {provider_name} network error response...")
            from requests.exceptions import RequestException

            with patch('requests.get', side_effect=RequestException("Network timeout")):
                result = service._check_status(test_task_id)

            assert "task_id" in result, f"{provider_name}: task_id missing from network error response"
            assert result["task_id"] == test_task_id, f"{provider_name}: task_id mismatch"
            print(f"   ✅ Network Error: task_id = {result['task_id']}")

            print(f"✅ {provider_name} provider: All tests passed!")

        # Test cross-provider consistency
        print(f"\n🔄 Testing cross-provider consistency...")

        # Create both services
        minimax_service = MinimaxVideoGen(prompt="test")
        byteplus_service = BytePlusVideoGen(prompt="test")

        # Mock successful responses for both
        mock_response = Mock()
        mock_response.status_code = 200

        # Test Minimax
        mock_response.json.return_value = {"status": "Success", "file_id": "test_file"}
        with patch('requests.get', return_value=mock_response):
            minimax_result = minimax_service._check_status(test_task_id)

        # Test BytePlus
        mock_response.json.return_value = {
            "status": "succeeded",
            "content": {"video_url": "https://example.com/video.mp4"}
        }
        with patch('requests.get', return_value=mock_response):
            byteplus_result = byteplus_service._check_status(test_task_id)

        # Verify both have task_id
        assert minimax_result["task_id"] == test_task_id, "Minimax task_id mismatch"
        assert byteplus_result["task_id"] == test_task_id, "BytePlus task_id mismatch"

        print(f"   ✅ Minimax task_id: {minimax_result['task_id']}")
        print(f"   ✅ BytePlus task_id: {byteplus_result['task_id']}")
        print("✅ Cross-provider consistency verified!")

        print("\n" + "=" * 70)
        print("🎉 ALL TASK_ID CONSISTENCY TESTS PASSED!")
        print("=" * 70)
        print("\n📋 Summary:")
        print("✅ Minimax provider: task_id always included")
        print("✅ BytePlus provider: task_id always included")
        print("✅ All error scenarios covered")
        print("✅ Cross-provider consistency verified")
        print("\n🔧 Fixed scenarios:")
        print("• Successful completion responses")
        print("• Processing/running status responses")
        print("• Failed generation responses")
        print("• HTTP error responses (4xx/5xx)")
        print("• Network error responses")
        print("• Exception handling responses")

        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_task_id_consistency()
    sys.exit(0 if success else 1)
