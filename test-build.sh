#!/bin/bash

# Test build script to check for missing dependencies
set -e

echo "🔨 Building Docker image to test for missing dependencies..."

# Build the image
docker build -t pyvideotrans-test:latest .

echo "✅ Build completed successfully!"

echo "🧪 Testing the image..."

# Run the container to test for import errors
docker run --rm --name pyvideotrans-test-run -p 9013:9011 pyvideotrans-test:latest &

# Get the container PID
CONTAINER_PID=$!

# Wait a bit for the container to start
sleep 10

# Check if the container is still running (no import errors)
if ps -p $CONTAINER_PID > /dev/null; then
    echo "✅ Container is running successfully!"
    
    # Test if the API is responding
    if curl -f http://localhost:9013/api/config/settings > /dev/null 2>&1; then
        echo "✅ API is responding!"
    else
        echo "⚠️  API might not be fully ready yet"
    fi
    
    # Stop the container
    kill $CONTAINER_PID
    wait $CONTAINER_PID 2>/dev/null || true
    echo "🛑 Test container stopped"
else
    echo "❌ Container failed to start - check for missing dependencies"
    exit 1
fi

echo "🎉 All tests passed!"
