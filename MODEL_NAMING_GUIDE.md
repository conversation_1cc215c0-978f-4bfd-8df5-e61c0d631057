# Unified Model Naming Guide

## Overview

The `/generate_video` endpoint now supports a unified model naming pattern that automatically detects the video generation provider and maps to the correct internal model names.

## Supported Model Patterns

### BytePlus Models (seedance-*)
- `seedance-lite` → BytePlus Lite model
- `seedance-pro` → BytePlus Pro model
- `seedance-custom` → Custom BytePlus model (passed as-is)

### Minimax Models (minimax-*)
- `minimax-hailuo-02` → MiniMax-Hailuo-02 (1080P, up to 10s)
- `minimax-hailuo` → MiniMax-Hailuo-02 (alias)
- `minimax-t2v-director` → T2V-01-Director (720P, text-to-video with camera control)
- `minimax-i2v-director` → I2V-01-Director (720P, image-to-video with camera control)
- `minimax-s2v` → S2V-01 (720P, subject reference)
- `minimax-i2v` → I2V-01 (720P, image-to-video)
- `minimax-i2v-live` → I2V-01-live (720P, image-to-video live)
- `minimax-t2v` → T2V-01 (720P, text-to-video)
- `minimax-custom` → MiniMax-custom (custom model)

## Usage Examples

### 1. BytePlus Lite (Auto-detected)
```json
{
    "prompt": "A dog running in the park",
    "model": "seedance-lite",
    "async_mode": true
}
```

### 2. BytePlus Pro (Auto-detected)
```json
{
    "prompt": "Cinematic shot of mountains at sunset",
    "model": "seedance-pro",
    "async_mode": true
}
```

### 3. Minimax Hailuo-02 (Auto-detected)
```json
{
    "prompt": "A woman drinking coffee in a café",
    "model": "minimax-hailuo-02",
    "duration": 6,
    "resolution": "1080P",
    "async_mode": true
}
```

### 4. Minimax Image-to-Video (Auto-detected)
```json
{
    "prompt": "Make this image come alive with gentle movement",
    "image_path": "/path/to/image.jpg",
    "model": "minimax-i2v-director",
    "duration": 6,
    "resolution": "768P",
    "async_mode": true
}
```

## Key Features

### 🎯 **Auto-Detection**
- Provider is automatically detected from the model name
- No need to specify `videogen_type` when using unified naming
- Model name overrides `videogen_type` if both are provided

### 🔄 **Backward Compatibility**
- Direct model names still work (e.g., "MiniMax-Hailuo-02")
- Existing API calls continue to function without changes

### 📝 **Case Insensitive**
- Model names are case-insensitive
- `minimax-hailuo-02`, `Minimax-Hailuo-02`, `MINIMAX-HAILUO-02` all work

### ⚙️ **Smart Defaults**
- When no model is specified, uses provider-specific defaults
- BytePlus: `seedance-1-0-lite-t2v-250428`
- Minimax: `MiniMax-Hailuo-02`

## API Parameters

### Common Parameters
- `prompt`: Text description for video generation
- `model`: Unified model name (auto-detects provider)
- `image_path`: Image path for image-to-video generation
- `async_mode`: Whether to use async mode (default: true)

### Minimax-Specific Parameters (when using minimax-* models)
- `duration`: Video duration in seconds (6 or 10, default: 6)
- `resolution`: Video resolution (768P or 1080P, default: 768P)
- `prompt_optimizer`: Enable prompt optimization (default: true)

## Migration Guide

### Before (Provider-specific)
```json
{
    "prompt": "A cat playing",
    "videogen_type": 1,
    "model": "MiniMax-Hailuo-02",
    "duration": 6
}
```

### After (Unified naming)
```json
{
    "prompt": "A cat playing",
    "model": "minimax-hailuo-02",
    "duration": 6
}
```

## Error Handling

- Invalid model names fall back to backward compatibility mode
- Unknown `minimax-*` patterns get mapped to `MiniMax-{suffix}`
- Unknown `seedance-*` patterns are passed as-is to BytePlus

## Testing

Run the test suite to verify the unified naming:
```bash
python test_unified_model_naming.py
```

## Configuration

### Setup Minimax
```bash
python setup_minimax_videogen.py
```

### Setup BytePlus
```bash
python setup_byteplus_videogen.py
```

Both providers can be used simultaneously with the unified naming system.
