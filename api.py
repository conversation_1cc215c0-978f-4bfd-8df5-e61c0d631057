if __name__ == '__main__':
  print('API ...')
  import json
  import multiprocessing
  import random
  import re
  import shutil
  import threading
  import time
  from pathlib import Path

  from flask import Flask, request, jsonify, send_from_directory, session, redirect, url_for
  from flask_cors import CORS
  from flask_login import Lo<PERSON><PERSON>anager, login_required, current_user
  from flask_bcrypt import Bcrypt
  from waitress import serve
  import hashlib
  import os

  from videotrans.configure import config
  from videotrans.configure.enhanced_config import enhanced_config
  from videotrans.database.config_watcher import get_realtime_config_manager
  from videotrans.database.task_manager import task_manager
  from videotrans.database.media_manager import media_manager
  from videotrans.task._dubbing import DubbingSrt
  from videotrans.task._speech2text import SpeechToText
  from videotrans.task._translate_srt import TranslateSrt
  from videotrans.task._apply_subtitle_style import ApplySubtitleStyleTask
  from videotrans.task.job import start_thread
  from videotrans.task.trans_create import TransCreate
  from videotrans.task._video_generation import VideoGenerationTask
  from videotrans.util import tools
  from videotrans import tts as tts_model, translator, recognition
  from videotrans import videogen
  from functools import wraps
  import uuid
  import google.generativeai as genai
  import requests
  import urllib.parse

  ###### 配置信息
  #### api文档 https://pyvideotrans.com/api-cn
  config.exec_mode = 'api'
  ROOT_DIR = config.ROOT_DIR
  HOST = "127.0.0.1"
  PORT = 9011
  if Path(ROOT_DIR + '/host.txt').is_file():
    host_str = Path(ROOT_DIR + '/host.txt').read_text(encoding='utf-8').strip()
    host_str = re.sub(r'https?://', '', host_str).split(':')
    if len(host_str) > 0:
      HOST = host_str[0]
    if len(host_str) == 2:
      PORT = int(host_str[1])

  # 存储生成的文件和进度日志
  API_RESOURCE = 'apidata'
  TARGET_DIR = ROOT_DIR + f'/{API_RESOURCE}'
  Path(TARGET_DIR).mkdir(parents=True, exist_ok=True)
  # 进度日志
  PROCESS_INFO = TARGET_DIR + '/processinfo'
  if Path(PROCESS_INFO).is_dir():
    shutil.rmtree(PROCESS_INFO)
  Path(PROCESS_INFO).mkdir(parents=True, exist_ok=True)
  # url前缀
  # URL_PREFIX = f"{BASE_URL}/{API_RESOURCE}"

  config.exit_soft = False
  # 停止 结束 失败状态
  end_status_list = ['error', 'succeed', 'end', 'stop']
  # 日志状态
  logs_status_list = ['logs']

  ######################

  app = Flask(__name__, static_folder=TARGET_DIR, template_folder='templates')

  # Configure Flask app
  app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
  app.config['GOOGLE_CLIENT_ID'] = os.environ.get('GOOGLE_CLIENT_ID')
  app.config['GOOGLE_CLIENT_SECRET'] = os.environ.get('GOOGLE_CLIENT_SECRET')

  # Initialize Flask extensions
  bcrypt = Bcrypt(app)
  login_manager = LoginManager()
  login_manager.init_app(app)
  login_manager.login_view = 'auth.login'
  login_manager.login_message = 'Please log in to access this page.'
  login_manager.login_message_category = 'info'

  # Configure CORS to be very permissive for development
  # This allows all origins, headers, and methods
  from flask_cors import cross_origin

  # Apply CORS globally with maximum permissiveness
  CORS(app,
       origins="*",  # Allow all origins
       methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"],
       allow_headers="*",  # Try to allow all headers
       supports_credentials=False,
       automatic_options=True,
       send_wildcard=True,
       vary_header=False)

  # Additional CORS handler to ensure all headers are allowed
  @app.after_request
  def after_request(response):
    """Add comprehensive CORS headers to all responses"""
    origin = request.headers.get('Origin')
    if origin:
      response.headers['Access-Control-Allow-Origin'] = origin
    else:
      response.headers['Access-Control-Allow-Origin'] = '*'

    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD'
    response.headers['Access-Control-Allow-Headers'] = '*'
    response.headers['Access-Control-Max-Age'] = '86400'  # 24 hours

    # Handle preflight requests for any custom headers
    if request.method == 'OPTIONS':
      requested_headers = request.headers.get('Access-Control-Request-Headers')
      if requested_headers:
        response.headers['Access-Control-Allow-Headers'] = requested_headers

    return response


  # Flask-Login user loader
  @login_manager.user_loader
  def load_user(user_id):
    """Load user for Flask-Login"""
    try:
      from videotrans.auth.utils import get_user_by_id
      return get_user_by_id(int(user_id))
    except (ValueError, TypeError):
      return None


  # Register authentication blueprint
  try:
    from videotrans.auth import auth_bp
    app.register_blueprint(auth_bp)
    print("✅ Authentication system initialized successfully")
  except Exception as e:
    print(f"❌ Failed to initialize authentication system: {e}")


  # Enhanced User Context Middleware with Authentication Support
  @app.before_request
  def extract_user_context():
    """Extract and validate user context with multiple authentication methods"""
    # IMPORTANT: Skip ALL processing for OPTIONS requests (CORS preflight)
    # OPTIONS requests should never be blocked by authentication/authorization
    if request.method == 'OPTIONS':
      return None

    # Skip authentication for certain endpoints
    skip_endpoints = [
      '/file/',
      '/usr/lib/media/',
      '/api/config/',
      '/api/realtime/',
      '/auth/',  # Authentication routes
      '/static/'  # Static files
    ]

    # Check if current request should skip authentication
    if any(request.path.startswith(endpoint) for endpoint in skip_endpoints):
      return None

    from flask import g
    from videotrans.auth.utils import get_user_by_api_key, verify_jwt_token, get_user_by_id

    # Initialize user context
    g.user_id = None
    g.authenticated_user = None

    # Method 1: Check if user is logged in via Flask-Login (session-based)
    if current_user.is_authenticated:
      g.user_id = str(current_user.id)
      g.authenticated_user = current_user
      config.logger.debug(f"Session-based auth: User {current_user.username} ({current_user.id})")
      return None

    # Method 2: Check for API key in X-API-KEY header
    api_key = request.headers.get('X-API-KEY')
    if api_key:
      user = get_user_by_api_key(api_key)
      if user:
        g.user_id = str(user.id)
        g.authenticated_user = user
        config.logger.debug(f"API key auth: User {user.username} ({user.id})")
        return None

    # Method 3: Check for JWT token in Authorization header
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
      token = auth_header[7:]  # Remove 'Bearer ' prefix
      payload = verify_jwt_token(token)
      if payload:
        user = get_user_by_id(payload.get('user_id'))
        if user:
          g.user_id = str(user.id)
          g.authenticated_user = user
          config.logger.debug(f"JWT auth: User {user.username} ({user.id})")
          return None

    # Method 4: Backward compatibility - X-USER-ID header (deprecated but supported)
    legacy_user_id = request.headers.get('X-USER-ID')
    if legacy_user_id:
      # For backward compatibility, accept string user IDs
      g.user_id = legacy_user_id.strip()
      config.logger.debug(f"Legacy auth: User ID {g.user_id}")
      return None

    # Check if authentication is required for this endpoint
    protected_endpoints = [
      '/api/tasks',
      '/api/media',
      '/upload',
      '/tts',
      '/translate_srt',
      '/recogn',
      '/trans_video',
      '/generate_video',
      '/generate_image',
      '/v1/chat/completions'  # OpenAI compatible endpoint
    ]

    requires_auth = any(request.path.startswith(endpoint) for endpoint in protected_endpoints)

    if requires_auth:
      return jsonify({
        "code": 1,
        "msg": "Authentication required. Please login or provide valid API key/token."
      }), 401


  def require_user_context(f):
    """Decorator to ensure user context is available (updated for new auth system)"""

    @wraps(f)
    def decorated_function(*args, **kwargs):
      from flask import g
      if not hasattr(g, 'user_id') or not g.user_id:
        return jsonify({
          "code": 1,
          "msg": "Authentication required. Please login or provide valid API key/token."
        }), 401
      return f(*args, **kwargs)

    return decorated_function


  def get_current_user_id():
    """Helper function to get current user ID from request context"""
    from flask import g
    return getattr(g, 'user_id', None)


  def get_current_user():
    """Helper function to get current authenticated user object"""
    from flask import g
    return getattr(g, 'authenticated_user', None)


  # Initialize real-time configuration system
  try:
    realtime_config_manager = get_realtime_config_manager()
    print("✅ Real-time configuration system initialized")
    print(f"   Watcher running: {realtime_config_manager.watcher._running}")
    print(f"   Registered services: {len(realtime_config_manager._service_instances)}")
  except Exception as e:
    print(f"⚠️  Failed to initialize real-time configuration: {e}")

  # Register configuration API endpoints
  try:
    from videotrans.database.api_endpoints import config_api

    app.register_blueprint(config_api)
    print("✅ Configuration API endpoints registered successfully")
  except Exception as e:
    print(f"❌ Failed to register configuration API endpoints: {e}")

  # Root route - redirect to authentication
  @app.route('/')
  def index():
    """Root route - redirect to appropriate page based on authentication"""
    if current_user.is_authenticated:
      return redirect(url_for('auth.profile'))
    else:
      return redirect(url_for('auth.login'))

  # 第1个接口 /tts
  """
  根据字幕合成配音接口

  请求数据类型: Content-Type:application

  请求参数：

  name:必须参数，字符串类型，需要配音的srt字幕的绝对路径(需同本软件在同一设备)，或者直接传递合法的srt字幕格式内容
  tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
      6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API"
  voice_role:必须参数，字符串类型，对应配音渠道的角色名，edge-tts/azure-tts/302.ai(azure模型)时目标语言不同，角色名也不同，具体见底部
  target_language:必须参数，字符串类型，需要配音的语言类型代码，即所传递的字幕文字语言代码，可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
  voice_rate:可选参数，字符串类型，语速加减值，格式为：加速`+数字%`，减速`-数字%`
  volume:可选参数，字符串类型，音量变化值(仅配音渠道为edge-tts生效)，格式为 增大音量`+数字%`，降低音量`-数字%`
  pitch:可选参数，字符串类型，音调变化值(仅配音渠道为edge-tts生效)，格式为 调大音调`+数字Hz`,降低音量`-数字Hz`
  out_ext:可选参数，字符串类型，输出配音文件类型，mp3|wav|flac|aac,默认wav
  voice_autorate:可选参数，布尔类型，默认False，是否自动加快语速，以便与字幕对齐

  返回数据：
  返回类型：json格式，
  成功时返回，可根据task_id通过 task_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  失败时返回
  {"code":1,"msg":"错误信息"}


  请求示例
  ```
  def test_tts():
      res=requests.post("http://127.0.0.1:9011/tts",json={
      "name":"C:/users/<USER>/videos/zh0.srt",
      "voice_role":"zh-CN-YunjianNeural",
      "target_language_code":"zh-cn",
      "voice_rate":"+0%",
      "volume":"+0%",
      "pitch":"+0Hz",
      "tts_type":"0",
      "out_ext":"mp3",
      "voice_autorate":True,
      })
      print(res.json())
  ```
  """


  @app.route('/tts', methods=['POST'])
  def tts():
    data = request.json
    # 从请求数据中获取参数
    name = data.get('name', '').strip()
    if not name:
      return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
    is_srt = True
    if name.find("\n") == -1 and name.endswith('.srt'):
      if not Path(name).exists():
        return jsonify({"code": 1, "msg": f"The file {name} is not exist"})
    else:
      tmp_file = config.TEMP_DIR + f'/tts-srt-{time.time()}-{random.randint(1, 9999)}.srt'
      is_srt = tools.is_srt_string(name)
      Path(tmp_file).write_text(tools.process_text_to_srt_str(name) if not is_srt else name, encoding='utf-8')
      name = tmp_file

    cfg = {
      "name": name,
      "voice_role": data.get("voice_role"),
      "target_language_code": data.get('target_language_code'),
      "tts_type": int(data.get('tts_type', 0)),
      "voice_rate": data.get('voice_rate', "+0%"),
      "volume": data.get('volume', "+0%"),
      "pitch": data.get('pitch', "+0Hz"),
      "out_ext": data.get('out_ext', "mp3"),
      "voice_autorate": bool(data.get('voice_autorate', False)) if is_srt else False,
    }
    is_allow_lang = tts_model.is_allow_lang(langcode=cfg['target_language_code'], tts_type=cfg['tts_type'])
    if is_allow_lang is not True:
      return jsonify({"code": 4, "msg": is_allow_lang})
    is_input_api = tts_model.is_input_api(tts_type=cfg['tts_type'], return_str=True)
    if is_input_api is not True:
      return jsonify({"code": 5, "msg": is_input_api})

    obj = tools.format_video(name, None)
    obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
    obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
    Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
    cfg.update(obj)

    config.box_tts = 'ing'
    # Get user context for database integration
    user_id = get_current_user_id()
    trk = DubbingSrt.create_with_user_context(cfg=cfg, user_id=user_id)
    config.dubb_queue.append(trk)
    tools.set_process(text=f"Currently in queue No.{len(config.dubb_queue)}", uuid=obj['uuid'])
    return jsonify({'code': 0, 'task_id': obj['uuid']})


  # 第2个接口 /translate_srt
  """
  字幕翻译接口

  请求参数:
  类型 Content-Type:application/json

  请求数据:
  name:必须参数，字符串类型，需要翻译的srt字幕的绝对路径(需同本软件在同一设备)，或者直接传递合法的srt字幕格式内容
  translate_type：必须参数，整数类型，翻译渠道
  target_language:必须参数，字符串类型，要翻译到的目标语言代码。可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
  source_code:可选参数，字符串类型，原始字幕语言代码，可选同上

  返回数据
  返回类型：json格式，
  成功时返回，可根据task_id通过 task_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_translate_srt():
      res=requests.post("http://127.0.0.1:9011/translate_srt",json={
      "name":"C:/users/<USER>/videos/zh0.srt",
      "target_language":"en",
      "translate_type":0
      })
      print(res.json())
  ```

  """


  @app.route('/translate_srt', methods=['POST'])
  def translate_srt():
    data = request.json
    # 从请求数据中获取参数
    name = data.get('name', '').strip()
    if not name:
      return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
    name = get_local_file_path(request, name)

    if name.find("\n") == -1 and name.endswith('.srt'):
      if not Path(name).exists():
        return jsonify({"code": 1, "msg": f"The file {name} is not exist"})
    else:
      tmp_file = config.TEMP_DIR + f'/trans-srt-{time.time()}-{random.randint(1, 9999)}.srt'
      is_srt = tools.is_srt_string(name)
      Path(tmp_file).write_text(tools.process_text_to_srt_str(name) if not is_srt else name, encoding='utf-8')
      name = tmp_file

    cfg = {
      "translate_type": int(data.get('translate_type', 12)),
      "text_list": tools.get_subtitle_from_srt(name),
      "target_code": data.get('target_language'),
      "source_code": data.get('source_code', '')
    }
    is_allow = translator.is_allow_translate(translate_type=cfg['translate_type'], show_target=cfg['target_code'],
                                             return_str=True)
    if is_allow is not True:
      return jsonify({"code": 5, "msg": is_allow})
    obj = tools.format_video(name, None)
    obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
    obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
    Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
    cfg.update(obj)

    config.box_trans = 'ing'
    # Get user context for database integration
    user_id = get_current_user_id()
    trk = TranslateSrt.create_with_user_context(cfg=cfg, user_id=user_id)
    config.trans_queue.append(trk)
    tools.set_process(text=f"Currently in queue No.{len(config.trans_queue)}", uuid=obj['uuid'])
    return jsonify({'code': 0, 'task_id': obj['uuid']})


  # 第3个接口 /recogn
  """
  语音识别、音视频转字幕接口

  请求参数:
  类型 Content-Type:application/json

  请求数据:
  name:必须参数，字符串类型，需要翻译的音频或视频的绝对路径(需同本软件在同一设备)
  recogn_type:必须参数，数字类型，语音识别模式，0=faster-whisper本地模型识别，1=openai-whisper本地模型识别，2=Google识别api，3=zh_recogn中文识别，4=豆包模型识别，5=自定义识别API，6=OpenAI识别API
  model_name:必须参数faster-whisper和openai-whisper模式时的模型名字
  detect_language:必须参数，字符串类型，音视频中人类说话语言。中文zh，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
  split_type：可选参数，字符串类型，默认all：整体识别，可选avg：均等分割
  is_cuda:可选参数，布尔类型，是否启用CUDA加速，默认False

  返回数据
  返回类型：json格式，
  成功时返回，可根据task_id通过 task_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  失败时返回
  {"code":1,"msg":"错误信息"}

  示例
  def test_recogn():
      res=requests.post("http://127.0.0.1:9011/recogn",json={
      "name":"C:/Users/<USER>/Videos/10ass.mp4",
      "recogn_type":0,
      "split_type":"all",
      "model_name":"tiny",
      "is_cuda":False,
      "detect_language":"zh",
      })
      print(res.json())

  """


  @app.route('/recogn', methods=['POST'])
  def recogn():
    data = request.json
    # 从请求数据中获取参数
    name = data.get('name', '').strip()
    if not name:
      return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})

    name = get_local_file_path(request, name)

    if not Path(name).is_file():
      return jsonify({"code": 1, "msg": f"The file {name} is not exist"})

    cfg = {
      "recogn_type": int(data.get('recogn_type', 6)),
      "split_type": data.get('split_type', 'all'),
      "model_name": data.get('model_name', 'whisper-1'),
      "is_cuda": bool(data.get('is_cuda', False)),
      "detect_language": data.get('detect_language', '')
    }

    is_allow = recognition.is_allow_lang(langcode=cfg['detect_language'], recogn_type=cfg['recogn_type'])
    if is_allow is not True:
      return jsonify({"code": 5, "msg": is_allow})

    is_input = recognition.is_input_api(recogn_type=cfg['recogn_type'], return_str=True)
    if is_input is not True:
      return jsonify({"code": 5, "msg": is_input})

    obj = tools.format_video(name, None)
    obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
    obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
    Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
    cfg.update(obj)
    config.box_recogn = 'ing'
    # Get user context for database integration
    user_id = get_current_user_id()
    trk = SpeechToText.create_with_user_context(cfg=cfg, user_id=user_id)
    config.prepare_queue.append(trk)
    tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}", uuid=obj['uuid'])
    return jsonify({'code': 0, 'task_id': obj['uuid']})


  # 第4个接口
  """
  视频完整翻译接口


  请求参数:
  类型 Content-Type:application/json

  请求数据:
  name:必须参数，字符串类型，需要翻译的音频或视频的绝对路径(需同本软件在同一设备)
  recogn_type:必须参数，数字类型，语音识别模式，0=faster-whisper本地模型识别，1=openai-whisper本地模型识别，2=Google识别api，3=zh_recogn中文识别，4=豆包模型识别，5=自定义识别API，6=OpenAI识别API
  model_name:必须参数faster-whisper和openai-whisper模式时的模型名字
  split_type：可选参数，字符串类型，默认all：整体识别，可选avg：均等分割
  is_cuda:可选参数，布尔类型，是否启用CUDA加速，默认False
  translate_type：必须参数，整数类型，翻译渠道
  target_language:必须参数，字符串类型，要翻译到的目标语言代码。可选值 简体中文zh-cn，繁体zh-tw，英语en，法语fr，德语de，日语ja，韩语ko，俄语ru，西班牙语es，泰国语th，意大利语it，葡萄牙语pt，越南语vi，阿拉伯语ar，土耳其语tr，印地语hi，匈牙利语hu，乌克兰语uk，印尼语id，马来语ms，哈萨克语kk，捷克语cs，波兰语pl，荷兰语nl，瑞典语sv
  source_language:可选参数，字符串类型，原始字幕语言代码，可选同上
  tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
      6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API"
  voice_role:必须参数，字符串类型，对应配音渠道的角色名，edge-tts/azure-tts/302.ai(azure模型)时目标语言不同，角色名也不同，具体见底部
  voice_rate:可选参数，字符串类型，语速加减值，格式为：加速`+数字%`，减速`-数字%`
  volume:可选参数，字符串类型，音量变化值(仅配音渠道为edge-tts生效)，格式为 增大音量`+数字%`，降低音量`-数字%`
  pitch:可选参数，字符串类型，音调变化值(仅配音渠道为edge-tts生效)，格式为 调大音调`+数字Hz`,降低音量`-数字Hz`
  out_ext:可选参数，字符串类型，输出配音文件类型，mp3|wav|flac|aac,默认wav
  voice_autorate:可选参数，布尔类型，默认False，是否自动加快语速，以便与字幕对齐
  subtitle_type:可选参数，整数类型，默认0，字幕嵌入类型，0=不嵌入字幕，1=嵌入硬字幕，2=嵌入软字幕，3=嵌入双硬字幕，4=嵌入双软字幕
  append_video：可选参数，布尔类型，默认False，如果配音后音频时长大于视频，是否延长视频末尾
  only_video:可选参数，布尔类型，默认False，是否只生成视频文件，不生成字幕音频等

  返回数据
  返回类型：json格式，
  成功时返回，可根据task_id通过 task_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  失败时返回
  {"code":1,"msg":"错误信息"}

  示例
  def test_trans_video():
      res=requests.post("http://127.0.0.1:9011/trans_video",json={
      "name":"C:/Users/<USER>/Videos/10ass.mp4",

      "recogn_type":0,
      "split_type":"all",
      "model_name":"tiny",

      "translate_type":0,
      "source_language":"zh-cn",
      "target_language":"en",

      "tts_type":0,
      "voice_role":"zh-CN-YunjianNeural",
      "voice_rate":"+0%",
      "volume":"+0%",
      "pitch":"+0Hz",
      "voice_autorate":True,
      "video_autorate":True,

      "is_separate":False,
      "back_audio":"",

      "subtitle_type":1,
      "append_video":False,

      "is_cuda":False,
      })
      print(res.json())

  """


  @app.route('/trans_video', methods=['POST'])
  def trans_video():
    data = request.json
    name = data.get('name', '')
    if not name:
      return jsonify({"code": 1, "msg": "The parameter name is not allowed to be empty"})
    if not Path(name).exists():
      return jsonify({"code": 1, "msg": f"The file {name} is not exist"})

    cfg = {
      # 通用
      "name": name,

      "is_separate": bool(data.get('is_separate', False)),
      "back_audio": data.get('back_audio', ''),

      # 识别
      "recogn_type": int(data.get('recogn_type', 0)),
      "split_type": data.get('split_type', 'all'),
      "model_name": data.get('model_name', 'tiny'),
      "cuda": bool(data.get('is_cuda', False)),

      "subtitles": data.get("subtitles", ""),

      # 翻译
      "translate_type": int(data.get('translate_type', 0)),
      "target_language": data.get('target_language'),
      "source_language": data.get('source_language'),

      # 配音
      "tts_type": int(data.get('tts_type', 0)),
      "voice_role": data.get('voice_role', ''),
      "voice_rate": data.get('voice_rate', '+0%'),
      "voice_autorate": bool(data.get('voice_autorate', False)),
      "video_autorate": bool(data.get('video_autorate', False)),
      "volume": data.get('volume', '+0%'),
      "pitch": data.get('pitch', '+0Hz'),

      "subtitle_type": int(data.get('subtitle_type', 0)),
      "append_video": bool(data.get('append_video', False)),

      "is_batch": True,
      "app_mode": "biaozhun",

      "only_video": bool(data.get('only_video', False))

    }
    if not cfg['subtitles']:
      is_allow = recognition.is_allow_lang(langcode=cfg['target_language'], recogn_type=cfg['recogn_type'])
      if is_allow is not True:
        return jsonify({"code": 5, "msg": is_allow})

      is_input = recognition.is_input_api(recogn_type=cfg['recogn_type'], return_str=True)
      if is_input is not True:
        return jsonify({"code": 5, "msg": is_input})
    if cfg['source_language'] != cfg['target_language']:
      is_allow = translator.is_allow_translate(translate_type=cfg['translate_type'], show_target=cfg['target_language'],
                                               return_str=True)
      if is_allow is not True:
        return jsonify({"code": 5, "msg": is_allow})

    if cfg['voice_role'] and cfg['voice_role'].lower() != 'no' and cfg['target_language']:
      is_allow_lang = tts_model.is_allow_lang(langcode=cfg['target_language'], tts_type=cfg['tts_type'])
      if is_allow_lang is not True:
        return jsonify({"code": 4, "msg": is_allow_lang})
      is_input_api = tts_model.is_input_api(tts_type=cfg['tts_type'], return_str=True)
      if is_input_api is not True:
        return jsonify({"code": 5, "msg": is_input_api})

    obj = tools.format_video(name, None)
    obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
    obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
    Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
    cfg.update(obj)

    config.current_status = 'ing'
    # Get user context for database integration
    user_id = get_current_user_id()
    trk = TransCreate.create_with_user_context(cfg=cfg, user_id=user_id)
    config.prepare_queue.append(trk)
    tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}", uuid=obj['uuid'])
    #
    return jsonify({'code': 0, 'task_id': obj['uuid']})


  # 第5个接口 /voice_list
  """
  获取指定TTS服务的语音角色列表接口

  请求参数:
  类型 GET请求，URL参数

  请求数据:
  tts_type:必须参数，数字类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS",3=302.AI,4="FishTTS",5="Azure-TTS",
      6="GPT-SoVITS",7="clone-voice",8="OpenAI TTS",9="Elevenlabs.io",10="Google TTS",11="自定义TTS API",
      12="字节火山语音合成",13="F5-TTS",14="kokoro-TTS",15="Google Cloud TTS",16="Gemini TTS",17="AusyncLab TTS"
  language:可选参数，字符串类型，语言代码过滤，如zh、en、fr等

  返回数据
  返回类型：json格式，
  成功时返回
  {"code":0,"msg":"ok","data":{"service_name":"Edge-TTS","service_id":0,"voices":[{"id":"voice1","name":"voice1"},{"id":"voice2","name":"voice2"},{"id":"voice3","name":"voice3"},{"id":"voice4","name":"voice4"}]}}

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_voice_list():
      res = requests.get("http://127.0.0.1:9011/voice_list?tts_type=0&language=zh")
      print(res.json())
  ```

  """


  @app.route('/voices', methods=['GET'])
  @app.route('/voice_list', methods=['GET'])
  def voice_list():
    tts_type = request.args.get('tts_type')
    language = request.args.get('language', '')

    if tts_type is None:
      return jsonify({"code": 1, "msg": "The parameter tts_type is required"})

    try:
      tts_type = int(tts_type)
    except (ValueError, TypeError):
      return jsonify({"code": 1, "msg": "The parameter tts_type must be a valid integer"})

    if tts_type < 0 or tts_type >= len(tts_model.TTS_NAME_LIST):
      return jsonify({"code": 1, "msg": f"Invalid tts_type. Must be between 0 and {len(tts_model.TTS_NAME_LIST) - 1}"})

    service_name = tts_model.TTS_NAME_LIST[tts_type]
    all_voices = []

    try:
      if tts_type == tts_model.EDGE_TTS:
        voice_data = tools.get_edge_rolelist(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"zh": [{"id": "voice1", "name": "voice1"}], "en": [{"id": "voice3", "name": "voice3"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      elif tts_type == tts_model.COSYVOICE_TTS:
        voice_data = tools.get_cosyvoice_role(return_objects=True)
        if voice_data:
          all_voices = list(voice_data.values())
      elif tts_type == tts_model.CHATTTS:
        voice_data = enhanced_config.get_tts_config('chattts').get('voicelist', [])
        voice_names = ["No"] + voice_data
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.AI302_TTS:
        voice_data = tools.get_302ai(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"zh": [{"id": "voice1", "name": "voice1"}], "en": [{"id": "voice2", "name": "voice2"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      elif tts_type == tts_model.FISHTTS:
        voice_data = tools.get_fishtts_role(return_objects=True)
        if voice_data:
          all_voices = list(voice_data.values())
      elif tts_type == tts_model.AZURE_TTS:
        voice_data = tools.get_azure_rolelist(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"zh": [{"id": "voice1", "name": "voice1"}], "en": [{"id": "voice2", "name": "voice2"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      elif tts_type == tts_model.GPTSOVITS_TTS:
        voice_data = tools.get_gptsovits_role(return_objects=True)
        if voice_data:
          all_voices = list(voice_data.values())
      elif tts_type == tts_model.CLONE_VOICE_TTS:
        voice_data = enhanced_config.get_tts_config('clone').get('voicelist', ["clone"])
        all_voices = [{"id": voice, "name": voice} for voice in voice_data]
      elif tts_type == tts_model.OPENAI_TTS:
        voice_data = enhanced_config.get_tts_config('openai').get('role',
                                                                  "alloy,ash,coral,echo,fable,onyx,nova,sage,shimmer,verse").split(
          ',')
        voice_names = [v.strip() for v in voice_data if v.strip()]
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.ELEVENLABS_TTS:
        voice_data = tools.get_elevenlabs_role(return_objects=True)
        if voice_data:
          all_voices = voice_data
      elif tts_type == tts_model.ELEVENLABS_TTS_WS:
        voice_data = tools.get_elevenlabs_role(return_objects=True)
        if voice_data:
          all_voices = voice_data
      elif tts_type == tts_model.GOOGLE_TTS:
        voice_names = ["en-US-Standard-A", "en-US-Standard-B", "zh-CN-Standard-A", "zh-CN-Standard-B"]
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.TTS_API:
        voice_data = enhanced_config.get_tts_config('ttsapi').get('voice_role', '').strip().split(',')
        voice_names = [v.strip() for v in voice_data if v.strip()] if voice_data and voice_data[0] else []
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.VOLCENGINE_TTS:
        voice_data = tools.get_volcenginetts_rolelist(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"zh": [{"id": "voice1", "name": "voice1"}], "en": [{"id": "voice2", "name": "voice2"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      elif tts_type == tts_model.F5_TTS:
        voice_data = tools.get_f5tts_role(return_objects=True)
        if voice_data:
          all_voices = list(voice_data.values())
      elif tts_type == tts_model.KOKORO_TTS:
        voice_data = tools.get_kokoro_rolelist(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"en": [{"id": "voice1", "name": "voice1"}], "zh": [{"id": "voice2", "name": "voice2"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      elif tts_type == tts_model.GOOGLECLOUD_TTS:
        voice_names = ["en-US-Standard-A", "en-US-Standard-B", "zh-CN-Standard-A", "zh-CN-Standard-B"]
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.GEMINI_TTS:
        voice_names = ["Puck", "Charon", "Kore", "Fenrir", "zh-voice-1", "zh-voice-2"]
        all_voices = [{"id": voice, "name": voice} for voice in voice_names]
      elif tts_type == tts_model.AUSYNCLAB_TTS:
        voice_data = tools.get_ausynclab_rolelist(return_objects=True)
        if voice_data:
          # voice_data is a dict like {"en": [{"id": "voice1", "name": "voice1"}], "zh": [{"id": "voice2", "name": "voice2"}]}
          for lang_voices in voice_data.values():
            all_voices.extend(lang_voices)
      else:
        all_voices = []

      # Remove duplicates and filter by language if specified
      # Since all_voices now contains objects, we need to deduplicate by voice name
      seen_names = set()
      unique_voice_objects = []
      for voice_obj in all_voices:
        voice_name = voice_obj.get('name', voice_obj.get('id', ''))
        if voice_name not in seen_names:
          seen_names.add(voice_name)
          unique_voice_objects.append(voice_obj)

      # Filter by language if specified
      # if language:
      #   filtered_voice_objects = []
      #   language_lower = language.lower()
      #   for voice_obj in unique_voice_objects:
      #     voice_name = voice_obj.get('name', voice_obj.get('id', ''))
      #     # Check if voice name contains language code or starts with language prefix
      #     if (language_lower in voice_name.lower() or
      #       voice_name.lower().startswith(language_lower) or
      #       (language_lower == 'zh' and (
      #         'zh-' in voice_name.lower() or 'chinese' in voice_name.lower() or '中文' in voice_name)) or
      #       (language_lower == 'en' and ('en-' in voice_name.lower() or 'english' in voice_name.lower())) or
      #       (language_lower == 'ja' and (
      #         'ja-' in voice_name.lower() or 'japanese' in voice_name.lower() or '日语' in voice_name)) or
      #       (language_lower == 'ko' and (
      #         'ko-' in voice_name.lower() or 'korean' in voice_name.lower() or '韩语' in voice_name))):
      #       filtered_voice_objects.append(voice_obj)
      #   unique_voice_objects = filtered_voice_objects

      # unique_voice_objects already contains objects with id and name fields
      unique_voices = unique_voice_objects

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {
          "service_name": service_name,
          "service_id": tts_type,
          "voices": unique_voices
        }
      })

    except Exception as e:
      config.logger.error(f"Error getting voice list for tts_type {tts_type}: {str(e)}")
      return jsonify({"code": 1, "msg": f"Error retrieving voice list: {str(e)}"})


  def generate_video_thumbnail(video_path, thumbnail_path, seek_time="00:00:01"):
    """
    Generate a thumbnail from a video file using FFmpeg

    Args:
        video_path (str): Path to the input video file
        thumbnail_path (str): Path where the thumbnail should be saved
        seek_time (str): Time position to extract frame from (default: 1 second)

    Returns:
        bool: True if thumbnail generation succeeded, False otherwise
    """
    try:
      # FFmpeg command to extract a frame as thumbnail
      cmd = [
        "-y",  # Overwrite output file
        "-i", video_path,  # Input video
        "-ss", seek_time,  # Seek to specific time
        "-vframes", "1",  # Extract only 1 frame
        "-q:v", "2",  # High quality JPEG
        "-vf", "scale=320:-1",  # Scale to width 320, maintain aspect ratio
        thumbnail_path
      ]

      # Use the existing runffmpeg function from tools
      result = tools.runffmpeg(cmd)

      # Check if thumbnail file was created successfully
      if result and Path(thumbnail_path).exists():
        config.logger.info(f"Thumbnail generated successfully: {thumbnail_path}")
        return True
      else:
        config.logger.warning(f"Thumbnail generation failed for: {video_path}")
        return False

    except Exception as e:
      config.logger.error(f"Error generating thumbnail for {video_path}: {str(e)}")
      return False


  def is_video_file(filename):
    """
    Check if a file is a video file based on its extension

    Args:
        filename (str): The filename to check

    Returns:
        bool: True if the file is a video file, False otherwise
    """
    if not filename:
      return False

    file_ext = Path(filename).suffix.lower().lstrip('.')
    return file_ext in config.VIDEO_EXTS


  # 第6个接口 /upload
  """
  文件上传接口

  支持两种上传方式:
  1. 文件上传 (Content-Type: multipart/form-data)
  2. URL上传 (Content-Type: application/json)

  文件上传请求参数:
  file: 必须参数，文件类型，要上传的文件
  name: 可选参数，字符串类型，用户自定义的媒体名称
  ignore_save_library: 可选参数，字符串类型，设置为"1"时跳过保存到媒体库

  URL上传请求参数 (JSON):
  url: 必须参数，字符串类型，要下载的文件URL
  name: 可选参数，字符串类型，用户自定义的媒体名称
  ignore_save_library: 可选参数，字符串类型，设置为"1"时跳过保存到媒体库

  URL查询参数:
  ignore_save_library: 可选参数，设置为"1"时跳过保存到媒体库 (例: /upload?ignore_save_library=1)

  返回数据
  返回类型：json格式，
  成功时返回
  {"code":0,"msg":"ok","data":{"file_path":"/usr/lib/media/date_str/hash_filename.ext","url":"http://127.0.0.1:9011/file/date_str/hash_filename.ext","thumbnail_path":"/usr/lib/media/date_str/hash_filename_thumb.jpg","thumbnail_url":"http://127.0.0.1:9011/file/date_str/hash_filename_thumb.jpg","name":"用户提供的名称"}}

  注意：thumbnail_path和thumbnail_url字段仅在上传视频文件时返回
  注意：当ignore_save_library=1时，不会返回media_id字段，因为文件未保存到媒体库

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  # 文件上传示例
  def test_file_upload():
      with open("test.mp4", "rb") as f:
          files = {"file": f}
          data = {"name": "My Video"}  # 可选的自定义名称
          res = requests.post("http://127.0.0.1:9011/upload", files=files, data=data)
          print(res.json())

  # URL上传示例
  def test_url_upload():
      data = {"url": "https://example.com/video.mp4", "name": "Downloaded Video"}
      res = requests.post("http://127.0.0.1:9011/upload", json=data)
      print(res.json())

  # 跳过保存到媒体库示例
  def test_upload_without_library():
      # 方式1: 使用URL参数
      res = requests.post("http://127.0.0.1:9011/upload?ignore_save_library=1", files={"file": f})
      # 方式2: 使用表单数据
      data = {"ignore_save_library": "1"}
      res = requests.post("http://127.0.0.1:9011/upload", files={"file": f}, data=data)
      # 方式3: 使用JSON数据 (URL上传)
      data = {"url": "https://example.com/video.mp4", "ignore_save_library": "1"}
      res = requests.post("http://127.0.0.1:9011/upload", json=data)
  ```

  """


  @app.route('/upload', methods=['OPTIONS'])
  def handle_upload_options():
    """Handle CORS preflight for /upload endpoint"""
    return '', 200

  @app.route('/upload', methods=['POST'])
  def upload_file():
    # Import required modules at the beginning
    import mimetypes
    from videotrans.util import tools

    # Extract ignore_save_library parameter from URL query parameters
    ignore_save_library = request.args.get('ignore_save_library', '').strip() == '1'

    # Determine request type - file upload or URL upload
    content_type = request.content_type or ''
    is_url_upload = False
    url_to_download = None

    # Check if this is a JSON request with URL
    user_provided_name = None
    if 'application/json' in content_type:
      try:
        data = request.get_json()
        if data and 'url' in data:
          url_to_download = data['url'].strip()
          if not url_to_download:
            return jsonify({"code": 1, "msg": "URL cannot be empty"})
          is_url_upload = True
          # Extract optional name field from JSON
          user_provided_name = data.get('name', '').strip() if data.get('name') else None
          # Extract ignore_save_library from JSON data (overrides URL parameter)
          if 'ignore_save_library' in data:
            ignore_save_library = str(data.get('ignore_save_library', '')).strip() == '1'
        else:
          return jsonify({"code": 1, "msg": "URL parameter is required for JSON requests"})
      except Exception as e:
        return jsonify({"code": 1, "msg": f"Invalid JSON data: {str(e)}"})

    # Handle traditional file upload
    elif 'multipart/form-data' in content_type:
      # 检查是否有文件在请求中
      if 'file' not in request.files:
        return jsonify({"code": 1, "msg": "No file provided"})

      file = request.files['file']

      # 检查文件名是否为空
      if file.filename == '':
        return jsonify({"code": 1, "msg": "No file selected"})

      # Extract optional name field from form data
      user_provided_name = request.form.get('name', '').strip() if request.form.get('name') else None
      # Extract ignore_save_library from form data (overrides URL parameter)
      if 'ignore_save_library' in request.form:
        ignore_save_library = request.form.get('ignore_save_library', '').strip() == '1'

    else:
      return jsonify({"code": 1, "msg": "Content-Type must be multipart/form-data for file upload or application/json for URL upload"})

    # Process the upload (either file or URL)
    if is_url_upload:
      try:
        # Validate URL format
        if not tools.validate_url(url_to_download):
          return jsonify({"code": 1, "msg": "Invalid URL format"})

        # Get user ID for media library (optional for backward compatibility)
        user_id = get_current_user_id()

        # 创建上传目录 - organize by user if user_id available
        date_str = tools.get_current_time_as_yymmddhhmmss(format='ymd')
        if user_id:
          upload_dir = f'/usr/lib/media/{user_id}/{date_str}'
        else:
          upload_dir = f'/usr/lib/media/{date_str}'
        Path(upload_dir).mkdir(parents=True, exist_ok=True)

        # Extract filename from URL
        original_filename = tools.extract_filename_from_url(url_to_download)

        # Generate temporary download path
        temp_download_path = os.path.join(upload_dir, f"temp_{original_filename}")

        # Download the file
        success, error_msg, downloaded_size = tools.download_file_from_url(
          url_to_download,
          temp_download_path,
          timeout=60,
          max_size_mb=500
        )

        if not success:
          return jsonify({"code": 1, "msg": f"Failed to download file: {error_msg}"})

        # Read downloaded file content for hash generation
        with open(temp_download_path, 'rb') as f:
          file_content = f.read()

        # Get file extension
        file_ext = Path(original_filename).suffix
        if not file_ext:
          # Try to detect extension from content or use generic
          mime_type = mimetypes.guess_type(original_filename)[0]
          if mime_type:
            import mimetypes
            ext = mimetypes.guess_extension(mime_type)
            if ext:
              file_ext = ext
          if not file_ext:
            file_ext = '.bin'  # Generic extension

        # 生成hash文件名 (使用文件内容 + 时间戳)
        hash_input = f"{file_content.hex()}-{time.time()}"
        file_hash = tools.get_md5(hash_input)
        hash_filename = f"{file_hash}{file_ext}"

        # 完整文件路径
        file_path = os.path.join(upload_dir, hash_filename)

        # Move temp file to final location
        os.rename(temp_download_path, file_path)

        # Get file metadata
        mime_type = mimetypes.guess_type(original_filename)[0]
        media_type = tools.detect_media_type(original_filename, mime_type)

        # Set file size from downloaded content
        file_size = len(file_content)

      except Exception as e:
        # Clean up any temporary files
        try:
          if 'temp_download_path' in locals() and os.path.exists(temp_download_path):
            os.unlink(temp_download_path)
        except:
          pass
        return jsonify({"code": 1, "msg": f"URL upload failed: {str(e)}"})

    else:
      # Handle traditional file upload
      file = request.files['file']
      if file:
        try:
          # Get user ID for media library (optional for backward compatibility)
          user_id = get_current_user_id()

          # 创建上传目录 - organize by user if user_id available
          date_str = tools.get_current_time_as_yymmddhhmmss(format='ymd')
          if user_id:
            upload_dir = f'/usr/lib/media/{user_id}/{date_str}'
          else:
            upload_dir = f'/usr/lib/media/{date_str}'
          Path(upload_dir).mkdir(parents=True, exist_ok=True)

          # 获取文件扩展名
          original_filename = file.filename
          file_ext = Path(original_filename).suffix

          # 读取文件内容用于生成hash
          file_content = file.read()
          file.seek(0)  # 重置文件指针

          # 生成hash文件名 (使用文件内容 + 时间戳)
          hash_input = f"{file_content.hex()}-{time.time()}"
          file_hash = tools.get_md5(hash_input)
          hash_filename = f"{file_hash}{file_ext}"

          # 完整文件路径
          file_path = os.path.join(upload_dir, hash_filename)

          # 保存文件
          file.save(file_path)

          # Get file metadata
          mime_type = mimetypes.guess_type(original_filename)[0]
          media_type = tools.detect_media_type(original_filename, mime_type)

          # Set file size from uploaded content
          file_size = len(file_content)

        except Exception as e:
          return jsonify({"code": 1, "msg": f"File upload failed: {str(e)}"})

    # Common processing for both upload types
    try:
        # 生成相对路径用于数据库存储
        if user_id:
          relative_file_path = f"/{user_id}/{date_str}/{hash_filename}"
          file_url = f"{get_base_url()}/file/{user_id}/{date_str}/{hash_filename}"
        else:
          relative_file_path = f"/{date_str}/{hash_filename}"
          file_url = f"{get_base_url()}/file/{date_str}/{hash_filename}"

        # 准备响应数据
        response_data = {
          "url": file_url,
          "filename": hash_filename,
          "original_filename": original_filename,
          "name": user_provided_name,  # User-provided display name
          "file_size": file_size,
          "mime_type": mime_type,
          "media_type": media_type
        }

        thumbnail_path = None
        thumbnail_url = None
        relative_thumbnail_path = None

        # 如果是视频文件，生成缩略图和提取元数据
        video_metadata = {}
        if is_video_file(original_filename):
          try:
            # 生成缩略图文件名
            thumbnail_filename = f"{file_hash}_thumb.jpg"
            thumbnail_path = os.path.join(upload_dir, thumbnail_filename)

            # 生成缩略图
            if generate_video_thumbnail(file_path, thumbnail_path):
              # 生成相对路径用于数据库存储
              if user_id:
                relative_thumbnail_path = f"/{user_id}/{date_str}/{thumbnail_filename}"
                thumbnail_url = f"{get_base_url()}/file/{user_id}/{date_str}/{thumbnail_filename}"
              else:
                relative_thumbnail_path = f"/{date_str}/{thumbnail_filename}"
                thumbnail_url = f"{get_base_url()}/file/{date_str}/{thumbnail_filename}"

              response_data["thumbnail_path"] = relative_thumbnail_path
              response_data["thumbnail_url"] = thumbnail_url
              config.logger.info(f"Thumbnail generated for video: {original_filename}")
            else:
              config.logger.warning(f"Failed to generate thumbnail for video: {original_filename}")
              thumbnail_path = None
              relative_thumbnail_path = None
          except Exception as thumb_e:
            # 缩略图生成失败不应该影响文件上传
            config.logger.error(f"Thumbnail generation error for {original_filename}: {str(thumb_e)}")
            thumbnail_path = None
            relative_thumbnail_path = None

          # 提取视频元数据
          try:
            video_info = tools.get_video_info(file_path)
            video_metadata = {
              "duration": video_info.get("time", 0),  # Duration in milliseconds
              "duration_seconds": round(video_info.get("time", 0) / 1000, 2),  # Duration in seconds
              "width": video_info.get("width", 0),
              "height": video_info.get("height", 0),
              "fps": video_info.get("video_fps", 0),
              "video_codec": video_info.get("video_codec_name", ""),
              "audio_codec": video_info.get("audio_codec_name", ""),
              "color_format": video_info.get("color", ""),
              "streams_count": video_info.get("streams_len", 0),
              "audio_streams": video_info.get("streams_audio", 0)
            }

            # 添加视频元数据到响应
            response_data.update(video_metadata)
            config.logger.info(f"Video metadata extracted for: {original_filename} - Duration: {video_metadata['duration_seconds']}s, Resolution: {video_metadata['width']}x{video_metadata['height']}")

          except Exception as meta_e:
            # 元数据提取失败不应该影响文件上传
            config.logger.error(f"Video metadata extraction error for {original_filename}: {str(meta_e)}")
            video_metadata = {}

        # Save to media library if user_id is available and ignore_save_library is not set
        if user_id and not ignore_save_library:
          try:
            # Prepare metadata for database storage
            metadata_for_db = video_metadata.copy() if video_metadata else {}

            media_id = media_manager.create_media_entry(
              user_id=user_id,
              filename=hash_filename,
              original_filename=original_filename,
              file_path=relative_file_path,  # Use relative path instead of absolute
              file_size=len(file_content),
              mime_type=mime_type,
              media_type=media_type,
              metadata=metadata_for_db,  # Include video metadata
              name=user_provided_name  # User-provided display name
            )

            # Update with video metadata and thumbnail if available
            update_params = {}
            if relative_thumbnail_path and thumbnail_path and Path(thumbnail_path).exists():
              update_params['thumbnail_path'] = relative_thumbnail_path

            # Add video metadata to individual database columns
            if video_metadata:
              if 'duration_seconds' in video_metadata:
                update_params['duration'] = video_metadata['duration_seconds']
              if 'width' in video_metadata:
                update_params['width'] = video_metadata['width']
              if 'height' in video_metadata:
                update_params['height'] = video_metadata['height']
              if 'video_codec' in video_metadata:
                update_params['codec'] = video_metadata['video_codec']
              if 'fps' in video_metadata:
                update_params['frame_rate'] = video_metadata['fps']

            # Update metadata if we have any parameters to update
            if update_params:
              success = media_manager.update_media_metadata(
                user_id=user_id,
                media_id=media_id,
                **update_params
              )
              if success:
                config.logger.info(f"Video metadata saved to database for media ID {media_id}: {update_params}")
              else:
                config.logger.error(f"Failed to save video metadata to database for media ID {media_id}")
            else:
              config.logger.info(f"No video metadata to update for media ID {media_id}")

            response_data["media_id"] = media_id
            config.logger.info(f"File saved to media library: {original_filename} (ID: {media_id})")

          except Exception as media_e:
            # Media library save failure shouldn't break upload
            config.logger.error(f"Failed to save to media library: {str(media_e)}")
        elif ignore_save_library:
          config.logger.info(f"Skipping media library save due to ignore_save_library parameter: {original_filename}")

        return jsonify({
          "code": 0,
          "msg": "ok",
          "data": response_data
        })

    except Exception as e:
        return jsonify({"code": 1, "msg": f"Upload failed: {str(e)}"})

    return jsonify({"code": 1, "msg": "Invalid file"})


  # Authentication decorator for OpenAI-compatible endpoints
  def get_base_url():
    return enhanced_config.get_system_setting('server_host', f"http://{HOST}:{PORT}")


  def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
      auth_header = request.headers.get('Authorization')
      if not auth_header:
        return jsonify({
          "error": {
            "message": "You didn't provide an API key.",
            "type": "invalid_request_error",
            "param": None,
            "code": None
          }
        }), 401

      if not auth_header.startswith('Bearer '):
        return jsonify({
          "error": {
            "message": "Invalid authorization header format. Expected 'Bearer <token>'.",
            "type": "invalid_request_error",
            "param": None,
            "code": None
          }
        }), 401

      # Extract the token (everything after 'Bearer ')
      token = auth_header[7:]
      if not token:
        return jsonify({
          "error": {
            "message": "You didn't provide an API key.",
            "type": "invalid_request_error",
            "param": None,
            "code": None
          }
        }), 401

      # For now, we'll accept any non-empty token
      # In production, you might want to validate against a database or config
      return f(*args, **kwargs)

    return decorated_function


  # OpenAI-compatible Chat Completions endpoint
  """
  OpenAI-compatible Chat Completions API

  请求参数:
  类型 Content-Type: application/json
  Authorization: Bearer API_KEY

  请求数据:
  model: 必须参数，字符串类型，模型名称 (支持 gemini-1.5-pro, gpt-4, gpt-3.5-turbo 等)
  messages: 必须参数，数组类型，对话消息列表
  temperature: 可选参数，浮点数类型，控制随机性 (0.0-2.0)
  max_tokens: 可选参数，整数类型，最大生成token数
  top_p: 可选参数，浮点数类型，核采样参数
  stream: 可选参数，布尔类型，是否流式返回 (暂不支持)

  返回数据:
  返回类型：json格式，OpenAI兼容格式
  成功时返回:
  {
      "id": "chatcmpl-xxx",
      "object": "chat.completion",
      "created": 1234567890,
      "model": "gemini-1.5-pro",
      "choices": [
          {
              "index": 0,
              "message": {
                  "role": "assistant",
                  "content": "回复内容"
              },
              "finish_reason": "stop"
          }
      ],
      "usage": {
          "prompt_tokens": 10,
          "completion_tokens": 20,
          "total_tokens": 30
      }
  }

  失败时返回:
  {
      "error": {
          "message": "错误信息",
          "type": "invalid_request_error",
          "param": null,
          "code": null
      }
  }
  """


  @app.route('/v1/chat/completions', methods=['POST'])
  def chat_completions():
    try:
      data = request.json
      if not data:
        return jsonify({
          "error": {
            "message": "Request body must be JSON",
            "type": "invalid_request_error",
            "param": None,
            "code": None
          }
        }), 400

      # Validate required parameters
      model = data.get('model', 'gemini-2.5-flash-preview-05-20')
      chat_complete_model = enhanced_config.config_manager.get('chat_complete_model')
      if chat_complete_model and len(chat_complete_model) > 0:
        model = chat_complete_model

      messages = data.get('messages')

      if not model:
        return jsonify({
          "error": {
            "message": "Missing required parameter: 'model'",
            "type": "invalid_request_error",
            "param": "model",
            "code": None
          }
        }), 400

      if not messages:
        return jsonify({
          "error": {
            "message": "Missing required parameter: 'messages'",
            "type": "invalid_request_error",
            "param": "messages",
            "code": None
          }
        }), 400

      if not isinstance(messages, list) or len(messages) == 0:
        return jsonify({
          "error": {
            "message": "'messages' must be a non-empty array",
            "type": "invalid_request_error",
            "param": "messages",
            "code": None
          }
        }), 400

      # Extract optional parameters
      temperature = data.get('temperature', 0.7)
      max_tokens = data.get('max_tokens', 4096)
      top_p = data.get('top_p', 1.0)
      stream = data.get('stream', False)

      if stream:
        return jsonify({
          "error": {
            "message": "Streaming is not currently supported",
            "type": "invalid_request_error",
            "param": "stream",
            "code": None
          }
        }), 400

      # Generate response based on model
      response_content = _generate_chat_response(model, messages, temperature, max_tokens, top_p)

      # Create OpenAI-compatible response
      completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
      created_timestamp = int(time.time())

      return jsonify({
        "id": completion_id,
        "object": "chat.completion",
        "created": created_timestamp,
        "model": model,
        "choices": [
          {
            "index": 0,
            "message": {
              "role": "assistant",
              "content": response_content
            },
            "finish_reason": "stop"
          }
        ],
        "usage": {
          "prompt_tokens": _estimate_tokens(messages),
          "completion_tokens": _estimate_tokens([{"content": response_content}]),
          "total_tokens": _estimate_tokens(messages) + _estimate_tokens([{"content": response_content}])
        }
      })

    except Exception as e:
      config.logger.error(f"Chat completions error: {str(e)}")
      return jsonify({
        "error": {
          "message": f"Internal server error: {str(e)}",
          "type": "internal_server_error",
          "param": None,
          "code": None
        }
      }), 500


  # 静态文件服务 - 为上传的文件提供访问
  @app.route('/usr/lib/media/<filename>')
  def uploaded_file(filename):
    upload_dir = '/usr/lib/media'
    try:
      return send_from_directory(upload_dir, filename)
    except FileNotFoundError:
      return jsonify({"code": 1, "msg": "File not found"}), 404


  # 新的文件服务接口 - 支持路径参数访问上传的文件
  """
  文件访问接口

  请求参数:
  类型: GET

  请求路径:
  /file/<path:filepath> - filepath是相对于/usr/lib/media/的文件路径，支持子目录

  返回数据:
  成功时返回文件内容
  失败时返回404或错误信息

  示例:
  GET /file/20241201/abc123.mp4  # 访问 /usr/lib/media/20241201/abc123.mp4
  GET /file/abc123.mp4           # 访问 /usr/lib/media/abc123.mp4
  """


  @app.route('/file/<path:filepath>')
  def serve_uploaded_file(filepath):
    # 安全检查：防止目录遍历攻击
    if '..' in filepath:
      return jsonify({"code": 1, "msg": "Invalid file path"}), 400

    # Remove leading slash if present (our relative paths start with /)
    if filepath.startswith('/'):
      filepath = filepath[1:]

    # 基础上传目录
    base_upload_dir = '/usr/lib/media'

    # 构建完整文件路径
    full_file_path = os.path.join(base_upload_dir, filepath)

    # 检查文件是否存在
    if not os.path.isfile(full_file_path):
      return jsonify({"code": 1, "msg": "File not found"}), 404

    # 安全检查：确保文件在允许的目录内
    try:
      # 获取规范化的绝对路径
      real_file_path = os.path.realpath(full_file_path)
      real_base_path = os.path.realpath(base_upload_dir)

      # 检查文件是否在基础目录内
      if not real_file_path.startswith(real_base_path):
        return jsonify({"code": 1, "msg": "Access denied"}), 403
    except Exception:
      return jsonify({"code": 1, "msg": "Invalid file path"}), 400

    try:
      # 获取文件所在目录和文件名
      file_dir = os.path.dirname(full_file_path)
      filename = os.path.basename(full_file_path)

      # 使用Flask的send_from_directory发送文件
      return send_from_directory(file_dir, filename)
    except Exception as e:
      return jsonify({"code": 1, "msg": f"Error serving file: {str(e)}"}), 500


  # Helper functions for chat completions
  def _generate_chat_response(model, messages, temperature, max_tokens, top_p):
    """Generate chat response based on the specified model"""
    try:
      # Convert messages to text for processing
      conversation_text = ""
      for msg in messages:
        role = msg.get('role', 'user')
        content = msg.get('content', '')
        conversation_text += f"{role}: {content}\n"

      # Route to appropriate AI service based on model name
      if 'gemini' in model.lower():
        return _generate_gemini_response(conversation_text, model, temperature, max_tokens)
      elif 'gpt' in model.lower() or 'chatgpt' in model.lower():
        return _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p)
      elif 'claude' in model.lower():
        return _generate_claude_response(conversation_text, model, temperature, max_tokens)
      else:
        # Default to local LLM or first available service
        return _generate_default_response(conversation_text, model, temperature, max_tokens, top_p)

    except Exception as e:
      config.logger.error(f"Error generating chat response: {str(e)}")
      raise Exception(f"Failed to generate response: {str(e)}")


  def _generate_gemini_response(conversation_text, model, temperature, max_tokens):
    """Generate response using Gemini API"""
    try:
      # Check if Gemini is configured
      api_key = enhanced_config.get_api_key('gemini')
      if not api_key:
        raise Exception("Gemini API key not configured. Please set gemini_key in configuration.")

      # Configure Gemini
      genai.configure(api_key=api_key)

      # Use the configured model or default
      gemini_model = enhanced_config.config_manager.get('gemini_model', 'gemini-1.5-pro')

      # Create model instance
      model_instance = genai.GenerativeModel(
        model_name=gemini_model,
        generation_config={
          "max_output_tokens": max_tokens,
          "temperature": temperature
        }
      )

      # Generate response
      response = model_instance.generate_content(conversation_text)

      if response and response.text:
        return response.text.strip()
      else:
        raise Exception("Empty response from Gemini")

    except Exception as e:
      config.logger.error(f"Gemini API error: {str(e)}")
      raise Exception(f"Gemini API error: {str(e)}")


  def _generate_openai_response(conversation_text, model, temperature, max_tokens, top_p):
    """Generate response using OpenAI API"""
    try:
      from openai import OpenAI
      import httpx

      # Check if OpenAI is configured
      api_key = enhanced_config.get_api_key('chatgpt')
      api_url = enhanced_config.config_manager.get('chatgpt_api', 'https://api.openai.com/v1')

      if not api_key:
        raise Exception("OpenAI API key not configured. Please set chatgpt_key in configuration.")

      # Create OpenAI client
      client = OpenAI(
        api_key=api_key,
        base_url=api_url,
        http_client=httpx.Client(timeout=7200)
      )

      # Convert conversation text back to messages format
      messages = []
      lines = conversation_text.strip().split('\n')
      for line in lines:
        if ':' in line:
          role, content = line.split(':', 1)
          role = role.strip().lower()
          content = content.strip()
          if role in ['user', 'assistant', 'system']:
            messages.append({"role": role, "content": content})

      # Generate response
      response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p
      )

      if response.choices and response.choices[0].message:
        return response.choices[0].message.content.strip()
      else:
        raise Exception("Empty response from OpenAI")

    except Exception as e:
      config.logger.error(f"OpenAI API error: {str(e)}")
      raise Exception(f"OpenAI API error: {str(e)}")


  def _generate_claude_response(conversation_text, model, temperature, max_tokens):
    """Generate response using Claude API"""
    try:
      # Check if Claude is configured
      api_key = enhanced_config.get_api_key('claude')
      if not api_key:
        raise Exception("Claude API key not configured. Please set claude_key in configuration.")

      # For now, return a placeholder since Claude integration would need more setup
      return "Claude integration not fully implemented yet. Please use Gemini or OpenAI models."

    except Exception as e:
      config.logger.error(f"Claude API error: {str(e)}")
      raise Exception(f"Claude API error: {str(e)}")


  def _generate_default_response(conversation_text, model, temperature, max_tokens, top_p):
    """Generate response using local LLM or fallback"""
    try:
      # Try local LLM first
      api_url = enhanced_config.config_manager.get('localllm_api')
      api_key = enhanced_config.get_api_key('localllm')

      if api_url:
        from openai import OpenAI
        import httpx

        client = OpenAI(
          api_key=api_key,
          base_url=api_url,
          http_client=httpx.Client(timeout=7200)
        )

        # Convert conversation text back to messages format
        messages = []
        lines = conversation_text.strip().split('\n')
        for line in lines:
          if ':' in line:
            role, content = line.split(':', 1)
            role = role.strip().lower()
            content = content.strip()
            if role in ['user', 'assistant', 'system']:
              messages.append({"role": role, "content": content})

        response = client.chat.completions.create(
          model=enhanced_config.config_manager.get('localllm_model', model),
          messages=messages,
          temperature=temperature,
          max_tokens=max_tokens,
          top_p=top_p
        )

        if response.choices and response.choices[0].message:
          return response.choices[0].message.content.strip()

      # Fallback response
      return f"I'm a chat assistant. You said: {conversation_text.split('user:')[-1].strip() if 'user:' in conversation_text else conversation_text}"

    except Exception as e:
      config.logger.error(f"Default response generation error: {str(e)}")
      # Final fallback
      return "I'm sorry, I'm having trouble generating a response right now. Please check the API configuration."


  def _estimate_tokens(messages):
    """Rough estimation of token count"""
    total_chars = 0
    for msg in messages:
      if isinstance(msg, dict):
        content = msg.get('content', '')
      else:
        content = str(msg)
      total_chars += len(content)
    # Rough approximation: 1 token ≈ 4 characters
    return max(1, total_chars // 4)


  # 第7个接口 /v2/apply_subtitle_style
  """
  应用字幕样式和配音到视频接口

  请求参数:
  类型 Content-Type:application/json

  请求数据:
  srt_url:必须参数，字符串类型，SRT字幕文件的URL地址或本地路径
  video_url:必须参数，字符串类型，视频文件的URL地址或本地路径
  voiceSeparation:可选参数，布尔类型，是否进行人声分离，默认false
  sourceLanguage:可选参数，字符串类型，源语言代码，默认"en"
  targetLanguage:可选参数，字符串类型，目标语言代码，默认"zh-cn"
  subtitleLayout:可选参数，字符串类型，字幕布局，"single"=单字幕，"double"=双字幕，默认"single"
  subtitleStyle:可选参数，对象类型，字幕样式设置
      fontSize:可选参数，整数类型，主字幕字体大小，默认15
      fontFamily:可选参数，字符串类型，主字幕字体名称，默认"NotoSansCJK-Regular"
      primaryColor:可选参数，字符串类型，主字幕颜色，BGR格式如"&H00A6A6E2"
      primaryStrokeWidth:可选参数，整数类型，主字幕描边宽度，默认1
      shadowColor:可选参数，字符串类型，阴影颜色，默认"&H80000000"
      showPrimaryShadow:可选参数，布尔类型，是否显示主字幕阴影，默认false
      showPrimaryStroke:可选参数，布尔类型，是否显示主字幕描边，默认false
      primaryMarginV:可选参数，整数类型，主字幕垂直边距，默认0
      primaryBackgroundColor:可选参数，字符串类型，主字幕背景色，默认"&H33000000"
      showPrimaryBackground:可选参数，布尔类型，是否显示主字幕背景，默认false
      secondaryFontSize:可选参数，整数类型，副字幕字体大小，默认22
      secondaryFontFamily:可选参数，字符串类型，副字幕字体名称
      secondaryColor:可选参数，字符串类型，副字幕颜色，默认"&H0028E2A1"
      secondaryStrokeColor:可选参数，字符串类型，副字幕描边颜色，默认"&H000505E1"
      secondaryStrokeWidth:可选参数，整数类型，副字幕描边宽度，默认1
      secondaryBackgroundColor:可选参数，字符串类型，副字幕背景色，默认"&H000F0F4D"
      showSecondaryShadow:可选参数，布尔类型，是否显示副字幕阴影，默认false
      showSecondaryStroke:可选参数，布尔类型，是否显示副字幕描边，默认false
      showSecondaryBackground:可选参数，布尔类型，是否显示副字幕背景，默认true
      secondaryMarginV:可选参数，整数类型，副字幕垂直边距，默认32
  dubbing_settings:可选参数，对象类型，配音设置
      tts_type:可选参数，整数类型，配音渠道，0="Edge-TTS",1='CosyVoice',2="ChatTTS"等，默认0
      voice_role:可选参数，字符串类型，语音角色名称
      voice_rate:可选参数，字符串类型，语速，格式"+0%"，默认"+0%"
      volume:可选参数，字符串类型，音量，格式"+0%"，默认"+0%"
      pitch:可选参数，字符串类型，音调，格式"+0Hz"，默认"+0Hz"

  返回数据
  返回类型：json格式，
  成功时返回，可根据task_id通过 task_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_apply_subtitle_style():
      res=requests.post("http://127.0.0.1:9011/v2/apply_subtitle_style",json={
          "srt_url":"http://127.0.0.1:9011/file/subtitle.srt",
          "video_url":"http://127.0.0.1:9011/file/video.mp4",
          "voiceSeparation": true,
          "sourceLanguage": "en",
          "targetLanguage": "vi",
          "subtitleLayout": "double",
          "subtitleStyle": {
              "fontSize": 15,
              "fontFamily": "NotoSansCJK-Regular",
              "primaryColor": "&H00A6A6E2",
              "primaryStrokeWidth": 1,
              "shadowColor": "&H80000000",
              "showPrimaryShadow": false,
              "showPrimaryStroke": false,
              "primaryMarginV": 0,
              "primaryBackgroundColor": "&H33000000",
              "showPrimaryBackground": false,
              "secondaryFontSize": 22,
              "secondaryFontFamily": "NotoSansCJK-Regular",
              "secondaryColor": "&H0028E2A1",
              "secondaryStrokeColor": "&H000505E1",
              "secondaryStrokeWidth": 1,
              "secondaryBackgroundColor": "&H000F0F4D",
              "showSecondaryShadow": false,
              "showSecondaryStroke": false,
              "showSecondaryBackground": true,
              "secondaryMarginV": 32
          },
          "dubbing_settings":{
              "tts_type":0,
              "voice_role":"zh-CN-YunjianNeural",
              "voice_rate":"+0%",
              "volume":"+0%",
              "pitch":"+0Hz"
          }
      })
      print(res.json())
  ```
  """


  @app.route('/dubbing', methods=['POST'])
  def apply_subtitle_style():
    try:
      data = request.json
      if not data:
        return jsonify({"code": 1, "msg": "Request body must be JSON"})

      # 验证必需参数
      srt_url = data.get('srt_url', '').strip()
      video_url = data.get('video_url', '').strip()

      if not srt_url:
        return jsonify({"code": 1, "msg": "The parameter srt_url is required"})
      if not video_url:
        return jsonify({"code": 1, "msg": "The parameter video_url is required"})

      # Convert URLs to local file paths
      srt_path = get_local_file_path(request, srt_url)
      video_path = get_local_file_path(request, video_url)

      # 获取前端字幕设置
      subtitle_style = data.get('subtitleStyle', {})
      voice_separation = data.get('voiceSeparation', False)
      source_language = data.get('sourceLanguage', 'en')
      target_language = data.get('targetLanguage', 'zh-cn')
      subtitle_layout = data.get('subtitleLayout', 'single')  # single, double

      # 获取视频尺寸设置
      width = data.get('width', 1920)  # 默认宽度1920
      height = data.get('height', 1080)  # 默认高度1080

      # 验证文件路径
      if not Path(srt_path).exists():
        return jsonify({"code": 1, "msg": f"SRT file not found: {srt_path}"})
      if not Path(video_path).exists():
        return jsonify({"code": 1, "msg": f"Video file not found: {video_path}"})

      # 获取配音设置
      dubbing_settings = data.get('dubbing_settings', {})
      enable_dubbing = bool(dubbing_settings)  # 如果提供了配音设置则启用配音
      tts_type = dubbing_settings.get('tts_type', 0) if enable_dubbing else 0
      voice_role = dubbing_settings.get('voice_role', '') if enable_dubbing else ''
      voice_rate = dubbing_settings.get('voice_rate', '+0%') if enable_dubbing else '+0%'
      volume = dubbing_settings.get('volume', '+0%') if enable_dubbing else '+0%'
      pitch = dubbing_settings.get('pitch', '+0Hz') if enable_dubbing else '+0Hz'

      # 验证配音设置
      if enable_dubbing and voice_role:
        is_allow_lang = tts_model.is_allow_lang(langcode=target_language, tts_type=tts_type)
        if is_allow_lang is not True:
          return jsonify({"code": 4, "msg": is_allow_lang})
        is_input_api = tts_model.is_input_api(tts_type=tts_type, return_str=True)
        if is_input_api is not True:
          return jsonify({"code": 5, "msg": is_input_api})

      # 创建任务配置
      obj = tools.format_video("temp_video", None)
      obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
      obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
      Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
      Path(obj['cache_folder']).mkdir(parents=True, exist_ok=True)

      cfg = {
        'srt_path': srt_path,
        'video_path': video_path,
        'voice_separation': voice_separation,
        'source_language': source_language,
        'target_language': target_language,
        'subtitle_layout': subtitle_layout,
        'subtitle_style': subtitle_style,  # Pass the original subtitle_style directly
        'width': width,  # Video width from request payload
        'height': height,  # Video height from request payload
        'dubbing_settings': {
          'enable_dubbing': enable_dubbing,
          'tts_type': tts_type,
          'voice_role': voice_role,
          'voice_rate': voice_rate,
          'volume': volume,
          'pitch': pitch,
          'target_language': target_language
        } if enable_dubbing else None
      }
      cfg.update(obj)

      # 创建并启动任务
      # Get user context for database integration
      user_id = get_current_user_id()
      trk = ApplySubtitleStyleTask.create_with_user_context(cfg=cfg, user_id=user_id)
      config.prepare_queue.append(trk)
      tools.set_process(text=f"Currently in queue No.{len(config.prepare_queue)}", uuid=obj['uuid'])

      return jsonify({'code': 0, 'task_id': obj['uuid']})

    except Exception as e:
      config.logger.error(f"Apply subtitle style error: {str(e)}")
      return jsonify({"code": 1, "msg": f"Error: {str(e)}"})


  # 第8个接口 /generate_image
  """
  图像生成接口 - 使用BytePlus ModelArk生成图像

  请求参数:
  类型 Content-Type:application/json

  请求数据:
  prompt:必需参数，字符串类型，图像生成的文本描述
  size:可选参数，字符串类型，生成图像的尺寸，默认"1024x1024"，支持"1024x1024", "1024x1792", "1792x1024"等

  返回数据
  返回类型：json格式，
  成功时返回
  {"code":0,"msg":"ok","image_url":"生成的图像URL"}

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_generate_image():
      res=requests.post("http://127.0.0.1:9011/generate_image",json={
          "prompt":"Fisheye lens, the head of a cat, the image shows the effect that the facial features of the cat are distorted due to the shooting method.",
          "size":"1024x1024"
      })
      print(res.json())
  ```
  """


  @app.route('/generate_image', methods=['POST'])
  def generate_image():
    try:
      data = request.json
      if not data:
        return jsonify({"code": 1, "msg": "Request body must be JSON"})

      # 获取参数
      prompt = data.get('prompt', '').strip()
      size = data.get('size', '1024x1024').strip()

      # 验证输入
      if not prompt:
        return jsonify({"code": 1, "msg": "Prompt is required"})

      # 验证尺寸格式
      # valid_sizes = ["1024x1024", "1024x1792", "1792x1024", "512x512", "768x768"]
      # if size not in valid_sizes:
      #     return jsonify({"code": 1, "msg": f"Invalid size. Supported sizes: {', '.join(valid_sizes)}"})

      # 检查API密钥
      api_key = enhanced_config.get_api_key('byteplus_modelark')
      if not api_key:
        return jsonify({"code": 1,
                        "msg": "BytePlus ModelArk API key not configured. Please set byteplus_modelark_key in configuration."})

      # 准备API请求
      api_url = "https://ark.ap-southeast.bytepluses.com/api/v3/images/generations"
      headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}',
        'User-Agent': 'PyVideoTrans/1.0'
      }

      payload = {
        "model": "seedream-3-0-t2i-250415",
        "prompt": prompt,
        "response_format": "url",
        "size": size,
        "guidance_scale": 3,
        "watermark": False
      }

      # 发送请求
      response = requests.post(
        api_url,
        headers=headers,
        json=payload,
        timeout=60
      )

      if response.status_code != 200:
        error_msg = f"API request failed with status {response.status_code}"
        try:
          error_data = response.json()
          if 'error' in error_data:
            error_msg = error_data['error'].get('message', error_msg)
        except:
          error_msg = f"{error_msg}: {response.text}"
        return jsonify({"code": 1, "msg": error_msg})

      result = response.json()

      # 检查响应格式
      if 'data' not in result or not result['data']:
        return jsonify({"code": 1, "msg": "Invalid response format from API"})

      # 获取图像URL
      image_data = result['data'][0] if isinstance(result['data'], list) else result['data']
      image_url = image_data.get('url', '')

      if not image_url:
        return jsonify({"code": 1, "msg": "No image URL in response"})

      return jsonify({
        'code': 0,
        'msg': 'ok',
        'image_url': image_url
      })

    except requests.exceptions.RequestException as e:
      config.logger.error(f"Image generation network error: {str(e)}")
      return jsonify({"code": 1, "msg": f"Network error: {str(e)}"})
    except Exception as e:
      config.logger.error(f"Image generation error: {str(e)}")
      return jsonify({"code": 1, "msg": f"Error: {str(e)}"})


  # 第9个接口 /generate_video
  """
  视频生成接口 - 支持多种视频生成服务

  请求参数:
  类型 Content-Type:application/json

  请求数据:
  prompt:可选参数，字符串类型，视频生成的文本描述
  image_path:可选参数，字符串类型，用于图像到视频生成的图片路径
  videogen_type:可选参数，整数类型，视频生成服务类型（可通过model自动检测），0=BytePlus，1=Minimax，默认0
  async_mode:可选参数，布尔类型，是否异步模式，默认True
  model:可选参数，字符串类型，统一模型命名格式：
    - BytePlus模型: seedance-lite, seedance-pro
    - Minimax模型: minimax-hailuo-02, minimax-t2v-director, minimax-i2v-director, minimax-s2v, minimax-i2v, minimax-t2v
    - 系统会自动检测提供商并映射到内部模型名称

  Minimax专用参数 (当使用minimax-*模型时):
  duration:可选参数，整数类型，视频时长(秒)，6或10，默认6
  resolution:可选参数，字符串类型，视频分辨率，768P或1080P，默认768P
  prompt_optimizer:可选参数，布尔类型，是否启用提示词优化，默认True

  返回数据
  返回类型：json格式，
  异步模式时返回，可根据task_id通过 video_status 获取任务进度
  {"code":0,"msg":"ok","task_id":任务id}

  同步模式时返回生成的视频路径
  {"code":0,"msg":"ok","video_path":"生成的视频文件路径"}

  失败时返回
  {"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_generate_video():
      # BytePlus 轻量版模型 (自动检测提供商)
      res=requests.post("http://127.0.0.1:9011/generate_video",json={
          "prompt":"A little dog is running in the sunshine",
          "model":"seedance-lite",
          "async_mode":True
      })
      print(res.json())

      # BytePlus 专业版模型
      res=requests.post("http://127.0.0.1:9011/generate_video",json={
          "prompt":"A cinematic shot of mountains",
          "model":"seedance-pro",
          "async_mode":True
      })
      print(res.json())

      # Minimax 文本到视频 (自动检测提供商)
      res=requests.post("http://127.0.0.1:9011/generate_video",json={
          "prompt":"A woman is drinking coffee",
          "model":"minimax-hailuo-02",
          "duration":6,
          "resolution":"1080P",
          "async_mode":True
      })
      print(res.json())

      # Minimax 图像到视频
      res=requests.post("http://127.0.0.1:9011/generate_video",json={
          "prompt":"Make this image move with wind effects",
          "image_path":"/path/to/image.jpg",
          "model":"minimax-i2v-director",
          "duration":6,
          "resolution":"768P",
          "async_mode":True
      })
      print(res.json())
  ```
  """


  @app.route('/generate_video', methods=['POST'])
  def generate_video():
    try:
      data = request.json
      if not data:
        return jsonify({"code": 1, "msg": "Request body must be JSON"})

      # 获取参数
      prompt = data.get('prompt', '').strip()
      style = data.get('style', '').strip()
      image_path = data.get('image_path', '').strip()
      videogen_type = int(data.get('videogen_type', 0))
      async_mode = bool(data.get('async_mode', True))

      # Model mapping and provider detection
      def parse_model_and_provider(model_name, default_videogen_type):
        """Parse model name and determine provider and internal model name"""
        if not model_name:
          return default_videogen_type, None

        model_lower = model_name.lower()

        # BytePlus models (seedance-*)
        if model_lower.startswith('seedance-'):
          provider_type = 0  # BytePlus
          if model_lower == 'seedance-lite':
            internal_model = enhanced_config.get('byteplus_model_t2v', 'seedance-1-0-lite-t2v-250428')
          elif model_lower == 'seedance-pro':
            internal_model = enhanced_config.get('byteplus_model_t2v_pro', 'seedance-1-0-lite-t2v-250428')
          else:
            # Custom seedance model, use as-is
            internal_model = model_name
          return provider_type, internal_model

        # Minimax models (minimax-*)
        elif model_lower.startswith('minimax-'):
          provider_type = 1  # Minimax
          model_suffix = model_name[8:]  # Remove 'minimax-' prefix

          # Map common patterns
          minimax_model_map = {
            'hailuo-02': 'MiniMax-Hailuo-02',
            'hailuo': 'MiniMax-Hailuo-02',
            't2v-director': 'T2V-01-Director',
            'i2v-director': 'I2V-01-Director',
            's2v': 'S2V-01',
            'i2v': 'I2V-01',
            'i2v-live': 'I2V-01-live',
            't2v': 'T2V-01'
          }

          internal_model = minimax_model_map.get(model_suffix.lower(), f'MiniMax-{model_suffix}')
          return provider_type, internal_model

        # Direct model names (backward compatibility)
        else:
          return default_videogen_type, model_name

      # Get model from client and parse it
      client_model = data.get('model', '')
      detected_videogen_type, parsed_model = parse_model_and_provider(client_model, videogen_type)

      # Update videogen_type if detected from model name
      if client_model and detected_videogen_type != videogen_type:
        videogen_type = detected_videogen_type

      # Set final model
      if parsed_model:
        model = parsed_model
      else:
        # Use defaults when no model specified
        if videogen_type == 0:  # BytePlus
          model = enhanced_config.get('byteplus_model_t2v', 'seedance-1-0-lite-t2v-250428')
          if style in ['pro', 'ultra']:
            model = enhanced_config.get('byteplus_model_t2v_pro', 'seedance-1-0-lite-t2v-250428')
        elif videogen_type == 1:  # Minimax
          model = enhanced_config.get('minimax_model', 'MiniMax-Hailuo-02')

      # 验证输入
      if not prompt and not image_path:
        return jsonify({"code": 1, "msg": "Either prompt or image_path must be provided"})

      config.logger.info(f"style={style}, model={model} promt={prompt[:100]}...")
      # 检查视频生成服务是否可用
      is_allow = videogen.is_allow_videogen(videogen_type=videogen_type, return_str=True)
      if is_allow is not True:
        return jsonify({"code": 5, "msg": is_allow})

      # 处理图片路径
      # if image_path:
      #     image_path = get_local_file_path(request, image_path)
      #     if not Path(image_path).exists():
      #         return jsonify({"code": 1, "msg": f"Image file not found: {image_path}"})

      # 创建任务对象
      obj = tools.format_video("video_generation", None)
      obj['target_dir'] = TARGET_DIR + f'/{obj["uuid"]}'
      obj['cache_folder'] = config.TEMP_DIR + f'/{obj["uuid"]}'
      Path(obj['target_dir']).mkdir(parents=True, exist_ok=True)
      Path(obj['cache_folder']).mkdir(parents=True, exist_ok=True)

      # 创建任务配置
      cfg = {
        'prompt': prompt,
        'image_path': image_path,
        'videogen_type': videogen_type,
        'model': model,
        'uuid': obj['uuid'],
        'cache_folder': obj['cache_folder'],
        'target_dir': obj['target_dir'],
        'async_mode': async_mode
      }

      # Add provider-specific parameters
      if videogen_type == 1:  # Minimax
        cfg['duration'] = int(data.get('duration', 6))  # 6 or 10 seconds
        cfg['resolution'] = data.get('resolution', '768P')  # 768P or 1080P
        cfg['prompt_optimizer'] = bool(data.get('prompt_optimizer', True))
      cfg.update(obj)

      # Create initial status file before returning response
      task_id = obj['uuid']
      status_file = PROCESS_INFO + f'/{task_id}.json'
      initial_status = {
        "type": "logs",
        "text": "Video generation task initialized and queued"
      }
      with open(status_file, 'w', encoding='utf-8') as f:
        json.dump(initial_status, f)

      if async_mode:
        # 异步模式，添加到任务队列
        # Get user context for database integration
        user_id = get_current_user_id()
        trk = VideoGenerationTask.create_with_user_context(cfg=cfg, user_id=user_id)
        config.videogen_queue.append(trk)
        tools.set_process(text=f"Video generation task queued. Position: {len(config.videogen_queue)}",
                          uuid=obj['uuid'])

        return jsonify({
          'code': 0,
          'msg': 'ok',
          'task_id': obj['uuid']
        })
      else:
        # 同步模式，直接执行
        # Get user context for database integration
        user_id = get_current_user_id()
        trk = VideoGenerationTask.create_with_user_context(cfg=cfg, user_id=user_id)
        success = trk.run()

        if success:
          # 查找生成的视频文件
          video_files = []
          if Path(obj['target_dir']).exists():
            for ext in ['.mp4', '.avi', '.mov', '.mkv']:
              video_files.extend(Path(obj['target_dir']).glob(f'*{ext}'))

          if video_files:
            video_path = str(video_files[0])
            return jsonify({
              'code': 0,
              'msg': 'ok',
              'video_path': video_path,
              'task_id': obj['uuid']
            })
          else:
            return jsonify({"code": 1, "msg": "Video generation completed but no video file found"})
        else:
          return jsonify({"code": 1, "msg": f"Video generation failed: {trk.error}"})

    except Exception as e:
      config.logger.error(f"Video generation error: {str(e)}")
      return jsonify({"code": 1, "msg": f"Error: {str(e)}"})


  # 第9个接口 /video_status
  """
  视频生成任务状态查询接口

  请求参数:
  类型 GET或POST

  请求数据:
  task_id:必须参数，字符串类型，视频生成任务ID

  返回数据
  返回类型：json格式

  进行中：{"code":-1,"msg":"正在生成视频","progress":50,"status":"running"}
  成功：{"code":0,"msg":"ok","video_path":"生成的视频文件路径","video_url":"可访问的视频URL"}
  失败：{"code":1,"msg":"错误信息"}

  请求示例
  ```
  def test_video_status():
      res=requests.get("http://127.0.0.1:9011/video_status?task_id=your_task_id")
      print(res.json())
  ```
  """


  @app.route('/video_status', methods=['GET', 'POST'])
  def video_status():
    try:
      # Get task_id (unchanged code)
      task_id = request.args.get('task_id')
      if task_id is None:
        task_id = request.form.get('task_id')
      if task_id is None and request.is_json:
        task_id = request.json.get('task_id')
      if not task_id:
        return jsonify({"code": 1, "msg": "task_id parameter is required"})

      # Check status file (unchanged code)
      status_file = PROCESS_INFO + f'/{task_id}.json'
      if not Path(status_file).exists():
        return jsonify({"code": 1, "msg": "Task not found"})

      # Read status info (unchanged code)
      with open(status_file, 'r', encoding='utf-8') as f:
        status_data = json.load(f)

      # Check if it's a video generation task (unchanged code)
      if status_data.get('type') not in ['logs', 'video_generation']:
        return jsonify({"code": 1, "msg": "Not a video generation task"})

      # FIX: Check if video file exists in target directory
      target_dir = TARGET_DIR + f'/{task_id}'
      video_files = []
      if Path(target_dir).exists():
        for ext in ['.mp4', '.avi', '.mov', '.mkv']:
          video_files.extend(Path(target_dir).glob(f'*{ext}'))

      # FIX: If video file exists, consider it completed regardless of status
      if video_files and "Video generation completed:" in status_data.get('text', ''):
        video_path = str(video_files[0])
        video_url = f"{get_base_url()}/apidata/{task_id}/{Path(video_path).name}"
        return jsonify({
          "code": 0,
          "msg": "ok",
          "video_path": video_path,
          "video_url": video_url,
          "status": "succeeded"
        })

      # Rest of the function remains unchanged
      if status_data.get('type') in end_status_list:
        if status_data.get('type') == 'succeed':
          # Find generated video files
          if video_files:
            video_path = str(video_files[0])
            video_url = f"http://{HOST}:{PORT}/apidata/{task_id}/{Path(video_path).name}"
            return jsonify({
              "code": 0,
              "msg": "ok",
              "video_path": video_path,
              "video_url": video_url,
              "status": "succeeded"
            })
          else:
            return jsonify({"code": 1, "msg": "Video file not found"})
        else:
          return jsonify({
            "code": 1,
            "msg": status_data.get('text', 'Video generation failed'),
            "status": "failed"
          })
      else:
        # Task in progress
        progress = 0
        status = "queued"
        if "running" in status_data.get('text', '').lower():
          status = "running"
          progress = 50
        elif "queue" in status_data.get('text', '').lower():
          status = "queued"
          progress = 10

        return jsonify({
          "code": -1,
          "msg": status_data.get('text', 'Processing'),
          "progress": progress,
          "status": status
        })
    except Exception as e:
      config.logger.error(f"Video status check error: {str(e)}")
      return jsonify({"code": 1, "msg": f"Error: {str(e)}"})


  # 获取任务进度
  """
  根据任务id，获取当前任务的状态

  请求数据类型：优先GET中获取，不存在则从POST中获取，都不存在则从 json数据中获取

  请求参数:
  task_id:必须，字符串类型

  返回:json格式数据
  code:-1=进行中，0=成功结束，>0=出错了
  msg:code为-1时为进度信息，code>0时为出错信息，成功时为ok
  data:仅当code==0成功时存在，是一个dict对象
      absolute_path是生成的文件列表list，每项均是一个文件的绝对路径
      url 是生成的文件列表list，每项均是一个可访问的url


  失败：{"code":1,"msg":"不存在该任务"}
  进行中：{"code":-1,"msg":"正在合成声音"}
  成功: {"code":0,"msg":"ok","data":{"absolute_path":["/data/1.srt","/data/1.mp4"],"url":["http://127.0.0.1:9011/task_id/1.srt"]}}


  示例
  def test_task_status():
      res=requests.post("http://127.0.0.1:9011/task_status",json={
          "task_id":"06c238d250f0b51248563c405f1d7294"
      })
      print(res.json())

  {
    "code": 0,
    "data": {
      "absolute_path": [
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/10ass.mp4",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/en.m4a",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/en.srt",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/end.srt.ass",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.m4a",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.srt",
        "F:/python/pyvideo/apidata/daa33fee2537b47a0b12e12b926a4b01/文件说明.txt"
      ],
      "url": [
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/10ass.mp4",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/en.m4a",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/en.srt",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/end.srt.ass",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.m4a",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/zh-cn.srt",
        "http://127.0.0.1:9011/apidata/daa33fee2537b47a0b12e12b926a4b01/文件说明.txt"
      ]
    },
    "msg": "ok"
  }

  """


  @app.route('/task_status', methods=['POST', 'GET'])
  def task_status():
    # 1. 优先从 GET 请求参数中获取 task_id
    task_id = request.args.get('task_id')

    # 2. 如果 GET 参数中没有 task_id，再从 POST 表单中获取
    if task_id is None:
      task_id = request.form.get('task_id')

    # 3. 如果 POST 表单中也没有 task_id，再从 JSON 请求体中获取
    if task_id is None and request.is_json:
      task_id = request.json.get('task_id')
    if not task_id:
      return jsonify({"code": 1, "msg": "The parem  task_id is not set"})
    return _get_task_data(task_id)


  # 获取多个任务 前台 content-type:application/json, 数据 {task_id_list:[id1,id2,....]}
  @app.route('/task_status_list', methods=['POST', 'GET'])
  def task_status_list():
    # 1. 优先从 GET 请求参数中获取 task_id
    task_ids = request.json.get('task_id_list', [])
    if not task_ids or len(task_ids) < 1:
      return jsonify({"code": 1, "msg": "Missing task_id"})

    return_data = {}
    for task_id in task_ids:
      return_data[task_id] = _get_task_data(task_id)
    return jsonify({"code": 0, "msg": "ok", "data": return_data})


  def _get_task_data(task_id):
    file = PROCESS_INFO + f'/{task_id}.json'
    if not Path(file).is_file():
      if task_id in config.uuid_logs_queue:
        return {"code": -1, "msg": _get_order(task_id)}

      return {"code": 1, "msg": f"The task {task_id} does not exist"}

    try:
      data = json.loads(Path(file).read_text(encoding='utf-8'))
    except Exception as e:
      return {"code": -1, "msg": Path(file).read_text(encoding='utf-8')}

    if data['type'] == 'error':
      return {"code": 3, "msg": data["text"]}
    if data['type'] in logs_status_list:
      text = data.get('text', '').strip()
      return {"code": -1, "msg": text if text else 'Waiting for processing'}
    # 完成，输出所有文件
    file_list = _get_files_in_directory(f'{TARGET_DIR}/{task_id}')
    if len(file_list) < 1:
      return {"code": 4, "msg": 'No result files were generated, something may have gone wrong'}

    return {
      "code": 0,
      "msg": "ok",
      "data": {
        # "absolute_path": [f'{TARGET_DIR}/{task_id}/{name}' for name in file_list],
        "url": [f'{get_base_url()}/{API_RESOURCE}/{task_id}/{name}' for name in file_list],
      }
    }


  # 排队
  def _get_order(task_id):
    order_num = 0
    for it in config.prepare_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"

    order_num = 0
    for it in config.regcon_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"
    order_num = 0
    for it in config.trans_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"
    order_num = 0
    for it in config.dubb_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"
    order_num = 0
    for it in config.align_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"
    order_num = 0
    for it in config.assemb_queue:
      order_num += 1
      if it.uuid == task_id:
        return f"No.{order_num} on perpare queue"
    return f"Waiting in queue"


  def _get_files_in_directory(dirname):
    """
    使用 pathlib 库获取指定目录下的所有文件名，并返回一个文件名列表。

    参数:
    dirname (str): 要获取文件的目录路径

    返回:
    list: 包含目录中所有文件名的列表
    """
    try:
      # 使用 Path 对象获取目录中的所有文件
      path = Path(dirname)
      files = [f.name for f in path.iterdir() if f.is_file()]
      return files
    except Exception as e:
      print(f"Error while accessing directory {dirname}: {e}")
      return []


  def get_local_file_path(request, name):
    # convert http://127.0.0.1:9011/file/250601/2155d0fba834bb41efe7ed00fc6fe309.mp4 to /250601/2155d0fba834bb41efe7ed00fc6fe309.mp4
    if name.startswith(get_base_url()):
      name = name.replace(f"{get_base_url()}/file/", '')
      name = '/usr/lib/media/' + name
    return name


  def _listen_queue():
    # 监听队列日志 uuid_logs_queue 不在停止中的 stoped_uuid_set
    Path(TARGET_DIR + f'/processinfo').mkdir(parents=True, exist_ok=True)
    while 1:
      # 找出未停止的
      uuid_list = list(config.uuid_logs_queue.keys())
      uuid_list = [uuid for uuid in uuid_list if uuid not in config.stoped_uuid_set]
      # 全部结束
      if len(uuid_list) < 1:
        time.sleep(1)
        continue
      while len(uuid_list) > 0:
        uuid = uuid_list.pop(0)
        if uuid in config.stoped_uuid_set:
          continue
        try:
          q = config.uuid_logs_queue.get(uuid)
          if not q:
            continue
          data = q.get(block=False)
          if not data:
            continue

          if data['type'] not in end_status_list + logs_status_list:
            continue
          with open(PROCESS_INFO + f'/{uuid}.json', 'w', encoding='utf-8') as f:
            f.write(json.dumps(data))
          if data['type'] in end_status_list:
            config.stoped_uuid_set.add(uuid)
            del config.uuid_logs_queue[uuid]
        except Exception:
          pass
      time.sleep(0.1)


  # Real-time configuration status endpoint
  # @app.route('/api/realtime/status', methods=['GET'])
  def realtime_status():
    """Get real-time configuration system status"""
    try:
      realtime_manager = get_realtime_config_manager()
      return jsonify({
        'success': True,
        'data': {
          'watcher_running': realtime_manager.watcher._running,
          'registered_services': len(realtime_manager._service_instances),
          'check_interval': realtime_manager.watcher.check_interval,
          'database_available': enhanced_config.config_manager._is_database_available(),
          'total_settings': len(enhanced_config.config_manager.get_all())
        }
      })
    except Exception as e:
      return jsonify({
        'success': False,
        'error': str(e)
      }), 500


  # Real-time configuration reload endpoint
  # @app.route('/api/realtime/reload', methods=['POST'])
  def realtime_reload():
    """Force reload all services with current configuration"""
    try:
      realtime_manager = get_realtime_config_manager()

      # Trigger reload for all registered services
      service_count = len(realtime_manager._service_instances)

      # Force notifications for key configuration changes
      key_settings = ['chatgpt_key', 'chatgpt_model', 'deepl_authkey', 'azure_speech_key']
      notification_count = 0

      for key in key_settings:
        current_value = enhanced_config.config_manager.get(key)
        if current_value:
          realtime_manager.watcher.notifier.notify_change(key, None, current_value, 'API Keys')
          notification_count += 1

      return jsonify({
        'success': True,
        'message': f'Triggered reload for {service_count} services with {notification_count} notifications'
      })
    except Exception as e:
      return jsonify({
        'success': False,
        'error': str(e)
      }), 500


  # Configuration update endpoint with real-time application
  # @app.route('/api/realtime/config/<key>', methods=['PUT'])
  def update_config_realtime(key):
    """Update configuration with immediate real-time application"""
    try:
      data = request.json
      value = data.get('value')
      category = data.get('category', 'Other')
      description = data.get('description', f'Real-time update for {key}')
      data_type = data.get('data_type', 'string')
      is_sensitive = data.get('is_sensitive', False)

      # Use realtime manager for immediate updates
      realtime_manager = get_realtime_config_manager()
      realtime_manager.set(
        key=key,
        value=value,
        category=category,
        description=description,
        data_type=data_type,
        is_sensitive=is_sensitive
      )

      return jsonify({
        'success': True,
        'message': f'Configuration {key} updated and applied immediately',
        'data': {
          'key': key,
          'value': '***' if is_sensitive else value,
          'category': category,
          'applied_realtime': True
        }
      })
    except Exception as e:
      return jsonify({
        'success': False,
        'error': str(e)
      }), 500


  # User-Scoped Task Management API Endpoints

  @app.route('/api/tasks', methods=['GET'])
  @require_user_context
  def get_user_tasks():
    """Get all tasks for the authenticated user with filtering options"""
    try:
      user_id = get_current_user_id()

      # Get query parameters
      status = request.args.get('status')
      task_type = request.args.get('type')
      limit = int(request.args.get('limit', 100))
      offset = int(request.args.get('offset', 0))
      order_by = request.args.get('order_by', 'created_at')
      order_desc = request.args.get('order_desc', 'true').lower() == 'true'

      # Get tasks from database
      tasks = task_manager.get_user_tasks(
        user_id=user_id,
        status=status,
        task_type=task_type,
        limit=limit,
        offset=offset,
        order_by=order_by,
        order_desc=order_desc
      )

      # Get user statistics
      stats = task_manager.get_user_task_stats(user_id)

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {
          "tasks": tasks,
          "stats": stats,
          "pagination": {
            "limit": limit,
            "offset": offset,
            "total": len(tasks)
          }
        }
      })

    except Exception as e:
      config.logger.error(f"Get user tasks error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get tasks: {str(e)}"
      }), 500


  @app.route('/api/tasks/<task_uuid>', methods=['GET'])
  @require_user_context
  def get_user_task(task_uuid):
    """Get specific task details for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Get task from database
      task = task_manager.get_task(user_id, task_uuid)

      if not task:
        return jsonify({
          "code": 1,
          "msg": "Task not found or access denied"
        }), 404

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {"task": task}
      })

    except Exception as e:
      config.logger.error(f"Get user task error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get task: {str(e)}"
      }), 500


  @app.route('/api/tasks/<task_uuid>/logs', methods=['GET'])
  @require_user_context
  def get_user_task_logs(task_uuid):
    """Get task execution logs for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Get query parameters
      level = request.args.get('level')
      limit = int(request.args.get('limit', 100))

      # Get logs from database
      logs = task_manager.get_task_logs(user_id, task_uuid, level, limit)

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {"logs": logs}
      })

    except Exception as e:
      config.logger.error(f"Get user task logs error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get task logs: {str(e)}"
      }), 500


  @app.route('/api/tasks/<task_uuid>/stop', methods=['POST'])
  @require_user_context
  def stop_user_task(task_uuid):
    """Stop a running task for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Verify task belongs to user
      task = task_manager.get_task(user_id, task_uuid)
      if not task:
        return jsonify({
          "code": 1,
          "msg": "Task not found or access denied"
        }), 404

      # Add to stopped tasks set (existing mechanism)
      config.stoped_uuid_set.add(task_uuid)

      # Update task status in database
      success = task_manager.update_task_status(user_id, task_uuid, 'stopped')

      if success:
        # Log the stop event
        task_manager.log_task_event(user_id, task_uuid, "Task stopped by user", "info")

        return jsonify({
          "code": 0,
          "msg": "Task stopped successfully"
        })
      else:
        return jsonify({
          "code": 1,
          "msg": "Failed to stop task"
        }), 500

    except Exception as e:
      config.logger.error(f"Stop user task error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to stop task: {str(e)}"
      }), 500


  @app.route('/api/tasks/<task_uuid>/cancel', methods=['POST'])
  @require_user_context
  def cancel_user_task(task_uuid):
    """Cancel a task for the authenticated user by setting status to cancelled"""
    try:
      user_id = get_current_user_id()

      # Verify task belongs to user
      task = task_manager.get_task(user_id, task_uuid)
      if not task:
        return jsonify({
          "code": 1,
          "msg": "Task not found or access denied"
        }), 404

      # Update task status to cancelled in database
      success = task_manager.update_task_status(
        user_id=user_id,
        task_uuid=task_uuid,
        status='cancelled'
      )

      if success:
        # Log the cancellation event
        task_manager.log_task_event(
          user_id=user_id,
          task_uuid=task_uuid,
          message="Task cancelled by user request",
          level="info"
        )

        return jsonify({
          "code": 0,
          "msg": "Task cancelled successfully",
          "data": {"task_uuid": task_uuid, "status": "cancelled"}
        })
      else:
        return jsonify({
          "code": 1,
          "msg": "Failed to cancel task"
        }), 500

    except Exception as e:
      config.logger.error(f"Cancel user task error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to cancel task: {str(e)}"
      }), 500


  @app.route('/api/tasks/<task_uuid>', methods=['DELETE'])
  @require_user_context
  def force_delete_user_task(task_uuid):
    """Force delete a task for the authenticated user (removes all related data)"""
    try:
      user_id = get_current_user_id()

      # Verify task belongs to user before deletion
      task = task_manager.get_task(user_id, task_uuid)
      if not task:
        return jsonify({
          "code": 1,
          "msg": "Task not found or access denied"
        }), 404

      # Force delete the task and all related data
      success = task_manager.delete_task(user_id, task_uuid)

      if success:
        # Log the deletion event
        config.logger.info(f"Task {task_uuid} force deleted by user {user_id}")

        return jsonify({
          "code": 0,
          "msg": "Task deleted successfully",
          "data": {"task_uuid": task_uuid, "status": "deleted"}
        })
      else:
        return jsonify({
          "code": 1,
          "msg": "Failed to delete task"
        }), 500

    except Exception as e:
      config.logger.error(f"Force delete user task error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to delete task: {str(e)}"
      }), 500


  # User-Scoped Media Library API Endpoints

  # Explicit OPTIONS handler for CORS preflight - must come before other routes
  @app.route('/api/media', methods=['OPTIONS'])
  def handle_media_options():
    """Handle CORS preflight for /api/media endpoint"""
    return '', 200

  @app.route('/api/media', methods=['GET'])
  @require_user_context
  def get_user_media():
    """Get all media files for the authenticated user with filtering options"""
    try:
      user_id = get_current_user_id()

      # Get query parameters with validation
      mime_type = request.args.get('type')  # e.g., 'video', 'audio', 'image' (filters by MIME type prefix)
      media_type = request.args.get('media_type')  # e.g., 'video', 'audio', 'image' (exact match)
      search = request.args.get('search')

      # Validate and parse numeric parameters
      try:
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))
      except (ValueError, TypeError):
        return jsonify({
          "code": 1,
          "msg": "Invalid limit or offset parameter"
        }), 400

      order_by = request.args.get('order_by', 'uploaded_at')
      order_desc = request.args.get('order_desc', 'true').lower() == 'true'

      # Get media from database
      base_url = get_base_url()
      media_list = media_manager.get_user_media(
        user_id=user_id,
        mime_type=mime_type,
        media_type=media_type,
        limit=limit,
        offset=offset,
        order_by=order_by,
        order_desc=order_desc,
        search=search,
        base_url=base_url
      )

      # Get user media statistics
      stats = media_manager.get_user_media_stats(user_id)

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {
          "media": media_list,
          "stats": stats,
          "pagination": {
            "limit": limit,
            "offset": offset,
            "total": len(media_list)
          }
        }
      })

    except Exception as e:
      import traceback
      error_details = traceback.format_exc()
      config.logger.error(f"Get user media error: {str(e)}")
      config.logger.error(f"Full traceback: {error_details}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get media: {str(e)}"
      }), 500


  @app.route('/api/media/<int:media_id>', methods=['OPTIONS'])
  def handle_media_item_options(media_id):
    """Handle CORS preflight for /api/media/<id> endpoint"""
    return '', 200

  @app.route('/api/media/<int:media_id>', methods=['GET'])
  @require_user_context
  def get_user_media_item(media_id):
    """Get specific media file details for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Get media from database
      base_url = get_base_url()
      media = media_manager.get_media(user_id, media_id, base_url)

      if not media:
        return jsonify({
          "code": 1,
          "msg": "Media not found or access denied"
        }), 404

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {"media": media}
      })

    except Exception as e:
      config.logger.error(f"Get user media item error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get media: {str(e)}"
      }), 500


  @app.route('/api/media/<int:media_id>', methods=['OPTIONS', 'DELETE'])
  def handle_media_delete_options_or_delete(media_id):
    """Handle CORS preflight and DELETE for /api/media/<id> endpoint"""
    if request.method == 'OPTIONS':
      return '', 200
    # For DELETE requests, apply user context requirement
    return delete_user_media_impl(media_id)

  @require_user_context
  def delete_user_media_impl(media_id):
    """Delete (soft delete) media file for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Soft delete media
      success = media_manager.soft_delete_media(user_id, media_id)

      if success:
        return jsonify({
          "code": 0,
          "msg": "Media deleted successfully"
        })
      else:
        return jsonify({
          "code": 1,
          "msg": "Media not found or access denied"
        }), 404

    except Exception as e:
      config.logger.error(f"Delete user media error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to delete media: {str(e)}"
      }), 500


  @app.route('/api/media/<int:media_id>/tasks', methods=['GET'])
  @require_user_context
  def get_media_tasks(media_id):
    """Get tasks that used this media file for the authenticated user"""
    try:
      user_id = get_current_user_id()

      # Get query parameters
      relationship_type = request.args.get('type')  # input, output, thumbnail

      # Get media associated with tasks
      media_list = media_manager.get_media_by_task(user_id, str(media_id), relationship_type)

      return jsonify({
        "code": 0,
        "msg": "ok",
        "data": {"media": media_list}
      })

    except Exception as e:
      config.logger.error(f"Get media tasks error: {str(e)}")
      return jsonify({
        "code": 1,
        "msg": f"Failed to get media tasks: {str(e)}"
      }), 500


  multiprocessing.freeze_support()  # Windows 上需要这个来避免子进程的递归执行问题
  print(f'Starting... API URL is   http://{HOST}:{PORT}')
  print(f'Document at https://pyvideotrans.com/api-cn')
  print(f'🔄 Real-time Configuration: ENABLED')
  print(f'   - Configuration changes apply immediately without restart')
  print(f'   - Real-time status: http://{HOST}:{PORT}/api/realtime/status')
  print(f'   - Configuration API: http://{HOST}:{PORT}/api/config/settings')
  start_thread()
  threading.Thread(target=_listen_queue).start()
  try:
    print(f'\n🚀 API Server Running: http://{HOST}:{PORT}')
    print(f'📖 Documentation: https://pyvideotrans.com/api-cn')
    print(f'⚙️  Real-time Config: http://{HOST}:{PORT}/api/realtime/status')
    serve(app, host=HOST, port=int(PORT))
  except Exception as e:
    import traceback

    traceback.print_exc()
