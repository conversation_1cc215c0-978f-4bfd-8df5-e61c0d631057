#!/bin/bash

# PyVideoTrans Quick Start Script
# This script helps you quickly build and push your Docker image

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 PyVideoTrans Docker Quick Start${NC}"
echo "=================================="

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker Desktop.${NC}"
    exit 1
fi

# Check if logged in
if ! docker info | grep -q "Username:"; then
    echo -e "${YELLOW}⚠️  Not logged in to Docker Hub${NC}"
    echo "Please login first:"
    echo "docker login"
    exit 1
fi

# Get Docker username
DOCKER_USERNAME=$(docker info | grep "Username:" | awk '{print $2}')
echo -e "${GREEN}✅ Logged in as: $DOCKER_USERNAME${NC}"

# Ask for version
echo ""
echo "What version would you like to build?"
echo "1) latest (default)"
echo "2) v1.0.0"
echo "3) Custom version"
read -p "Choose option (1-3): " choice

case $choice in
    1|"")
        VERSION="latest"
        ;;
    2)
        VERSION="v1.0.0"
        ;;
    3)
        read -p "Enter custom version: " VERSION
        ;;
    *)
        echo -e "${RED}Invalid choice${NC}"
        exit 1
        ;;
esac

# Ask for build options
echo ""
echo "Build options:"
echo "1) Single platform (faster)"
echo "2) Multi-platform (linux/amd64,linux/arm64)"
read -p "Choose option (1-2): " platform_choice

MULTIPLATFORM=""
if [ "$platform_choice" = "2" ]; then
    MULTIPLATFORM="--multiplatform"
fi

# Confirm build
echo ""
echo -e "${BLUE}Build Summary:${NC}"
echo "Username: $DOCKER_USERNAME"
echo "Repository: pyvideotrans"
echo "Version: $VERSION"
echo "Platform: $([ "$platform_choice" = "2" ] && echo "Multi-platform" || echo "Single platform")"
echo ""
read -p "Proceed with build? (y/N): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Build cancelled."
    exit 0
fi

# Run the build
echo ""
echo -e "${BLUE}🔨 Starting build...${NC}"
export DOCKER_USERNAME
./build-and-push.sh $VERSION $MULTIPLATFORM

echo ""
echo -e "${GREEN}🎉 Build completed successfully!${NC}"
echo ""
echo "Your image is now available at:"
echo -e "${BLUE}docker pull $DOCKER_USERNAME/pyvideotrans:$VERSION${NC}"
echo ""
echo "To use it:"
echo "docker run -d -p 9011:9011 $DOCKER_USERNAME/pyvideotrans:$VERSION"
echo ""
echo "Or update your docker-compose.yml:"
echo "services:"
echo "  pyvideotrans:"
echo "    image: $DOCKER_USERNAME/pyvideotrans:$VERSION"
