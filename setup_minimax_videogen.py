#!/usr/bin/env python3
"""
Setup script for Minimax video generation service
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from videotrans.configure.enhanced_config import enhanced_config
    from videotrans import videogen
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Please make sure you're running this script from the project root directory.")
    sys.exit(1)


def setup_minimax_videogen():
    """Setup Minimax video generation service"""
    print("=" * 60)
    print("Minimax Video Generation Setup")
    print("=" * 60)
    print()
    print("This script will help you configure Minimax video generation service.")
    print("You'll need a Minimax API key from: https://www.minimax.io/")
    print()

    # Check if already configured
    try:
        is_configured = videogen.is_allow_videogen(videogen_type=1, return_str=True)
        if is_configured is True:
            print("✅ Minimax video generation is already configured!")
            choice = input("Do you want to reconfigure? (y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("Setup cancelled.")
                return True
    except Exception:
        pass  # Not configured yet

    # Get API key
    while True:
        api_key = input("Enter your Minimax API key: ").strip()
        if not api_key:
            print("API key cannot be empty. Please try again.")
            continue

        if len(api_key) < 20:
            print("API key seems too short. Please check and try again.")
            continue

        break

    # Set API key
    try:
        success = enhanced_config.set_api_key('minimax', api_key, 'Minimax API key for video generation')
        if success:
            print("✅ API key saved successfully!")
        else:
            print("❌ Failed to save API key. Please check the key format.")
            return False
    except Exception as e:
        print(f"❌ Error saving API key: {e}")
        return False

    # Configure video generation settings
    print("\nConfiguring video generation settings...")

    # Default model
    print("\nAvailable Minimax models:")
    print("1. MiniMax-Hailuo-02 (1080P, max 10s)")
    print("2. T2V-01-Director (720P, text-to-video with camera control)")
    print("3. I2V-01-Director (720P, image-to-video with camera control)")
    print("4. S2V-01 (720P, subject reference)")
    print("5. I2V-01 (720P, image-to-video)")
    print("6. I2V-01-live (720P, image-to-video live)")
    print("7. T2V-01 (720P, text-to-video)")

    model_choice = input("Select default model (1-7, default: 1): ").strip()
    model_map = {
        '1': 'MiniMax-Hailuo-02',
        '2': 'T2V-01-Director',
        '3': 'I2V-01-Director',
        '4': 'S2V-01',
        '5': 'I2V-01',
        '6': 'I2V-01-live',
        '7': 'T2V-01'
    }
    model = model_map.get(model_choice, 'MiniMax-Hailuo-02')

    # Default duration
    duration = input("Default video duration in seconds (6 or 10, default: 6): ").strip()
    if duration not in ['6', '10']:
        duration = '6'

    # Default resolution
    resolution = input("Default resolution (768P or 1080P, default: 768P): ").strip()
    if resolution not in ['768P', '1080P']:
        resolution = '768P'

    # Save video generation configuration
    videogen_config = {
        'model': model,
        'duration': int(duration),
        'resolution': resolution,
        'prompt_optimizer': True,
        'timeout': 300,
        'max_retries': 3
    }

    try:
        enhanced_config.set_videogen_config('minimax', videogen_config)
        print("✅ Video generation configuration saved!")
    except Exception as e:
        print(f"❌ Error saving video generation config: {e}")
        return False

    # Test configuration
    print("\nTesting configuration...")
    try:
        is_configured = videogen.is_allow_videogen(videogen_type=1, return_str=True)
        if is_configured is True:
            print("✅ Minimax video generation is properly configured!")
        else:
            print(f"❌ Configuration test failed: {is_configured}")
            return False
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

    print("\n" + "=" * 60)
    print("Setup completed successfully!")
    print("=" * 60)
    print()
    print("You can now use Minimax video generation in PyVideoTrans:")
    print()
    print("API Endpoint: POST /generate_video")
    print("Example request (unified model naming):")
    print("""
{
    "prompt": "A woman is drinking coffee",
    "model": "minimax-hailuo-02",
    "duration": 6,
    "resolution": "1080P",
    "async_mode": true
}
""")
    print("Note: Provider is auto-detected from model name!")
    print("No need to specify videogen_type when using unified naming.")
    print()
    print("Unified Model Names (auto-detects provider):")
    print("  BytePlus Models:")
    print("    • seedance-lite  -> BytePlus Lite")
    print("    • seedance-pro   -> BytePlus Pro")
    print("  Minimax Models:")
    print("    • minimax-hailuo-02     -> MiniMax-Hailuo-02")
    print("    • minimax-t2v-director  -> T2V-01-Director")
    print("    • minimax-i2v-director  -> I2V-01-Director")
    print("    • minimax-s2v           -> S2V-01")
    print("    • minimax-i2v           -> I2V-01")
    print("    • minimax-i2v-live      -> I2V-01-live")
    print("    • minimax-t2v           -> T2V-01")
    print()
    print("For more information, visit: https://www.minimax.io/platform/document/video_generation")

    return True


if __name__ == "__main__":
    try:
        success = setup_minimax_videogen()
        if success:
            print("✅ Setup completed successfully!")
        else:
            print("❌ Setup failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
