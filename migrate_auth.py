#!/usr/bin/env python3
"""
Database migration script to add authentication tables
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from videotrans.database.models import DatabaseManager, Base, User
from videotrans.configure import config


def migrate_database():
    """Run database migration to add authentication tables"""
    print("🔄 Starting database migration for authentication...")

    try:
        # Initialize database manager
        db_manager = DatabaseManager()

        # Check if we need to drop and recreate the users table
        print("📊 Checking existing database schema...")

        # First, let's check if there's an existing users table with old schema
        session = db_manager.get_session()
        try:
            from sqlalchemy import text
            # Try to execute a raw SQL query to check table structure
            result = session.execute(text("SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND table_schema = 'public'"))
            existing_columns = [row[0] for row in result.fetchall()]

            if existing_columns:
                print(f"⚠️  Found existing users table with columns: {existing_columns}")

                # Check if it has our new schema
                required_columns = ['username', 'email', 'password_hash', 'api_key']
                has_new_schema = all(col in existing_columns for col in required_columns)

                if not has_new_schema:
                    print("🔄 Existing users table has old schema, dropping and recreating...")
                    # Drop the old users table
                    session.execute(text("DROP TABLE IF EXISTS users CASCADE"))
                    session.commit()
                    print("✅ Old users table dropped")
                else:
                    print("✅ Users table already has correct schema")
            else:
                print("✅ No existing users table found")

        except Exception as e:
            print(f"⚠️  Could not check existing schema: {e}")
            session.rollback()

        session.close()

        # Create all tables (this will create missing tables)
        print("📊 Creating authentication tables...")
        Base.metadata.create_all(db_manager.engine)

        # Test database connection with new session
        session = db_manager.get_session()
        try:
            user_count = session.query(User).count()
            print(f"✅ Users table created successfully. Current user count: {user_count}")

            # Create a default admin user if no users exist
            if user_count == 0:
                print("👤 Creating default admin user...")
                from videotrans.auth.utils import create_user

                admin_user = create_user(
                    username="admin",
                    email="admin@localhost",
                    password="admin123",
                    first_name="Admin",
                    last_name="User"
                )

                if admin_user:
                    # Make the user an admin
                    admin_user.is_admin = True
                    session.add(admin_user)
                    session.commit()
                    print(f"✅ Default admin user created:")
                    print(f"   Username: admin")
                    print(f"   Email: admin@localhost")
                    print(f"   Password: admin123")
                    print(f"   API Key: {admin_user.api_key}")
                    print("⚠️  Please change the default password after first login!")
                else:
                    print("❌ Failed to create default admin user")

        except Exception as e:
            print(f"❌ Error testing database: {e}")
            return False
        finally:
            session.close()

        print("✅ Database migration completed successfully!")
        print("\n📋 Next steps:")
        print("1. Set environment variables for authentication:")
        print("   - SECRET_KEY: A secure random string for Flask sessions")
        print("   - GOOGLE_CLIENT_ID: (Optional) For Google OAuth")
        print("   - GOOGLE_CLIENT_SECRET: (Optional) For Google OAuth")
        print("\n2. Start the API server:")
        print("   python api.py")
        print("\n3. Access the web interface:")
        print("   http://localhost:9011/")
        print("\n4. API Authentication methods:")
        print("   - Session-based: Login via web interface")
        print("   - API Key: Use X-API-KEY header")
        print("   - JWT Token: Use Authorization: Bearer <token> header")

        return True

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")

    missing_deps = []

    try:
        import flask_login
        print("✅ flask-login")
    except ImportError:
        missing_deps.append("flask-login")

    try:
        import flask_wtf
        print("✅ flask-wtf")
    except ImportError:
        missing_deps.append("flask-wtf")

    try:
        import flask_bcrypt
        print("✅ flask-bcrypt")
    except ImportError:
        missing_deps.append("flask-bcrypt")

    try:
        import jwt
        print("✅ PyJWT")
    except ImportError:
        missing_deps.append("PyJWT")

    try:
        import google.auth
        print("✅ google-auth")
    except ImportError:
        missing_deps.append("google-auth")

    try:
        import google_auth_oauthlib
        print("✅ google-auth-oauthlib")
    except ImportError:
        missing_deps.append("google-auth-oauthlib")

    try:
        import email_validator
        print("✅ email-validator")
    except ImportError:
        missing_deps.append("email-validator")

    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them with:")
        print(f"pip install {' '.join(missing_deps)}")
        return False

    print("✅ All dependencies are installed!")
    return True


if __name__ == "__main__":
    print("🚀 PyVideoTrans Authentication Migration")
    print("=" * 50)

    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)

    # Run migration
    if migrate_database():
        print("\n🎉 Migration completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Migration failed!")
        sys.exit(1)
