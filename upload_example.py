#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Example usage of the /upload endpoint
"""

import requests
import json

def upload_file_example(file_path, custom_name=None):
    """
    Example function showing how to upload a file to the API

    Args:
        file_path (str): Path to the file to upload
        custom_name (str, optional): Custom name for the uploaded media

    Returns:
        dict: Response from the API
    """
    
    api_url = "http://127.0.0.1:9011/upload"
    headers = {'X-USER-ID': '1'}  # Required for user-scoped operations

    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {}
            if custom_name:
                data['name'] = custom_name
            response = requests.post(api_url, files=files, data=data if data else None, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print("✅ Upload successful!")
                print(f"Access URL: {data['data']['url']}")
                if custom_name:
                    print(f"Custom name: {data['data'].get('name', 'Not set')}")
                return data
            else:
                print(f"❌ Upload failed: {data.get('msg')}")
                return data
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the API server is running.")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def upload_url_example(url, custom_name=None):
    """
    Example function showing how to upload a file from URL to the API

    Args:
        url (str): URL of the file to download and upload
        custom_name (str, optional): Custom name for the uploaded media

    Returns:
        dict: Response from the API
    """

    api_url = "http://127.0.0.1:9011/upload"
    headers = {'X-USER-ID': '1'}  # Required for user-scoped operations

    try:
        data = {'url': url}
        if custom_name:
            data['name'] = custom_name

        response = requests.post(api_url, json=data, headers=headers)

        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print("✅ URL upload successful!")
                print(f"Access URL: {result['data']['url']}")
                if custom_name:
                    print(f"Custom name: {result['data'].get('name', 'Not set')}")
                return result
            else:
                print(f"❌ URL upload failed: {result.get('msg')}")
                return result
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None

    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure the API server is running.")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def download_uploaded_file(file_url, save_path):
    """
    Example function showing how to download an uploaded file
    
    Args:
        file_url (str): URL of the uploaded file
        save_path (str): Local path to save the downloaded file
    """
    
    try:
        response = requests.get(file_url)
        
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            print(f"✅ File downloaded to: {save_path}")
        else:
            print(f"❌ Download failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Download error: {str(e)}")

if __name__ == "__main__":
    # Example usage
    print("📁 File Upload Example")
    print("=" * 30)
    
    # Example 1: Upload a file (you need to provide a real file path)
    file_to_upload = "test_file.txt"  # Change this to an actual file path
    
    # Create a test file if it doesn't exist
    import os
    if not os.path.exists(file_to_upload):
        with open(file_to_upload, 'w') as f:
            f.write("This is a test file for upload demonstration.\n")
            f.write("Created for API testing purposes.\n")
        print(f"📝 Created test file: {file_to_upload}")
    
    # Upload the file with custom name
    custom_name = "My Test Document"
    print(f"📤 Uploading file with custom name: {custom_name}")
    result = upload_file_example(file_to_upload, custom_name)
    
    if result and result.get('code') == 0:
        # Example 2: Download the uploaded file
        file_url = result['data']['url']
        download_path = "downloaded_" + os.path.basename(file_to_upload)
        
        print(f"\n📥 Downloading file from: {file_url}")
        download_uploaded_file(file_url, download_path)
        
        # Clean up
        if os.path.exists(download_path):
            os.unlink(download_path)
            print(f"🗑️  Cleaned up: {download_path}")

    # Example 3: Upload from URL with custom name
    print(f"\n🌐 URL Upload Example")
    print("=" * 30)
    test_url = "https://httpbin.org/robots.txt"
    url_custom_name = "Robots.txt from HTTPBin"
    print(f"📤 Uploading from URL with custom name: {url_custom_name}")
    url_result = upload_url_example(test_url, url_custom_name)

    # Clean up test file
    if os.path.exists(file_to_upload):
        os.unlink(file_to_upload)
        print(f"🗑️  Cleaned up: {file_to_upload}")
    
    print("\n✅ Example completed!")
