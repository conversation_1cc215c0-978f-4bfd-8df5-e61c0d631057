# Docker Environment Configuration for PyVideoTrans
# Copy this file to .env and fill in your API keys

# =============================================================================
# API Keys (Required for respective providers)
# =============================================================================

# Anthropic Claude API Key (Required for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI API Key (Optional, for OpenAI models)
OPENAI_API_KEY=your_openai_api_key_here

# Google AI API Key (Optional, for Gemini models)
GOOGLE_API_KEY=your_google_api_key_here

# Perplexity API Key (Optional, for research features)
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Mistral API Key (Optional, for Mistral models)
MISTRAL_API_KEY=your_mistral_api_key_here

# xAI API Key (Optional, for xAI models)
XAI_API_KEY=your_xai_api_key_here

# Azure OpenAI API Key (Optional, for Azure OpenAI models)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here

# =============================================================================
# Database Configuration (Automatically configured in docker-compose)
# =============================================================================

POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=pyvideotrans
POSTGRES_USER=pyvideotrans
POSTGRES_PASSWORD=pyvideotrans_password

# =============================================================================
# Application Configuration
# =============================================================================

# Timezone
TZ=UTC

# Python settings
PYTHONUNBUFFERED=1

# =============================================================================
# Additional Service API Keys (Optional)
# =============================================================================

# Azure Speech Services
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here

# DeepL Translation
DEEPL_API_KEY=your_deepl_api_key_here

# ElevenLabs TTS
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Deepgram Speech Recognition
DEEPGRAM_API_KEY=your_deepgram_api_key_here
