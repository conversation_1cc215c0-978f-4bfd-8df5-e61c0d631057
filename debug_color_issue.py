#!/usr/bin/env python3
"""
Debug the color issue in subtitle styling
"""

import sys
import os
sys.path.append('/Users/<USER>/PycharmProjects/pyvideotrans')

from videotrans.task.as_s import SubtitleStyle, generate_bilingual_ass, VideoFormat, Subtitle

def debug_color_flow():
    """Debug the exact color flow through the system"""
    
    print("=== DEBUGGING COLOR ISSUE ===")
    
    # Test the exact configuration from the logs
    secondary_style_config = {
        'fontFamily': 'NotoSansCJK-Regular',
        'fontSize': 19,
        'color': '&H001CE10E',
        'strokeColor': '&H000505E1',
        'backgroundColor': '&H001FE5D8',  # This should be lime green RGB(216, 229, 31)
        'strokeWidth': 0,
        'marginV': 339,
        'showStroke': False,
        'showBackground': True,
        'showShadow': False
    }
    
    primary_style_config = {
        'fontFamily': 'NotoSansCJK-Regular',
        'fontSize': 30,
        'color': '&H001616DA',
        'strokeColor': '&H00000000',
        'backgroundColor': '&H001717D3',
        'strokeWidth': 1,
        'marginV': 233,
        'showStroke': False,
        'showBackground': True,
        'showShadow': False
    }
    
    print(f"Input secondary backgroundColor: {secondary_style_config['backgroundColor']}")
    
    # Simulate _create_combined_subtitle_style
    secondary_background_color = secondary_style_config.get('backgroundColor', '#000000')
    show_secondary_background = secondary_style_config.get('showBackground', False)
    
    print(f"Extracted secondary_background_color: {secondary_background_color}")
    print(f"show_secondary_background: {show_secondary_background}")
    
    # Create SubtitleStyle object
    style = SubtitleStyle(
        font_family=primary_style_config.get('fontFamily', 'Arial'),
        font_size=primary_style_config.get('fontSize', 20),
        primary_color=primary_style_config.get('color', '#FFFFFF'),
        primary_stroke_width=primary_style_config.get('strokeWidth', 0),
        primary_stroke_color=primary_style_config.get('strokeColor', '#000000'),
        primary_margin_v=primary_style_config.get('marginV', 40),
        primary_background_color=primary_style_config.get('backgroundColor', '#000000'),
        show_primary_background=primary_style_config.get('showBackground', False),
        secondary_background_color=secondary_background_color,  # This should be &H001FE5D8
        secondary_color=secondary_style_config.get('color', '#FFFF00'),
        secondary_font_family=secondary_style_config.get('fontFamily', 'Arial'),
        secondary_font_size=secondary_style_config.get('fontSize', 18),
        secondary_stroke_color=secondary_style_config.get('strokeColor', '#000000'),
        secondary_stroke_width=secondary_style_config.get('strokeWidth', 1),
        secondary_margin_v=secondary_style_config.get('marginV', 0),
        shadow_color='#000000',
        show_primary_shadow=primary_style_config.get('showShadow', False),
        show_primary_stroke=primary_style_config.get('showStroke', False),
        show_secondary_background=show_secondary_background,
        show_secondary_shadow=secondary_style_config.get('showShadow', False),
        show_secondary_stroke=secondary_style_config.get('showStroke', False)
    )
    
    print(f"SubtitleStyle.secondary_background_color: {style.secondary_background_color}")
    print(f"SubtitleStyle.show_secondary_background: {style.show_secondary_background}")
    
    # Test data
    src_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Hello world"),
    ]
    
    trans_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Xin chào thế giới"),
    ]
    
    # Generate ASS
    video_format = VideoFormat(width=1920, height=720)
    ass_content = generate_bilingual_ass(src_subs, trans_subs, style, video_format)
    
    print("\n=== GENERATED ASS CONTENT ===")
    print(ass_content)
    
    # Extract the Secondary style line
    lines = ass_content.split('\n')
    for line in lines:
        if line.startswith('Style: Secondary,'):
            print(f"\n=== SECONDARY STYLE LINE ===")
            print(line)
            
            # Parse the style line
            parts = line.split(',')
            if len(parts) >= 7:
                background_color = parts[6]
                print(f"Background color in ASS: {background_color}")
                
                # Check if this matches our expected color
                expected = '&H001FE5D8'
                if background_color == expected:
                    print("✅ Color is CORRECT!")
                else:
                    print(f"❌ Color is WRONG! Expected: {expected}, Got: {background_color}")
            break

if __name__ == "__main__":
    debug_color_flow()
