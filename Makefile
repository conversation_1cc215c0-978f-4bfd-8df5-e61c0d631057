# PyVideoTrans Docker Makefile
# Usage: make [target]

# Configuration
DOCKER_USERNAME ?= $(shell docker info | grep "Username:" | awk '{print $$2}')
IMAGE_NAME = pyvideotrans
VERSION ?= latest
REGISTRY = docker.io

# Full image name
FULL_IMAGE_NAME = $(REGISTRY)/$(DOCKER_USERNAME)/$(IMAGE_NAME):$(VERSION)

# Default target
.DEFAULT_GOAL := help

# Colors
BLUE = \033[0;34m
GREEN = \033[0;32m
YELLOW = \033[1;33m
NC = \033[0m

.PHONY: help build push test clean login logout info dev-up dev-down

help: ## Show this help message
	@echo "$(BLUE)PyVideoTrans Docker Commands$(NC)"
	@echo "============================="
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "Variables:"
	@echo "  DOCKER_USERNAME=$(DOCKER_USERNAME)"
	@echo "  VERSION=$(VERSION)"
	@echo "  FULL_IMAGE_NAME=$(FULL_IMAGE_NAME)"

info: ## Show Docker and image information
	@echo "$(BLUE)Docker Information$(NC)"
	@echo "=================="
	@docker --version
	@docker-compose --version
	@echo ""
	@echo "$(BLUE)Login Status$(NC)"
	@docker info | grep -E "(Username|Registry)" || echo "Not logged in"
	@echo ""
	@echo "$(BLUE)Image Information$(NC)"
	@echo "DOCKER_USERNAME: $(DOCKER_USERNAME)"
	@echo "IMAGE_NAME: $(IMAGE_NAME)"
	@echo "VERSION: $(VERSION)"
	@echo "FULL_IMAGE_NAME: $(FULL_IMAGE_NAME)"

login: ## Login to Docker Hub
	@echo "$(BLUE)Logging in to Docker Hub...$(NC)"
	docker login

logout: ## Logout from Docker Hub
	@echo "$(BLUE)Logging out from Docker Hub...$(NC)"
	docker logout

build: ## Build Docker image
	@echo "$(BLUE)Building Docker image: $(FULL_IMAGE_NAME)$(NC)"
	docker build -t $(FULL_IMAGE_NAME) .
	@echo "$(GREEN)Build completed: $(FULL_IMAGE_NAME)$(NC)"

build-no-cache: ## Build Docker image without cache
	@echo "$(BLUE)Building Docker image without cache: $(FULL_IMAGE_NAME)$(NC)"
	docker build --no-cache -t $(FULL_IMAGE_NAME) .
	@echo "$(GREEN)Build completed: $(FULL_IMAGE_NAME)$(NC)"

build-multiplatform: ## Build multi-platform Docker image
	@echo "$(BLUE)Building multi-platform Docker image: $(FULL_IMAGE_NAME)$(NC)"
	docker buildx build --platform linux/amd64,linux/arm64 -t $(FULL_IMAGE_NAME) --push .
	@echo "$(GREEN)Multi-platform build and push completed: $(FULL_IMAGE_NAME)$(NC)"

test: ## Test the built Docker image
	@echo "$(BLUE)Testing Docker image: $(FULL_IMAGE_NAME)$(NC)"
	@docker run -d --name pyvideotrans-test -p 9012:9011 $(FULL_IMAGE_NAME)
	@echo "Waiting for service to start..."
	@sleep 15
	@curl -f http://localhost:9012/api/config/settings > /dev/null 2>&1 && \
		echo "$(GREEN)✅ Test passed - service is responding$(NC)" || \
		echo "$(YELLOW)⚠️  Service might not be fully ready$(NC)"
	@docker stop pyvideotrans-test > /dev/null
	@docker rm pyvideotrans-test > /dev/null
	@echo "$(GREEN)Test completed and cleaned up$(NC)"

push: ## Push Docker image to registry
	@echo "$(BLUE)Pushing Docker image: $(FULL_IMAGE_NAME)$(NC)"
	docker push $(FULL_IMAGE_NAME)
	@echo "$(GREEN)Push completed: $(FULL_IMAGE_NAME)$(NC)"

build-and-push: build test push ## Build, test, and push Docker image
	@echo "$(GREEN)✅ Build, test, and push completed successfully!$(NC)"

quick-build: ## Quick build using the script
	@./build-and-push.sh $(VERSION)

quick-build-multiplatform: ## Quick multi-platform build using the script
	@./build-and-push.sh $(VERSION) --multiplatform

dev-up: ## Start development environment with docker-compose
	@echo "$(BLUE)Starting development environment...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Development environment started$(NC)"
	@echo "API available at: http://localhost:9011"

dev-down: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(NC)"
	docker-compose down
	@echo "$(GREEN)Development environment stopped$(NC)"

dev-logs: ## Show development environment logs
	docker-compose logs -f

dev-restart: ## Restart development environment
	@echo "$(BLUE)Restarting development environment...$(NC)"
	docker-compose restart
	@echo "$(GREEN)Development environment restarted$(NC)"

clean: ## Clean up Docker images and containers
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@docker system prune -f
	@docker images | grep "$(DOCKER_USERNAME)/$(IMAGE_NAME)" | awk '{print $$3}' | xargs -r docker rmi -f
	@echo "$(GREEN)Cleanup completed$(NC)"

clean-all: ## Clean up all Docker resources (WARNING: removes all unused images)
	@echo "$(YELLOW)⚠️  This will remove all unused Docker resources$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	docker system prune -a -f --volumes
	@echo "$(GREEN)Full cleanup completed$(NC)"

list-images: ## List all images for this project
	@echo "$(BLUE)Images for $(DOCKER_USERNAME)/$(IMAGE_NAME):$(NC)"
	@docker images | grep "$(DOCKER_USERNAME)/$(IMAGE_NAME)" || echo "No images found"

# Version-specific targets
build-latest: ## Build latest version
	@$(MAKE) build VERSION=latest

build-v1: ## Build v1.0.0 version
	@$(MAKE) build VERSION=v1.0.0

push-latest: ## Push latest version
	@$(MAKE) push VERSION=latest

push-v1: ## Push v1.0.0 version
	@$(MAKE) push VERSION=v1.0.0

# Release targets
release-patch: ## Build and push a patch release
	@echo "$(BLUE)Creating patch release...$(NC)"
	@$(MAKE) build-and-push VERSION=v1.0.$$(date +%Y%m%d)

release-minor: ## Build and push a minor release
	@echo "$(BLUE)Creating minor release...$(NC)"
	@$(MAKE) build-and-push VERSION=v1.$$(date +%m).0

# Check prerequisites
check: ## Check prerequisites
	@echo "$(BLUE)Checking prerequisites...$(NC)"
	@command -v docker >/dev/null 2>&1 || { echo "❌ Docker not installed"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose not installed"; exit 1; }
	@docker info >/dev/null 2>&1 || { echo "❌ Docker not running"; exit 1; }
	@docker info | grep -q "Username:" || { echo "❌ Not logged in to Docker Hub"; exit 1; }
	@echo "$(GREEN)✅ All prerequisites met$(NC)"
