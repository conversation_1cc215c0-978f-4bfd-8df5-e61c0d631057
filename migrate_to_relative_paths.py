#!/usr/bin/env python3
"""
Migration script to convert absolute paths to relative paths in media table
"""
import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def migrate_to_relative_paths():
    """Convert absolute file paths to relative paths in media table"""
    try:
        # Import here to avoid circular imports
        from videotrans.database.models import db_manager
        from sqlalchemy import text

        with db_manager.get_session() as session:
            # Get all media records with absolute paths
            result = session.execute(text("""
                SELECT id, file_path, thumbnail_path
                FROM media
                WHERE file_path LIKE '/usr/lib/media/%' OR thumbnail_path LIKE '/usr/lib/media/%'
            """)).fetchall()

            if not result:
                print("✅ No records need migration - all paths are already relative")
                return True

            print(f"📋 Found {len(result)} records to migrate...")

            updated_count = 0
            for row in result:
                media_id, file_path, thumbnail_path = row

                # Convert file_path to relative
                new_file_path = file_path
                if file_path and file_path.startswith('/usr/lib/media/'):
                    new_file_path = file_path.replace('/usr/lib/media', '')
                    if not new_file_path.startswith('/'):
                        new_file_path = '/' + new_file_path

                # Convert thumbnail_path to relative
                new_thumbnail_path = thumbnail_path
                if thumbnail_path and thumbnail_path.startswith('/usr/lib/media/'):
                    new_thumbnail_path = thumbnail_path.replace('/usr/lib/media', '')
                    if not new_thumbnail_path.startswith('/'):
                        new_thumbnail_path = '/' + new_thumbnail_path

                # Update the record
                session.execute(text("""
                    UPDATE media
                    SET file_path = :new_file_path, thumbnail_path = :new_thumbnail_path
                    WHERE id = :media_id
                """), {
                    'new_file_path': new_file_path,
                    'new_thumbnail_path': new_thumbnail_path,
                    'media_id': media_id
                })

                updated_count += 1
                print(f"✅ Updated ID {media_id}:")
                print(f"   File: {file_path} -> {new_file_path}")
                if thumbnail_path:
                    print(f"   Thumb: {thumbnail_path} -> {new_thumbnail_path}")

                # Commit in batches of 10
                if updated_count % 10 == 0:
                    session.commit()
                    print(f"   💾 Committed {updated_count} records...")

            # Final commit
            session.commit()
            print(f"\n🎉 Successfully migrated {updated_count} records to relative paths!")

            # Verify the migration
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM media
                WHERE file_path LIKE '/usr/lib/media/%' OR thumbnail_path LIKE '/usr/lib/media/%'
            """)).fetchone()

            remaining_absolute = result.count
            if remaining_absolute == 0:
                print("✅ Migration verification: All paths are now relative")
            else:
                print(f"⚠️  Migration verification: {remaining_absolute} absolute paths remain")

            return True

    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False

def verify_relative_paths():
    """Verify that all paths in the database are relative"""
    try:
        from videotrans.database.models import db_manager
        from sqlalchemy import text

        with db_manager.get_session() as session:
            # Check file paths
            result = session.execute(text("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN file_path LIKE '/usr/lib/media/%' THEN 1 ELSE 0 END) as absolute_files,
                    SUM(CASE WHEN file_path LIKE '/%' AND file_path NOT LIKE '/usr/lib/media/%' THEN 1 ELSE 0 END) as relative_files,
                    SUM(CASE WHEN thumbnail_path LIKE '/usr/lib/media/%' THEN 1 ELSE 0 END) as absolute_thumbs,
                    SUM(CASE WHEN thumbnail_path LIKE '/%' AND thumbnail_path NOT LIKE '/usr/lib/media/%' THEN 1 ELSE 0 END) as relative_thumbs
                FROM media
            """)).fetchone()

            print("📊 Path Analysis:")
            print(f"   Total records: {result.total}")
            print(f"   Absolute file paths: {result.absolute_files}")
            print(f"   Relative file paths: {result.relative_files}")
            print(f"   Absolute thumbnail paths: {result.absolute_thumbs}")
            print(f"   Relative thumbnail paths: {result.relative_thumbs}")

            # Show some examples
            examples = session.execute(text("""
                SELECT id, file_path, thumbnail_path
                FROM media
                ORDER BY id DESC
                LIMIT 3
            """)).fetchall()

            print("\n📋 Example paths:")
            for row in examples:
                media_id, file_path, thumbnail_path = row
                print(f"   ID {media_id}:")
                print(f"     File: {file_path}")
                if thumbnail_path:
                    print(f"     Thumb: {thumbnail_path}")

            return result.absolute_files == 0 and result.absolute_thumbs == 0

    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Run the migration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print("🚀 Media Path Migration: Absolute → Relative")
    print("=" * 60)

    try:
        # First verify current state
        print("🔍 Checking current path formats...")
        verify_relative_paths()

        print("\n🔄 Starting migration...")
        success = migrate_to_relative_paths()

        if success:
            print("\n✅ Migration completed successfully!")
            print("\n📋 Benefits of relative paths:")
            print("• More portable database")
            print("• Cleaner path storage")
            print("• Easier deployment and backup")
            print("• Consistent with new upload behavior")

            print("\n🔧 Path format examples:")
            print("• File: /1/250626/abc123.mp4 (instead of /usr/lib/media/1/250626/abc123.mp4)")
            print("• Thumb: /1/250626/abc123_thumb.jpg (instead of /usr/lib/media/1/250626/abc123_thumb.jpg)")

            return 0
        else:
            print("\n❌ Migration failed!")
            return 1

    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
