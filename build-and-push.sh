#!/bin/bash

# PyVideoTrans Docker Build and Push Script
# Usage: ./build-and-push.sh [version] [--multiplatform] [--no-cache]

set -e  # Exit on any error

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-}"
REPOSITORY_NAME="pyvideotrans"
DEFAULT_VERSION="latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    echo "Usage: $0 [version] [options]"
    echo ""
    echo "Arguments:"
    echo "  version           Version tag (default: latest)"
    echo ""
    echo "Options:"
    echo "  --multiplatform   Build for multiple platforms (linux/amd64,linux/arm64)"
    echo "  --no-cache        Build without using cache"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                          # Build and push latest"
    echo "  $0 v1.0.0                   # Build and push v1.0.0"
    echo "  $0 v1.0.0 --multiplatform   # Build multiplatform v1.0.0"
    echo "  $0 latest --no-cache        # Build latest without cache"
    echo ""
    echo "Environment Variables:"
    echo "  DOCKER_USERNAME    Your Docker Hub username (required)"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    
    # Check if logged in to Docker Hub
    if ! docker info | grep -q "Username:"; then
        log_error "Not logged in to Docker Hub. Please run: docker login"
        exit 1
    fi
    
    # Get Docker username if not set
    if [ -z "$DOCKER_USERNAME" ]; then
        DOCKER_USERNAME=$(docker info | grep "Username:" | awk '{print $2}')
        if [ -z "$DOCKER_USERNAME" ]; then
            log_error "Could not determine Docker Hub username. Please set DOCKER_USERNAME environment variable."
            exit 1
        fi
    fi
    
    log_success "Prerequisites check passed"
    log_info "Docker Hub username: $DOCKER_USERNAME"
}

build_image() {
    local version=$1
    local multiplatform=$2
    local no_cache=$3
    
    local image_tag="${DOCKER_USERNAME}/${REPOSITORY_NAME}:${version}"
    
    log_info "Building Docker image: $image_tag"
    
    # Build command
    local build_cmd="docker"
    local build_args=""
    
    if [ "$multiplatform" = true ]; then
        log_info "Building for multiple platforms..."
        build_cmd="docker buildx"
        build_args="--platform linux/amd64,linux/arm64"
        
        # Create buildx builder if it doesn't exist
        if ! docker buildx ls | grep -q "multiplatform"; then
            log_info "Creating multiplatform builder..."
            docker buildx create --name multiplatform --use
            docker buildx inspect --bootstrap
        else
            docker buildx use multiplatform
        fi
    fi
    
    if [ "$no_cache" = true ]; then
        build_args="$build_args --no-cache"
    fi
    
    # Build the image
    if [ "$multiplatform" = true ]; then
        $build_cmd build $build_args -t "$image_tag" --push .
        log_success "Multi-platform image built and pushed: $image_tag"
    else
        $build_cmd build $build_args -t "$image_tag" .
        log_success "Image built: $image_tag"
    fi
}

test_image() {
    local version=$1
    local image_tag="${DOCKER_USERNAME}/${REPOSITORY_NAME}:${version}"
    
    log_info "Testing image: $image_tag"
    
    # Start container for testing
    local container_name="pyvideotrans-test-$$"
    
    if docker run -d --name "$container_name" -p 9012:9011 "$image_tag" > /dev/null; then
        log_info "Container started: $container_name"
        
        # Wait a bit for the service to start
        sleep 10
        
        # Test if the service is responding
        if curl -f http://localhost:9012/api/config/settings > /dev/null 2>&1; then
            log_success "Image test passed - service is responding"
        else
            log_warning "Service might not be fully ready yet (this is normal)"
        fi
        
        # Clean up
        docker stop "$container_name" > /dev/null
        docker rm "$container_name" > /dev/null
        log_info "Test container cleaned up"
    else
        log_error "Failed to start test container"
        return 1
    fi
}

push_image() {
    local version=$1
    local image_tag="${DOCKER_USERNAME}/${REPOSITORY_NAME}:${version}"
    
    log_info "Pushing image to Docker Hub: $image_tag"
    
    if docker push "$image_tag"; then
        log_success "Image pushed successfully: $image_tag"
    else
        log_error "Failed to push image: $image_tag"
        return 1
    fi
}

main() {
    # Parse arguments
    local version="$DEFAULT_VERSION"
    local multiplatform=false
    local no_cache=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help)
                show_usage
                exit 0
                ;;
            --multiplatform)
                multiplatform=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            -*)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                version="$1"
                shift
                ;;
        esac
    done
    
    log_info "Starting build and push process..."
    log_info "Version: $version"
    log_info "Multiplatform: $multiplatform"
    log_info "No cache: $no_cache"
    
    # Check prerequisites
    check_prerequisites
    
    # Build image
    build_image "$version" "$multiplatform" "$no_cache"
    
    # For multiplatform builds, image is already pushed
    if [ "$multiplatform" = false ]; then
        # Test image locally
        test_image "$version"
        
        # Push image
        push_image "$version"
    fi
    
    log_success "Build and push completed successfully!"
    log_info "Image available at: docker pull ${DOCKER_USERNAME}/${REPOSITORY_NAME}:${version}"
    
    # Show image info
    if [ "$multiplatform" = false ]; then
        echo ""
        log_info "Local image information:"
        docker images "${DOCKER_USERNAME}/${REPOSITORY_NAME}:${version}"
    fi
}

# Run main function
main "$@"
