# PyVideoTrans Docker Setup

This guide explains how to run PyVideoTrans using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for Docker
- 10GB+ free disk space

## Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd pyvideotrans

# Copy environment file
cp .env.docker .env

# Edit .env file with your API keys
nano .env  # or use your preferred editor
```

### 2. Configure API Keys

Edit the `.env` file and add your API keys:

```bash
# Required for most features
ANTHROPIC_API_KEY=sk-ant-api03-...
OPENAI_API_KEY=sk-proj-...
GOOGLE_API_KEY=AIza...

# Optional but recommended
PERPLEXITY_API_KEY=pplx-...
DEEPL_API_KEY=...
ELEVENLABS_API_KEY=...
```

### 3. Create Data Directories

```bash
# Create persistent data directories
mkdir -p data/{apidata,tmp,logs,models,videotrans}
```

### 4. Start Services

```bash
# Start all services (API + PostgreSQL)
docker-compose up -d

# View logs
docker-compose logs -f pyvideotrans

# Check status
docker-compose ps
```

### 5. Access the API

The API will be available at: `http://localhost:9011`

- API Documentation: `http://localhost:9011/api/config/settings`
- Health Check: `http://localhost:9011/api/realtime/status`

## Configuration

### Custom Host/Port

Create a `host.txt` file to change the default host/port:

```bash
echo "0.0.0.0:9011" > host.txt
```

### Custom Configuration

You can mount custom configuration files:

```yaml
# In docker-compose.yml, add to volumes:
- ./your-config.json:/app/videotrans/cfg.json:ro
- ./your-params.json:/app/params.json:ro
```

## Usage Examples

### Text-to-Speech

```bash
curl -X POST http://localhost:9011/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello world",
    "voice": "en-US-AriaNeural",
    "rate": "+0%"
  }'
```

### File Upload

```bash
curl -X POST http://localhost:9011/upload \
  -H "X-USER-ID: user123" \
  -F "file=@your-video.mp4" \
  -F "name=My Video"
```

## Management Commands

### View Logs

```bash
# All services
docker-compose logs

# Specific service
docker-compose logs pyvideotrans
docker-compose logs postgres

# Follow logs
docker-compose logs -f pyvideotrans
```

### Restart Services

```bash
# Restart all
docker-compose restart

# Restart specific service
docker-compose restart pyvideotrans
```

### Update Application

```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Management

```bash
# Access PostgreSQL
docker-compose exec postgres psql -U pyvideotrans -d pyvideotrans

# Backup database
docker-compose exec postgres pg_dump -U pyvideotrans pyvideotrans > backup.sql

# Restore database
docker-compose exec -T postgres psql -U pyvideotrans pyvideotrans < backup.sql
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Change port in docker-compose.yml
   ports:
     - "9012:9011"  # Use port 9012 instead
   ```

2. **Permission issues**
   ```bash
   # Fix data directory permissions
   sudo chown -R 1000:1000 data/
   ```

3. **Out of memory**
   ```bash
   # Increase Docker memory limit to 4GB+
   # Check Docker Desktop settings
   ```

4. **Database connection issues**
   ```bash
   # Check if PostgreSQL is running
   docker-compose ps postgres
   
   # Check logs
   docker-compose logs postgres
   ```

### Performance Optimization

1. **Enable GPU support** (if available):
   ```yaml
   # Add to pyvideotrans service in docker-compose.yml
   deploy:
     resources:
       reservations:
         devices:
           - driver: nvidia
             count: 1
             capabilities: [gpu]
   ```

2. **Increase memory limits**:
   ```yaml
   # Add to pyvideotrans service
   mem_limit: 4g
   memswap_limit: 4g
   ```

## Security Notes

- Never commit `.env` file with real API keys
- Use strong passwords for database
- Consider using Docker secrets for production
- Regularly update base images for security patches

## Support

For issues specific to Docker setup, check:
1. Docker and Docker Compose versions
2. Available system resources
3. Network connectivity
4. File permissions

For application-specific issues, refer to the main documentation at https://pyvideotrans.com
