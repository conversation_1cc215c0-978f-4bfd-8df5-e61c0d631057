*.log
*.srt
.git
.github
.idea
edgetts.txt
tmp
images
models/*.pt
models/models--Systran--faster-whisper-base
models/models--Systran--faster-whisper-tiny
dev
venv
f5-tts
apidata
venv.bak
dist
source
build
test.py
ffmpeg/ffmpeg.exe
ffmpeg/ffprobe.exe
ffmpeg/ffplay.exe
ffmpeg/ytwin32.exe
ytlinux
ytdarwin
__pycache__
*.spec
*.ui
*.bak
*.aac
5.wav
15.wav
*副本.py
pretrained_models
elevenlabs.json
pack.bat
pack-api.bat
gitcmd.bat
google.txt
videotrans/translator/freechatgpt.py
videotrans/cfg.json
videotrans/params.json
videotrans/glossary.txt
/tmp/
runtime
f5-tts

# Arquivos do sistema
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Diretórios específicos do projeto
videotrans/core/
videotrans/planner/
videotrans/util/segments.py

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 