#!/usr/bin/env python3
"""
Configuration management script for pyvideotrans
"""
import argparse
import json
import sys
import time
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from videotrans.database.config_manager import get_config_manager
from videotrans.database.config_watcher import get_realtime_config_manager
from videotrans.database.migrations import ConfigMigrator
from videotrans.database.models import db_manager


def migrate_config():
    """Run configuration migration from JSON to database"""
    print("Starting configuration migration...")
    migrator = ConfigMigrator()
    
    if not migrator.needs_migration():
        print("Migration not needed - database already has settings")
        return True
    
    success = migrator.run_migration()
    if success:
        print("Migration completed successfully!")
    else:
        print("Migration failed!")
    return success


def backup_config(output_file: str = None):
    """Create a backup of current configuration"""
    try:
        config_manager = get_config_manager()
        settings = config_manager.get_all()

        backup_data = {
            'version': '1.0',
            'settings': settings
        }

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            print(f"Configuration backed up to {output_file}")
        else:
            print(json.dumps(backup_data, ensure_ascii=False, indent=2))

        return True
    except Exception as e:
        print(f"Backup failed: {e}")
        return False


def restore_config(input_file: str):
    """Restore configuration from backup file"""
    try:
        config_manager = get_config_manager()
        with open(input_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        if 'settings' not in backup_data:
            print("Invalid backup file format")
            return False

        settings = backup_data['settings']
        restored_count = 0

        for key, value in settings.items():
            config_manager.set(key, value)
            restored_count += 1

        print(f"Restored {restored_count} settings from {input_file}")
        return True
    except Exception as e:
        print(f"Restore failed: {e}")
        return False


def list_settings(category: str = None):
    """List all configuration settings"""
    try:
        config_manager = get_config_manager()
        if category:
            settings = config_manager.get_by_category(category)
            print(f"Settings in category '{category}':")
        else:
            settings = config_manager.get_all()
            print("All configuration settings:")

        for key, value in sorted(settings.items()):
            # Mask sensitive values
            if any(term in key.lower() for term in ['key', 'secret', 'password', 'token']):
                display_value = '*' * 8 if value else 'Not set'
            else:
                display_value = value
            print(f"  {key}: {display_value}")

        print(f"\nTotal: {len(settings)} settings")
        return True
    except Exception as e:
        print(f"Failed to list settings: {e}")
        return False


def get_setting(key: str):
    """Get a specific configuration setting"""
    try:
        config_manager = get_config_manager()
        value = config_manager.get(key)
        if value is None:
            print(f"Setting '{key}' not found")
            return False

        # Mask sensitive values
        if any(term in key.lower() for term in ['key', 'secret', 'password', 'token']):
            display_value = '*' * 8
        else:
            display_value = value

        print(f"{key}: {display_value}")
        return True
    except Exception as e:
        print(f"Failed to get setting: {e}")
        return False


def set_setting(key: str, value: str, data_type: str = None):
    """Set a configuration setting with real-time updates"""
    try:
        # Convert value based on type
        if data_type == 'int':
            value = int(value)
        elif data_type == 'float':
            value = float(value)
        elif data_type == 'bool':
            value = value.lower() in ('true', '1', 'yes', 'on')
        elif data_type == 'json':
            value = json.loads(value)

        # Use realtime config manager for immediate updates
        realtime_manager = get_realtime_config_manager()
        realtime_manager.set(key, value, data_type=data_type)
        print(f"Setting '{key}' updated successfully (real-time)")
        return True
    except Exception as e:
        print(f"Failed to set setting: {e}")
        return False


def delete_setting(key: str):
    """Delete a configuration setting with real-time updates"""
    try:
        # Use realtime config manager for immediate updates
        realtime_manager = get_realtime_config_manager()
        success = realtime_manager.delete(key)
        if success:
            print(f"Setting '{key}' deleted successfully (real-time)")
        else:
            print(f"Setting '{key}' not found")
        return success
    except Exception as e:
        print(f"Failed to delete setting: {e}")
        return False


def status():
    """Show configuration system status"""
    try:
        config_manager = get_config_manager()
        migrator = ConfigMigrator()
        needs_migration = migrator.needs_migration()
        db_available = config_manager._is_database_available()

        print("Configuration System Status:")
        print(f"  Database available: {db_available}")
        print(f"  Needs migration: {needs_migration}")

        # Show database type
        try:
            from videotrans.database.models import db_manager
            if hasattr(db_manager, 'use_postgresql') and db_manager.use_postgresql:
                from videotrans.database.postgres_config import postgres_config
                print(f"  Database type: PostgreSQL")
                print(f"  Connection: {postgres_config.get_connection_info()}")
            else:
                print(f"  Database type: SQLite")
                print(f"  Database file: {db_manager.database_url}")
        except Exception:
            print(f"  Database type: Unknown")

        if db_available:
            settings_count = len(config_manager.get_all())
            print(f"  Total settings: {settings_count}")

        # Show realtime configuration status
        try:
            realtime_manager = get_realtime_config_manager()
            print(f"  Realtime watcher: {'Running' if realtime_manager.watcher._running else 'Stopped'}")
            print(f"  Registered services: {len(realtime_manager._service_instances)}")
        except Exception as e:
            print(f"  Realtime status: Error - {e}")

        return True
    except Exception as e:
        print(f"Failed to get status: {e}")
        return False


def test_realtime_updates():
    """Test real-time configuration updates"""
    try:
        print("Testing real-time configuration updates...")

        # Get realtime manager
        realtime_manager = get_realtime_config_manager()

        # Test setting
        test_key = "realtime_test_setting"
        test_value = f"test_value_{int(time.time())}"

        print(f"Setting {test_key} = {test_value}")
        realtime_manager.set(test_key, test_value, category="Test")

        # Verify setting
        config_manager = get_config_manager()
        retrieved_value = config_manager.get(test_key)
        if retrieved_value == test_value:
            print("✅ Real-time setting successful")
        else:
            print("❌ Real-time setting failed")
            return False

        # Test update
        new_value = f"updated_value_{int(time.time())}"
        print(f"Updating {test_key} = {new_value}")
        realtime_manager.set(test_key, new_value, category="Test")

        # Verify update
        updated_value = config_manager.get(test_key)
        if updated_value == new_value:
            print("✅ Real-time update successful")
        else:
            print("❌ Real-time update failed")
            return False

        # Clean up
        realtime_manager.delete(test_key)
        print("✅ Real-time configuration test completed successfully")

        return True

    except Exception as e:
        print(f"❌ Real-time test failed: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Configuration management for pyvideotrans')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Migration command
    subparsers.add_parser('migrate', help='Migrate configuration from JSON to database')
    
    # Backup command
    backup_parser = subparsers.add_parser('backup', help='Create configuration backup')
    backup_parser.add_argument('-o', '--output', help='Output file for backup')
    
    # Restore command
    restore_parser = subparsers.add_parser('restore', help='Restore configuration from backup')
    restore_parser.add_argument('file', help='Backup file to restore from')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List configuration settings')
    list_parser.add_argument('-c', '--category', help='Filter by category')
    
    # Get command
    get_parser = subparsers.add_parser('get', help='Get a configuration setting')
    get_parser.add_argument('key', help='Setting key to get')
    
    # Set command
    set_parser = subparsers.add_parser('set', help='Set a configuration setting')
    set_parser.add_argument('key', help='Setting key to set')
    set_parser.add_argument('value', help='Setting value to set')
    set_parser.add_argument('-t', '--type', choices=['string', 'int', 'float', 'bool', 'json'],
                           help='Data type of the value')
    
    # Delete command
    delete_parser = subparsers.add_parser('delete', help='Delete a configuration setting')
    delete_parser.add_argument('key', help='Setting key to delete')
    
    # Status command
    subparsers.add_parser('status', help='Show configuration system status')

    # Test realtime command
    subparsers.add_parser('test-realtime', help='Test real-time configuration updates')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Execute command
    success = False
    
    if args.command == 'migrate':
        success = migrate_config()
    elif args.command == 'backup':
        success = backup_config(args.output)
    elif args.command == 'restore':
        success = restore_config(args.file)
    elif args.command == 'list':
        success = list_settings(args.category)
    elif args.command == 'get':
        success = get_setting(args.key)
    elif args.command == 'set':
        success = set_setting(args.key, args.value, args.type)
    elif args.command == 'delete':
        success = delete_setting(args.key)
    elif args.command == 'status':
        success = status()
    elif args.command == 'test-realtime':
        success = test_realtime_updates()
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
