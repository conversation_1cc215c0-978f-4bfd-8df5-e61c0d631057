# PyVideoTrans API Documentation

## Overview

PyVideoTrans API is a comprehensive video translation and processing service that provides:
- Text-to-Speech (TTS) synthesis
- Speech recognition and transcription
- Subtitle translation
- Complete video translation workflows
- AI-powered chat completions
- Image and video generation
- File upload and media management
- Real-time configuration management

**Base URL**: `http://127.0.0.1:9011` (configurable via `host.txt`)

## Authentication & User Context

### X-USER-ID Header
Many endpoints require user context for scoped operations:

```http
X-USER-ID: your-user-id
```

**Required for endpoints:**
- `/api/tasks/*` - Task management
- `/api/media/*` - Media library
- `/upload` - File uploads (optional but recommended)

**Not required for:**
- `/file/*` - File serving
- `/api/config/*` - Configuration
- `/v1/chat/completions` - OpenAI compatible endpoint

### OpenAI Compatible Endpoints
Some endpoints require Bearer token authentication:

```http
Authorization: Bearer your-api-key
```

## Core Processing Endpoints

### 1. Text-to-Speech (TTS)

**POST** `/tts`

Convert SRT subtitles to audio using various TTS providers.

**Request Body:**
```json
{
  "name": "/path/to/subtitle.srt",
  "tts_type": 0,
  "voice_role": "zh-CN-YunjianNeural",
  "target_language_code": "zh-cn",
  "voice_rate": "+0%",
  "volume": "+0%",
  "pitch": "+0Hz",
  "out_ext": "mp3",
  "voice_autorate": true
}
```

**Parameters:**
- `name` (required): SRT file path or SRT content string
- `tts_type` (required): TTS provider (0=Edge-TTS, 1=CosyVoice, 2=ChatTTS, etc.)
- `voice_role` (required): Voice character name
- `target_language_code` (required): Target language code
- `voice_rate` (optional): Speed adjustment ("+10%" or "-10%")
- `volume` (optional): Volume adjustment (Edge-TTS only)
- `pitch` (optional): Pitch adjustment (Edge-TTS only)
- `out_ext` (optional): Output format (mp3|wav|flac|aac)
- `voice_autorate` (optional): Auto-adjust speed for subtitle alignment

**Response:**
```json
{
  "code": 0,
  "msg": "ok",
  "task_id": "uuid-string"
}
```

### 2. Subtitle Translation

**POST** `/translate_srt`

Translate SRT subtitles between languages.

**Request Body:**
```json
{
  "name": "/path/to/subtitle.srt",
  "translate_type": 0,
  "target_language": "en",
  "source_code": "zh-cn"
}
```

**Parameters:**
- `name` (required): SRT file path or content
- `translate_type` (required): Translation provider ID
- `target_language` (required): Target language code
- `source_code` (optional): Source language code

**Response:**
```json
{
  "code": 0,
  "msg": "ok", 
  "task_id": "uuid-string"
}
```

### 3. Speech Recognition

**POST** `/recogn`

Convert audio/video to subtitles using speech recognition.

**Request Body:**
```json
{
  "name": "/path/to/video.mp4",
  "recogn_type": 0,
  "model_name": "tiny",
  "detect_language": "zh",
  "split_type": "all",
  "is_cuda": false
}
```

**Parameters:**
- `name` (required): Audio/video file path
- `recogn_type` (required): Recognition provider (0=faster-whisper, 1=openai-whisper, etc.)
- `model_name` (required): Model name for whisper
- `detect_language` (required): Audio language code
- `split_type` (optional): Processing mode ("all" or "avg")
- `is_cuda` (optional): Enable CUDA acceleration

### 4. Complete Video Translation

**POST** `/trans_video`

Full video translation pipeline: recognition → translation → TTS → video assembly.

**Request Body:**
```json
{
  "name": "/path/to/video.mp4",
  "recogn_type": 0,
  "model_name": "tiny",
  "translate_type": 0,
  "source_language": "zh-cn",
  "target_language": "en",
  "tts_type": 0,
  "voice_role": "en-US-AriaNeural",
  "voice_rate": "+0%",
  "volume": "+0%",
  "pitch": "+0Hz",
  "subtitle_type": 1,
  "append_video": false,
  "is_cuda": false
}
```

**Key Parameters:**
- `subtitle_type`: 0=no subtitles, 1=hard subtitles, 2=soft subtitles, 3=dual hard, 4=dual soft
- `append_video`: Extend video if audio is longer
- `only_video`: Generate only video file

## Voice Management

### Get Voice List

**GET** `/voice_list` or `/voices`

Get available voices for a TTS provider.

**Query Parameters:**
- `tts_type` (required): TTS provider ID
- `language` (optional): Filter by language code

**Response:**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "service_name": "Edge-TTS",
    "service_id": 0,
    "voices": [
      {"id": "zh-CN-YunjianNeural", "name": "Yunjian (Chinese)"},
      {"id": "en-US-AriaNeural", "name": "Aria (English)"}
    ]
  }
}
```

## File Management

### File Upload

**POST** `/upload`

Upload files or download from URLs with automatic thumbnail generation for videos.

#### Method 1: File Upload

**Headers:**
```http
Content-Type: multipart/form-data
X-USER-ID: your-user-id  # Optional but recommended
```

**Form Data:**
- `file`: File to upload

#### Method 2: URL Upload

**Headers:**
```http
Content-Type: application/json
X-USER-ID: your-user-id  # Optional but recommended
```

**JSON Body:**
```json
{
  "url": "https://example.com/path/to/video.mp4"
}
```

**Response (both methods):**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "url": "http://127.0.0.1:9011/file/20241201/hash_filename.mp4",
    "filename": "hash_filename.mp4",
    "original_filename": "original.mp4",
    "file_size": 1024000,
    "mime_type": "video/mp4",
    "media_type": "video",
    "thumbnail_url": "http://127.0.0.1:9011/file/20241201/hash_filename_thumb.jpg",
    "media_id": 123
  }
}
```

**URL Upload Notes:**
- Supports HTTP and HTTPS URLs
- Maximum file size: 500MB
- Download timeout: 60 seconds
- Automatically extracts filename from URL or Content-Disposition header
- Same processing pipeline as file uploads (thumbnails, metadata extraction)

**Error Response:**
```json
{
  "code": 1,
  "msg": "Error message"
}
```

**Common Error Messages:**
- `"Invalid URL format"` - URL is malformed or uses unsupported protocol
- `"Failed to download file: [reason]"` - Network error, timeout, or server error
- `"File too large: XMB (max: 500MB)"` - Downloaded file exceeds size limit
- `"No file provided"` - Missing file in multipart request
- `"URL parameter is required for JSON requests"` - Missing URL in JSON request

### File Access

**GET** `/file/<path:filepath>`

Access uploaded files.

**Examples:**
- `GET /file/20241201/video.mp4`
- `GET /file/user123/20241201/video.mp4`

## Task Management

### Get Task Status

**GET/POST** `/task_status`

Check task progress and results.

**Parameters:**
- `task_id` (required): Task UUID

**Response (In Progress):**
```json
{
  "code": -1,
  "msg": "Processing audio..."
}
```

**Response (Completed):**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "url": [
      "http://127.0.0.1:9011/apidata/task-id/output.mp4",
      "http://127.0.0.1:9011/apidata/task-id/subtitles.srt"
    ]
  }
}
```

### Multiple Task Status

**POST** `/task_status_list`

Get status for multiple tasks.

**Request Body:**
```json
{
  "task_id_list": ["uuid1", "uuid2", "uuid3"]
}
```

## User-Scoped APIs

### Task Management

**GET** `/api/tasks`

Get user's tasks with filtering.

**Headers:**
```http
X-USER-ID: your-user-id
```

**Query Parameters:**
- `status`: Filter by status
- `type`: Filter by task type
- `limit`: Results limit (default: 100)
- `offset`: Results offset (default: 0)
- `order_by`: Sort field (default: created_at)
- `order_desc`: Sort direction (default: true)

**Response:**
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "tasks": [
      {
        "id": 123,
        "uuid": "task-uuid-here",
        "user_id": "user123",
        "name": "Video Translation Task",
        "type": "TransCreate",
        "status": "completed",
        "progress": 100.0,
        "status_text": "Task completed successfully",
        "stage": "done",
        "source_file": "video.mp4",
        "output_files": ["translated_video.mp4", "subtitles.srt"],
        "config": {
          "source_language": "en",
          "target_language": "es"
        },
        "error_message": null,
        "retry_count": 0,
        "created_at": "2024-01-01T10:00:00",
        "started_at": "2024-01-01T10:01:00",
        "completed_at": "2024-01-01T10:15:00",
        "updated_at": "2024-01-01T10:15:00"
      }
    ],
    "stats": {
      "by_status": {"completed": 5, "failed": 1},
      "by_type": {"TransCreate": 4, "VideoGen": 2},
      "total": 6
    },
    "pagination": {
      "limit": 100,
      "offset": 0,
      "total": 6
    }
  }
}
```

**Security Note:** Sensitive configuration fields like `cache_folder`, `target_dir`, `dirname`, and other internal paths are automatically filtered out from API responses for security purposes.

**GET** `/api/tasks/{task_uuid}`

Get specific task details.

**POST** `/api/tasks/{task_uuid}/stop`

Stop a running task.

**POST** `/api/tasks/{task_uuid}/cancel`

Cancel a task by setting its status to 'cancelled'.

**DELETE** `/api/tasks/{task_uuid}`

Force delete a task and all related data (task logs, task stages, task media associations). This permanently removes the task from the database.

**Response:**
```json
{
  "code": 0,
  "msg": "Task deleted successfully",
  "data": {
    "task_uuid": "uuid-here",
    "status": "deleted"
  }
}
```

### Media Library

**GET** `/api/media`

Get user's media files.

**Query Parameters:**
- `type`: Filter by MIME type
- `search`: Search in filenames
- `limit`, `offset`, `order_by`, `order_desc`: Pagination

**GET** `/api/media/{media_id}`

Get specific media details.

**DELETE** `/api/media/{media_id}`

Soft delete media file.

## AI Services

### OpenAI Compatible Chat

**POST** `/v1/chat/completions`

OpenAI-compatible chat completions.

**Headers:**
```http
Authorization: Bearer your-api-key
Content-Type: application/json
```

**Request Body:**
```json
{
  "model": "gemini-1.5-pro",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "temperature": 0.7,
  "max_tokens": 4096
}
```

### Image Generation

**POST** `/generate_image`

Generate images using BytePlus ModelArk.

**Request Body:**
```json
{
  "prompt": "A beautiful landscape with mountains",
  "size": "1024x1024"
}
```

**Response:**
```json
{
  "code": 0,
  "msg": "ok",
  "image_url": "https://generated-image-url.com/image.jpg"
}
```

### Video Generation

**POST** `/generate_video`

Generate videos from text or images.

**Request Body:**
```json
{
  "prompt": "A dog running in sunshine --ratio 16:9",
  "image_path": "/path/to/image.jpg",
  "videogen_type": 0,
  "async_mode": true
}
```

**GET** `/video_status`

Check video generation status.

**Query Parameters:**
- `task_id`: Video generation task ID

## Subtitle Styling & Dubbing

**POST** `/dubbing`

Apply subtitle styles and dubbing to videos.

**Request Body:**
```json
{
  "srt_url": "http://127.0.0.1:9011/file/subtitle.srt",
  "video_url": "http://127.0.0.1:9011/file/video.mp4",
  "voiceSeparation": true,
  "sourceLanguage": "en",
  "targetLanguage": "zh-cn",
  "subtitleLayout": "double",
  "subtitleStyle": {
    "fontSize": 15,
    "fontFamily": "NotoSansCJK-Regular",
    "primaryColor": "&H00A6A6E2"
  },
  "dubbing_settings": {
    "tts_type": 0,
    "voice_role": "zh-CN-YunjianNeural",
    "voice_rate": "+0%"
  }
}
```

## Error Handling

### Standard Error Response
```json
{
  "code": 1,
  "msg": "Error description"
}
```

### Common Error Codes
- `code: 0` - Success
- `code: -1` - Task in progress
- `code: 1` - General error
- `code: 3` - Task error
- `code: 4` - Language not supported
- `code: 5` - API configuration missing

### HTTP Status Codes
- `200` - Success
- `400` - Bad request
- `401` - Unauthorized
- `404` - Not found
- `500` - Internal server error

## Language Codes

Supported languages: `zh-cn`, `zh-tw`, `en`, `fr`, `de`, `ja`, `ko`, `ru`, `es`, `th`, `it`, `pt`, `vi`, `ar`, `tr`, `hi`, `hu`, `uk`, `id`, `ms`, `kk`, `cs`, `pl`, `nl`, `sv`

## TTS Provider IDs

- `0` - Edge-TTS
- `1` - CosyVoice  
- `2` - ChatTTS
- `3` - 302.AI
- `4` - FishTTS
- `5` - Azure-TTS
- `6` - GPT-SoVITS
- `7` - Clone-Voice
- `8` - OpenAI TTS
- `9` - ElevenLabs
- `10` - Google TTS
- `11` - Custom TTS API

## Configuration Management

The API includes real-time configuration endpoints at `/api/config/*` for managing system settings without restart.

For complete API reference and examples, visit: https://pyvideotrans.com/api-cn
