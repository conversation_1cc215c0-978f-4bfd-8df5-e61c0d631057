#!/usr/bin/env python3
"""
Example script demonstrating URL upload functionality for the /upload API endpoint.

This script shows how to upload files from URLs using the new URL upload feature.
"""

import requests
import json


def upload_from_url(api_url, file_url, user_id=None):
    """
    Upload a file from URL using the /upload API endpoint
    
    Args:
        api_url (str): Base API URL (e.g., "http://127.0.0.1:9011")
        file_url (str): URL of the file to upload
        user_id (str, optional): User ID for user-scoped uploads
        
    Returns:
        dict: Response from the API
    """
    
    upload_endpoint = f"{api_url}/upload"
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json'
    }
    
    # Add user ID header if provided
    if user_id:
        headers['X-USER-ID'] = user_id
    
    # Prepare JSON payload
    payload = {
        "url": file_url
    }
    
    try:
        print(f"📥 Uploading from URL: {file_url}")
        response = requests.post(upload_endpoint, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print("✅ Upload successful!")
                upload_data = data['data']
                print(f"📁 Filename: {upload_data['filename']}")
                print(f"📄 Original: {upload_data['original_filename']}")
                print(f"📊 Size: {upload_data['file_size']} bytes")
                print(f"🔗 URL: {upload_data['url']}")
                print(f"🎭 Type: {upload_data['media_type']}")
                
                if 'thumbnail_url' in upload_data:
                    print(f"🖼️  Thumbnail: {upload_data['thumbnail_url']}")
                
                if 'media_id' in upload_data:
                    print(f"🆔 Media ID: {upload_data['media_id']}")
                
                return data
            else:
                print(f"❌ Upload failed: {data.get('msg')}")
                return data
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
                return error_data
            except:
                print(f"Response: {response.text}")
                return None
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None


def upload_file_traditional(api_url, file_path, user_id=None):
    """
    Upload a file using traditional multipart/form-data method for comparison
    
    Args:
        api_url (str): Base API URL
        file_path (str): Path to local file
        user_id (str, optional): User ID for user-scoped uploads
        
    Returns:
        dict: Response from the API
    """
    
    upload_endpoint = f"{api_url}/upload"
    
    # Prepare headers (don't set Content-Type for multipart, requests will handle it)
    headers = {}
    if user_id:
        headers['X-USER-ID'] = user_id
    
    try:
        print(f"📤 Uploading file: {file_path}")
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(upload_endpoint, headers=headers, files=files)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                print("✅ Upload successful!")
                upload_data = data['data']
                print(f"📁 Filename: {upload_data['filename']}")
                print(f"📄 Original: {upload_data['original_filename']}")
                print(f"📊 Size: {upload_data['file_size']} bytes")
                print(f"🔗 URL: {upload_data['url']}")
                return data
            else:
                print(f"❌ Upload failed: {data.get('msg')}")
                return data
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None


def main():
    """
    Example usage of both upload methods
    """
    
    # Configuration
    API_URL = "http://127.0.0.1:9011"
    USER_ID = "test_user_123"  # Optional
    
    print("🚀 Testing URL Upload API")
    print("=" * 50)
    
    # Example 1: Upload from URL
    print("\n1️⃣  Testing URL Upload")
    print("-" * 30)
    
    # Example URLs (replace with actual URLs)
    test_urls = [
        "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
        "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4",
        # Add more test URLs as needed
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\nTest {i}: {url}")
        result = upload_from_url(API_URL, url, USER_ID)
        if result:
            print("Success!")
        else:
            print("Failed!")
        print()
    
    # Example 2: Traditional file upload (for comparison)
    print("\n2️⃣  Testing Traditional File Upload")
    print("-" * 40)
    
    # Example local file (create a test file or use existing one)
    test_file = "test_video.mp4"  # Replace with actual file path
    
    try:
        result = upload_file_traditional(API_URL, test_file, USER_ID)
        if result:
            print("Traditional upload successful!")
        else:
            print("Traditional upload failed!")
    except Exception as e:
        print(f"Traditional upload test skipped: {e}")
    
    print("\n✨ Testing completed!")


if __name__ == "__main__":
    main()
