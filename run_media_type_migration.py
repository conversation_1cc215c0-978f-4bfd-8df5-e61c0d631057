#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the media_type column migration
"""
import sys
import os
import logging

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from videotrans.database.migrations.add_media_type_column import migrate_add_media_type_column

def main():
    """Run the media_type migration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    logger.info("Starting media_type column migration...")
    
    try:
        success = migrate_add_media_type_column()
        
        if success:
            logger.info("✅ Migration completed successfully!")
            print("\n🎉 Media type migration completed!")
            print("The media table now has a 'media_type' column with values: 'video', 'audio', 'image', or 'other'")
            print("All existing records have been updated with their detected media types.")
            return 0
        else:
            logger.error("❌ Migration failed!")
            print("\n💥 Migration failed! Check the logs for details.")
            return 1
            
    except Exception as e:
        logger.error(f"Unexpected error during migration: {str(e)}")
        print(f"\n💥 Unexpected error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
