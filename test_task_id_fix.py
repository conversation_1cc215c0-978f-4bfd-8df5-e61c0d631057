#!/usr/bin/env python3
"""
Test script to verify task_id is always included in status responses
"""

import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_task_id_in_status_responses():
    """Test that task_id is always included in _check_status responses"""
    print("=" * 60)
    print("Testing task_id in Minimax _check_status responses")
    print("=" * 60)
    
    try:
        from videotrans.videogen._minimax import MinimaxVideoGen
        
        # Create a Minimax service instance
        service = MinimaxVideoGen(prompt="test prompt")
        test_task_id = "test_task_123456"
        
        print("\n1. Testing successful status response...")
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status": "Success",
            "file_id": "test_file_123"
        }
        
        with patch('requests.get', return_value=mock_response):
            result = service._check_status(test_task_id)
            
        # Verify task_id is included
        assert "task_id" in result, "task_id missing from successful response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "succeeded", f"Wrong status: {result['status']}"
        assert "video_url" in result, "video_url missing from successful response"
        
        print(f"✅ Success case: {result}")
        
        print("\n2. Testing processing status response...")
        
        # Mock processing response
        mock_response.json.return_value = {
            "status": "Processing"
        }
        
        with patch('requests.get', return_value=mock_response):
            result = service._check_status(test_task_id)
            
        assert "task_id" in result, "task_id missing from processing response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "running", f"Wrong status: {result['status']}"
        
        print(f"✅ Processing case: {result}")
        
        print("\n3. Testing failed status response...")
        
        # Mock failed response
        mock_response.json.return_value = {
            "status": "Fail",
            "error": "Generation failed"
        }
        
        with patch('requests.get', return_value=mock_response):
            result = service._check_status(test_task_id)
            
        assert "task_id" in result, "task_id missing from failed response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "failed", f"Wrong status: {result['status']}"
        assert "error" in result, "error missing from failed response"
        
        print(f"✅ Failed case: {result}")
        
        print("\n4. Testing HTTP error response...")
        
        # Mock HTTP error response
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        with patch('requests.get', return_value=mock_response):
            result = service._check_status(test_task_id)
            
        assert "task_id" in result, "task_id missing from HTTP error response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "failed", f"Wrong status: {result['status']}"
        assert "error" in result, "error missing from HTTP error response"
        
        print(f"✅ HTTP error case: {result}")
        
        print("\n5. Testing network error response...")
        
        # Mock network error
        from requests.exceptions import RequestException
        
        with patch('requests.get', side_effect=RequestException("Network timeout")):
            result = service._check_status(test_task_id)
            
        assert "task_id" in result, "task_id missing from network error response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "failed", f"Wrong status: {result['status']}"
        assert "Network error" in result["error"], "Network error not in error message"
        
        print(f"✅ Network error case: {result}")
        
        print("\n6. Testing queued status response...")
        
        # Mock queued response
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status": "Preparing"
        }
        
        with patch('requests.get', return_value=mock_response):
            result = service._check_status(test_task_id)
            
        assert "task_id" in result, "task_id missing from queued response"
        assert result["task_id"] == test_task_id, f"task_id mismatch: {result['task_id']} != {test_task_id}"
        assert result["status"] == "queued", f"Wrong status: {result['status']}"
        
        print(f"✅ Queued case: {result}")
        
        print("\n" + "=" * 60)
        print("🎉 ALL TASK_ID TESTS PASSED!")
        print("=" * 60)
        print("\nVerified scenarios:")
        print("✅ Successful completion with file_id")
        print("✅ Processing/running status")
        print("✅ Failed generation")
        print("✅ HTTP errors (4xx/5xx)")
        print("✅ Network errors")
        print("✅ Queued/preparing status")
        print("\n📋 task_id is now guaranteed to be included in all responses!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_task_id_in_status_responses()
    sys.exit(0 if success else 1)
