{"last_opendir": "", "cuda": false, "line_roles": {}, "only_video": false, "is_separate": false, "remove_noise": false, "target_dir": "", "source_language": "English", "target_language": "Portuguese", "subtitle_language": "chi", "translate_type": 8, "subtitle_type": 1, "tts_type": 8, "split_type": "all", "model_name": "large-v3", "recogn_type": 1, "voice_autorate": false, "voice_role": "nova", "voice_rate": "+0%", "video_autorate": true, "append_video": false, "deepl_authkey": "", "deepl_api": "", "deepl_gid": "", "deeplx_address": "", "deeplx_key": "", "libre_address": "", "libre_key": "", "ott_address": "", "tencent_SecretId": "", "tencent_SecretKey": "", "tencent_termlist": "", "ali_id": "", "ali_key": "", "baidu_appid": "", "baidu_miyue": "", "chatgpt_api": "https://api.openai.com/v1", "chatgpt_key": "", "chatgpt_max_token": "4096", "chatgpt_model": "gpt-4.1-nano-2025-04-14", "chatgpt_template": "", "chatgpt_temperature": "0.4", "chatgpt_top_p": "1.0", "claude_api": "", "claude_key": "", "claude_model": "claude-3-5-sonnet-latest", "claude_template": "", "azure_api": "", "azure_key": "", "azure_version": "2024-06-01", "azure_model": "gpt-4o", "azure_template": "", "gemini_key": "", "gemini_model": "gemini-2.0-flash", "gemini_template": "", "gemini_srtprompt": "", "localllm_api": "", "localllm_key": "", "localllm_model": "qwen:7b", "localllm_template": "", "localllm_max_token": "4096", "localllm_temperature": "0.7", "localllm_top_p": "1.0", "zhipu_key": "", "guiji_key": "", "free_template": "", "zijiehuoshan_key": "", "zijiehuoshan_model": "", "zijiehuoshan_template": "", "ai302_key": "", "ai302_model": "", "ai302_template": "", "trans_api_url": "", "trans_secret": "", "coquitts_role": "", "coquitts_key": "", "elevenlabstts_role": ["clone"], "elevenlabstts_key": "", "elevenlabstts_models": "eleven_flash_v2_5", "openaitts_api": "https://api.openai.com/v1", "openaitts_key": "", "openaitts_model": "gpt-4o-mini-tts", "openaitts_instructions": "", "openaitts_role": "alloy,ash,coral,echo,fable,onyx,nova,sage,shimmer", "kokoro_api": "", "openairecognapi_url": "", "openairecognapi_key": "", "openairecognapi_prompt": "", "openairecognapi_model": "whisper-1", "clone_api": "", "clone_voicelist": ["clone"], "zh_recogn_api": "", "recognapi_url": "", "recognapi_key": "", "stt_url": "", "stt_model": "tiny", "sense_url": "", "ttsapi_url": "", "ttsapi_voice_role": "", "ttsapi_extra": "pyvideotrans", "ttsapi_language_boost": "auto", "ttsapi_emotion": "happy", "ai302tts_key": "", "ai302tts_model": "", "ai302tts_role": "alloy,echo,fable,onyx,nova,shimmer", "azure_speech_region": "", "azure_speech_key": "", "gptsovits_url": "", "gptsovits_role": "", "gptsovits_isv2": true, "gptsovits_extra": "pyvideotrans", "cosyvoice_url": "", "cosyvoice_role": "", "fishtts_url": "", "fishtts_role": "", "f5tts_url": "", "f5tts_model": "", "f5tts_role": "", "f5tts_is_whisper": false, "doubao_appid": "", "doubao_access": "", "volcenginetts_appid": "", "volcenginetts_access": "", "volcenginetts_cluster": "", "chattts_api": "", "app_mode": "<PERSON><PERSON>z<PERSON>", "stt_source_language": 0, "stt_recogn_type": 0, "stt_model_name": "", "stt_remove_noise": false, "deepgram_apikey": "", "deepgram_utt": 200, "trans_translate_type": 0, "trans_source_language": 0, "trans_target_language": 1, "trans_out_format": 0, "dubb_source_language": 0, "dubb_tts_type": 0, "dubb_role": 0, "dubb_out_format": 0, "dubb_voice_autorate": false, "dubb_hecheng_rate": 0, "dubb_pitch_rate": 0, "dubb_volume_rate": 0, "f5tts_ttstype": "F5-TTS", "volume": "0%", "pitch": "0Hz", "back_audio": "", "clear_cache": false, "copysrt_rawvideo": false, "gcloud_credential_json": "/path/to/your/credentials.json", "gcloud_language_code": "pt-BR", "gcloud_voice_name": "pt-BR-Standard-A", "gcloud_audio_encoding": "MP3", "gcloud_ssml_gender": "SSML_VOICE_GENDER_UNSPECIFIED"}