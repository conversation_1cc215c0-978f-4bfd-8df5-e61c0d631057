#!/usr/bin/env python3
"""
Test script for Minimax video generation integration
"""

import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_minimax_integration():
    """Test Minimax integration without making actual API calls"""
    print("=" * 60)
    print("Testing Minimax Video Generation Integration")
    print("=" * 60)
    
    try:
        # Test 1: Import modules
        print("\n1. Testing module imports...")
        from videotrans.videogen import get_video_gen_service, VIDEO_GEN_TYPES, is_allow_videogen
        from videotrans.videogen._minimax import MinimaxVideoGen
        print("✅ All modules imported successfully")
        
        # Test 2: Check available types
        print("\n2. Testing available video generation types...")
        print(f"Available types: {VIDEO_GEN_TYPES}")
        assert 1 in VIDEO_GEN_TYPES, "Minimax type (1) not found"
        assert VIDEO_GEN_TYPES[1] == "Minimax", "Minimax type name incorrect"
        print("✅ Minimax type correctly registered")
        
        # Test 3: Create Minimax service
        print("\n3. Testing Minimax service creation...")
        minimax_service = get_video_gen_service(1, prompt="test prompt")
        assert isinstance(minimax_service, MinimaxVideoGen), "Wrong service type created"
        assert minimax_service.model == "MiniMax-Hailuo-02", "Wrong default model"
        assert minimax_service.api_base == "https://api.minimax.io/v1", "Wrong API base"
        print("✅ Minimax service created with correct defaults")
        
        # Test 4: Test custom parameters
        print("\n4. Testing custom parameters...")
        custom_service = get_video_gen_service(
            1, 
            prompt="custom prompt",
            duration=10,
            resolution="1080P",
            prompt_optimizer=False
        )
        assert custom_service.duration == 10, "Custom duration not set"
        assert custom_service.resolution == "1080P", "Custom resolution not set"
        assert custom_service.prompt_optimizer == False, "Custom prompt_optimizer not set"
        print("✅ Custom parameters correctly applied")
        
        # Test 5: Test model selection logic (simulate API endpoint logic)
        print("\n5. Testing model selection logic...")
        
        def simulate_model_selection(data):
            """Simulate the API endpoint model selection logic"""
            videogen_type = data.get('videogen_type', 0)
            model = data.get('model', '')
            
            if not model:
                if videogen_type == 0:  # BytePlus
                    model = 'seedance-1-0-lite-t2v-250428'
                elif videogen_type == 1:  # Minimax
                    model = 'MiniMax-Hailuo-02'
            return model
        
        # Test cases
        test_cases = [
            # Client specifies Minimax model
            ({"videogen_type": 1, "model": "T2V-01-Director"}, "T2V-01-Director"),
            # Client doesn't specify model for Minimax
            ({"videogen_type": 1}, "MiniMax-Hailuo-02"),
            # Client specifies BytePlus model
            ({"videogen_type": 0, "model": "custom-model"}, "custom-model"),
            # Client doesn't specify model for BytePlus
            ({"videogen_type": 0}, "seedance-1-0-lite-t2v-250428"),
        ]
        
        for data, expected in test_cases:
            result = simulate_model_selection(data)
            assert result == expected, f"Model selection failed: {data} -> {result} (expected {expected})"
        
        print("✅ Model selection logic working correctly")
        
        # Test 6: Test configuration validation
        print("\n6. Testing configuration validation...")
        try:
            from videotrans.configure.enhanced_config import ConfigValidator
            validator = ConfigValidator()
            
            # Test valid Minimax API key format
            assert validator.validate_api_key("sk-1234567890123456789012345", "minimax"), "Valid Minimax key rejected"
            # Test invalid Minimax API key format
            assert not validator.validate_api_key("short", "minimax"), "Invalid Minimax key accepted"
            
            print("✅ Configuration validation working correctly")
        except Exception as e:
            print(f"⚠️  Configuration validation test skipped: {e}")
        
        # Test 7: Test API request simulation
        print("\n7. Testing API request simulation...")
        
        def simulate_api_request(request_data):
            """Simulate the /generate_video API endpoint logic"""
            # Extract parameters
            prompt = request_data.get('prompt', '').strip()
            videogen_type = int(request_data.get('videogen_type', 0))
            model = request_data.get('model', '')
            
            # Model selection logic
            if not model:
                if videogen_type == 1:  # Minimax
                    model = 'MiniMax-Hailuo-02'
            
            # Create service configuration
            cfg = {
                'prompt': prompt,
                'videogen_type': videogen_type,
                'model': model,
            }
            
            # Add Minimax-specific parameters
            if videogen_type == 1:
                cfg['duration'] = int(request_data.get('duration', 6))
                cfg['resolution'] = request_data.get('resolution', '768P')
                cfg['prompt_optimizer'] = bool(request_data.get('prompt_optimizer', True))
            
            return cfg
        
        # Test Minimax request
        minimax_request = {
            "prompt": "A woman drinking coffee",
            "videogen_type": 1,
            "model": "T2V-01-Director",
            "duration": 10,
            "resolution": "1080P"
        }
        
        result = simulate_api_request(minimax_request)
        expected_keys = ['prompt', 'videogen_type', 'model', 'duration', 'resolution', 'prompt_optimizer']
        
        for key in expected_keys:
            assert key in result, f"Missing key in result: {key}"
        
        assert result['model'] == "T2V-01-Director", "Wrong model in result"
        assert result['duration'] == 10, "Wrong duration in result"
        assert result['resolution'] == "1080P", "Wrong resolution in result"
        
        print("✅ API request simulation working correctly")
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("=" * 60)
        print("\nMinimax video generation provider is ready to use!")
        print("\nUsage examples:")
        print("\n1. With client-specified model:")
        print(json.dumps({
            "prompt": "A beautiful sunset over mountains",
            "videogen_type": 1,
            "model": "MiniMax-Hailuo-02",
            "duration": 6,
            "resolution": "1080P",
            "async_mode": True
        }, indent=2))
        
        print("\n2. With default model:")
        print(json.dumps({
            "prompt": "A cat playing with a ball",
            "videogen_type": 1,
            "duration": 10,
            "resolution": "768P",
            "async_mode": True
        }, indent=2))
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimax_integration()
    sys.exit(0 if success else 1)
