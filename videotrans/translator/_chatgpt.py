# -*- coding: utf-8 -*-
import re,math,time
from pathlib import Path
from typing import Union, List

import httpx,requests,json
from openai import OpenAI, APIConnectionError, APIError,RateLimitError

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config, ConfigValidator
from videotrans.configure.reloadable_service import ReloadableTranslator
from videotrans.translator._base import BaseTrans
from videotrans.util import tools


class ChatGPT(BaseTrans, ReloadableTranslator):

    def __init__(self, *args, **kwargs):
        # Initialize BaseTrans first
        BaseTrans.__init__(self, *args, **kwargs)

        # Initialize ReloadableTranslator with service name
        ReloadableTranslator.__init__(self, 'chatgpt')

        # Enhanced configuration access with validation
        self.trans_thread = int(enhanced_config.get_system_setting('aitrans_thread', 50))

        # Load and validate initial configuration
        self._load_and_validate_config()

        # 是srt则获取srt的提示词
        self._load_prompt()

        self._check_proxy()

    def _load_and_validate_config(self):
        """Load and validate configuration with real-time updates"""
        # Get API configuration with validation
        api_key = self.get_config_value('chatgpt_key', '')
        api_url = self.get_config_value('chatgpt_api', 'https://api.openai.com/v1')

        if not api_key:
            raise Exception('必须在翻译设置 - OpenAI ChatGPT 填写 SK' if config.defaulelang=='zh' else 'Please input your OpenAI API key')

        # Validate API key format
        if not ConfigValidator.validate_api_key(api_key, 'openai'):
            raise Exception('Invalid OpenAI API key format' if config.defaulelang!='zh' else 'OpenAI API密钥格式无效')

        self.api_url = self._get_url(api_url)

        # Get model configuration
        self.model_name = self.get_config_value('chatgpt_model', 'gpt-3.5-turbo')

    def _load_prompt(self):
        """Load prompt template"""
        template = self.get_config_value('chatgpt_template', '')
        if template:
            self.prompt = template.replace('{lang}', self.target_language_name)
        else:
            self.prompt = tools.get_prompt(ainame='chatgpt',is_srt=self.is_srt).replace('{lang}', self.target_language_name)

    def reload_config(self) -> bool:
        """Reload configuration in real-time"""
        try:
            old_api_key = getattr(self, 'api_key', '')
            old_model = getattr(self, 'model_name', '')

            # Reload configuration
            result = super().reload_config()

            if result:
                # Reload and validate configuration
                self._load_and_validate_config()
                self._load_prompt()

                print(f"ChatGPT configuration reloaded: model={self.model_name}")
                return True

            return False

        except Exception as e:
            print(f"Error reloading ChatGPT configuration: {e}")
            return False

    def llm_segment(self,words_all,inst=None):
        # 以2000个字或单词分成一批
        prompts_template=Path(config.ROOT_DIR+'/videotrans/recharge-llm.txt').read_text(encoding='utf-8')
        chunk_size=int(config.settings.get('llm_chunk_size',2000))
        
        
        def _send(words,batch_num=0):        
            prompts=prompts_template.replace('<INPUT></INPUT>','<INPUT>'+json.dumps(words,ensure_ascii=False)+'</INPUT>')
            

            message = [
                {
                    'role': 'user',
                    'content': prompts
                }
            ]
            #config.logger.info(f'{prompts=}')
            # Use enhanced configuration
            api_key = enhanced_config.get_api_key('chatgpt')
            max_tokens = enhanced_config.config_manager.get('chatgpt_max_token', 4096)

            model = OpenAI(api_key=api_key, base_url=self.api_url,
                           http_client=httpx.Client(proxy=self.proxies,timeout=7200))
            try:
                msg=f'第{batch_num}批次 LLM断句，每批次 {chunk_size} 个字或单词' if config.defaulelang=='zh' else f'Start sending {batch_num} batches of LLM segments, {chunk_size} words per batch'
                config.logger.info(msg)
                if inst:
                    inst.status_text=msg
                response = model.chat.completions.create(
                    model='gpt-4o-mini' if self.model_name.lower().find('gpt-3.5') > -1 else self.model_name,
                    timeout=7200,
                    max_tokens=max(int(max_tokens) if max_tokens else 4096, 4096),
                    messages=message
                )
                msg=f'第{batch_num}批次 LLM断句 完成' if config.defaulelang=='zh' else f'Ended  {batch_num} batches of LLM segments'
                config.logger.info(msg)
                if inst:
                    inst.status_text=msg
            except RateLimitError:
                config.logger.error(f'[chatGPT]第{batch_num}批次重新断句失败:429请求频繁，暂停10s后重试')
                time.sleep(10)
                return _send(words,batch_num=0)
            except APIConnectionError as e:
                config.logger.exception(e, exc_info=True)            
                raise Exception('无法连接到OpenAI服务，请尝试使用或更换代理' if config.defaulelang == 'zh' else 'Cannot connect to OpenAI service, please try using or changing proxy')
            except APIError as e:
                config.logger.exception(e,exc_info=True)
                raise
            except Exception as e:
                config.logger.exception(e,exc_info=True)
                raise

            if not hasattr(response,'choices'):
                config.logger.error(f'[chatGPT]第{batch_num}批次重新断句失败:{response=}')
                raise Exception(f"no choices:{response=}")
            if response.choices[0].finish_reason=='length':
                raise Exception(f"请增加最大输出token或降低LLM重新断句每批次字/单词数")
            if not response.choices[0].message.content:
                config.logger.error(f'[chatGPT]第{batch_num}批次重新断句失败:{response=}')
                raise Exception(f"no choices:{response=}")
                
            result = response.choices[0].message.content.strip()
            match = re.search(r'<DICT_LIST>(.*?)(</DICT_LIST>|$)',result, re.S|re.I)
            if not match:
                config.logger.error(f'[chatGPT]第{batch_num}批次重新断句失败:{result=}')
                raise Exception(f"ChatGPT 重新断句失败")
            llm_result=match.group(1).replace("\n","").strip()
            config.logger.info(f'第{batch_num}批次LLM断句结果:{llm_result}')
            sub_list=json.loads( re.sub(r'\,\]$',']', llm_result))
            return sub_list
        
        
        new_sublist=[]
        order_num=0
        for idx in range(0, len(words_all), chunk_size):
            order_num+=1        
            sub_list=_send(words_all[idx : idx + chunk_size],order_num)
            for i,s in enumerate(sub_list):
                tmp={}
                tmp['startraw']=tools.ms_to_time_string(ms=s["start"]*1000)
                tmp['endraw']=tools.ms_to_time_string(ms=s["end"]*1000)
                tmp['time'] = f"{tmp['startraw']} --> {tmp['endraw']}"
                tmp['text']=s['text'].strip()
                tmp['line']=i+1
                new_sublist.append(tmp)
        return new_sublist

        
    def _check_proxy(self):
        if re.search('localhost', self.api_url) or re.match(r'^https?://(\d+\.){3}\d+(:\d+)?', self.api_url):
            self.proxies=None
            return
        pro = self._set_proxy(type='set')
        if pro:
            self.proxies = pro

    def _get_url(self, url=""):
        if not url:
            return "https://api.openai.com/v1"
        if not url.startswith('http'):
            url = 'http://' + url
            # 删除末尾 /
        url = url.rstrip('/').lower()
        if url.find(".openai.com") > -1:
            return "https://api.openai.com/v1"
        if url.endswith('/v1'):
            return url
        # 存在 /v1/xx的，改为 /v1
        if url.endswith('/v1/chat/completions'):
            return re.sub(r'/v1.*$', '/v1', url)

        if re.match(r'^https?://[^/]+[a-zA-Z]+$',url):
            return url + "/v1"
        return url

    def _item_task(self, data: Union[List[str], str]) -> str:
        if self.refine3:
            return self._item_task_refine3(data)
        text="\n".join([i.strip() for i in data]) if isinstance(data,list) else data    
        message = [
            {
                'role': 'system',
                'content': "You are a translation assistant specializing in converting SRT subtitle content from one language to another while maintaining the original format and structure." if config.defaulelang != 'zh' else '您是一名翻译助理，专门负责将 SRT 字幕内容从一种语言转换为另一种语言，同时保持原始格式和结构。'},
            {
                'role': 'user',
                'content': self.prompt.replace('<INPUT></INPUT>',f'<INPUT>{text}</INPUT>')},
        ]

        config.logger.info(f"\n[chatGPT]发送请求数据:{message=}")

        # Use enhanced configuration with validation
        api_key = enhanced_config.get_api_key('chatgpt')
        max_tokens = int(enhanced_config.config_manager.get('chatgpt_max_token', 4096))
        temperature = float(enhanced_config.config_manager.get('chatgpt_temperature', 0.7))
        top_p = float(enhanced_config.config_manager.get('chatgpt_top_p', 1.0))

        model = OpenAI(api_key=api_key, base_url=self.api_url,
                       http_client=httpx.Client(proxy=self.proxies,timeout=7200))
        try:
            response = model.chat.completions.create(
                model='gpt-4o-mini' if self.model_name.lower().find('gpt-3.5') > -1 else self.model_name,
                timeout=7200,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                messages=message
            )
        except APIError as e:
            config.logger.exception(e,exc_info=True)
            raise
        except Exception as e:
            config.logger.exception(e,exc_info=True)
            raise
        config.logger.info(f'[chatGPT]响应:{response=}')
        result=""
        if response.choices:
            result = response.choices[0].message.content.strip()
        else:
            config.logger.error(f'[chatGPT]请求失败:{response=}')
            raise Exception(f"no choices:{response=}")
        
        match = re.search(r'<TRANSLATE_TEXT>(.*?)</TRANSLATE_TEXT>', re.sub(r'<think>(.*?)</think>','',result,re.S|re.I),re.S|re.I)
        if match:
            return match.group(1)
        return result.strip()


    def _item_task_refine3(self, data: Union[List[str], str]) -> str:
        prompt=self._refine3_prompt()
        text="\n".join([i.strip() for i in data]) if isinstance(data,list) else data
        prompt=prompt.replace('{lang}',self.target_language_name).replace('<INPUT></INPUT>',f'<INPUT>{text}</INPUT>')

        message = [
            {
                'role': 'system',
                'content':  "You are a translation assistant specializing in converting SRT subtitle content from one language to another while maintaining the original format and structure." if config.defaulelang != 'zh' else '您是一名翻译助理，专门负责将 SRT 字幕内容从一种语言转换为另一种语言，同时保持原始格式和结构。'},
            {
                'role': 'user',
                'content': prompt},
        ]

        config.logger.info(f"\n[chatGPT]发送请求数据:{message=}")

        # Use enhanced configuration
        api_key = enhanced_config.get_api_key('chatgpt')

        model = OpenAI(api_key=api_key, base_url=self.api_url,
                       http_client=httpx.Client(proxy=self.proxies,timeout=7200))
        try:
            response = model.chat.completions.create(
                model=self.model_name,
                timeout=7200,
                max_tokens=4096,
                messages=message
            )
        except APIConnectionError as e:
            config.logger.error(f'[chatGPT]:{e=}')
            raise
        config.logger.info(f'[chatGPT]响应:{response=}')

        if response.choices:
            result = response.choices[0].message.content.strip()
        else:
            config.logger.error(f'[chatGPT]请求失败:{response=}')
            raise Exception(f"no choices:{response=}")

        match = re.search(r'<step3_refined_translation>(.*?)</step3_refined_translation>', re.sub(r'<think>(.*?)</think>','',result,re.S|re.I), re.S|re.I)
        if not match:
            match = re.search(r'<TRANSLATE_TEXT>(.*?)</TRANSLATE_TEXT>', re.sub(r'<think>(.*?)</think>','',result,re.S|re.I), re.S|re.I)
        if match:
            return match.group(1)
        return result.strip()
        