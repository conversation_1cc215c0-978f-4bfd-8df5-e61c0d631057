import os
import time
from pathlib import Path
from typing import Dict, Any, Optional

from videotrans.configure import config
from videotrans.task._base import BaseTask
from videotrans.util import tools
from videotrans import videogen


class VideoGenerationTask(BaseTask):
    """Video generation task for queue processing"""

    def __init__(self, cfg: Dict[str, Any] = None, obj: Dict = None, user_id: str = None):
        super().__init__(cfg=cfg, obj=obj, user_id=user_id)
        # self.cfg is now set by the parent class

        # Required parameters
        self.prompt = self.cfg.get('prompt', '')
        self.image_path = self.cfg.get('image_path', '')
        self.videogen_type = self.cfg.get('videogen_type', 0)
        self.model = self.cfg.get('model', 'seedance-1-0-lite-t2v')

        # Task management (uuid is already set by parent class)
        self.cache_folder = self.cfg.get('cache_folder', config.TEMP_DIR)
        self.target_dir = self.cfg.get('target_dir', '')

        # Video generation service
        self.videogen_service = None
        self.video_path = ""
        self.error = ""

        # Ensure directories exist
        Path(self.cache_folder).mkdir(parents=True, exist_ok=True)
        Path(self.target_dir).mkdir(parents=True, exist_ok=True)

    def run(self):
        """Execute video generation task"""
        try:
            # Update progress: Starting
            self._update_progress(10.0, "Initializing video generation service...", "initialization")

            # Create video generation service
            self.videogen_service = videogen.get_video_gen_service(
                videogen_type=self.videogen_type,
                prompt=self.prompt,
                image_path=self.image_path,
                cache_folder=self.cache_folder,
                uuid_str=self.uuid,
                inst=self
            )

            # Set model if specified
            if hasattr(self.videogen_service, 'model'):
                self.videogen_service.model = self.model

            # Update progress: Service created
            self._update_progress(20.0, "Starting video generation...", "generation")

            # Generate video (synchronous mode for queue processing)
            # We'll monitor progress during generation
            self.video_path = self._generate_video_with_progress_tracking()

            if self.video_path and Path(self.video_path).exists():
                # Update progress: Video generated
                self._update_progress(80.0, "Processing generated video...", "processing")

                # Move video to target directory
                final_path = self._move_to_target_dir()

                # Update progress: File moved
                self._update_progress(85.0, "Generating thumbnail...", "thumbnail")

                # Generate thumbnail
                thumbnail_path = self._generate_thumbnail(final_path)

                # Update progress: Thumbnail generated
                self._update_progress(90.0, "Creating output files...", "finalizing")

                # Create info file
                info_file_path = self._create_info_file(final_path, thumbnail_path)

                # Update output files in database with full URLs
                output_files = self._generate_output_file_urls(final_path, info_file_path, thumbnail_path)
                self._update_output_files(output_files)

                # Complete task
                self._update_progress(100.0, f"Video generation completed: {Path(final_path).name}", "completed")
                self._signal(f"Video generation completed: {final_path}")

                return True
            else:
                self.error = "Video generation failed - no output file"

                # Update database task status for this failure case
                error_exception = Exception(self.error)
                self._handle_error(error_exception, "video_generation")

                return False

        except Exception as e:
            self.error = str(e)
            config.logger.exception(e, exc_info=True)

            # Update database task status using the base class error handler
            self._handle_error(e, "video_generation")

            return False

    def _generate_video_with_progress_tracking(self) -> str:
        """Generate video with progress tracking"""
        import threading
        import time

        # Start video generation in a separate thread
        video_path = None
        error = None
        generation_complete = threading.Event()

        def generate():
            nonlocal video_path, error
            try:
                video_path = self.videogen_service.generate_video(async_mode=False)
                generation_complete.set()
            except Exception as e:
                error = e
                generation_complete.set()

        # Start generation thread
        gen_thread = threading.Thread(target=generate)
        gen_thread.daemon = True
        gen_thread.start()

        # Monitor progress while generation is running
        last_progress = 20.0
        while not generation_complete.is_set():
            if self.videogen_service:
                service_status = self.videogen_service.get_status()
                service_progress = service_status.get("progress", 0)

                # Map service progress (0-100) to our progress range (20-80)
                if service_progress > 0:
                    mapped_progress = 20.0 + (service_progress * 0.6)  # 20% to 80%
                    if mapped_progress > last_progress:
                        last_progress = mapped_progress
                        status_text = service_status.get("status", "running")
                        self._update_progress(mapped_progress, f"Video generation {status_text}: {service_progress}%", "generation")

            # Wait before next check
            generation_complete.wait(5)  # Check every 5 seconds

        # Wait for thread to complete
        gen_thread.join(timeout=10)

        if error:
            raise error

        return video_path

    def _generate_thumbnail(self, video_path: str) -> Optional[str]:
        """Generate thumbnail from video file"""
        try:
            # Use the thumbnail generation function from tools

            # Generate thumbnail filename
            video_name = Path(video_path).stem
            thumbnail_filename = f"{video_name}_thumb.jpg"
            thumbnail_path = os.path.join(self.target_dir, thumbnail_filename)

            # Generate thumbnail using existing function
            if tools.generate_video_thumbnail(video_path, thumbnail_path):
                config.logger.info(f"Thumbnail generated successfully: {thumbnail_path}")
                return thumbnail_path
            else:
                config.logger.warning(f"Thumbnail generation failed for: {video_path}")
                return None

        except Exception as e:
            config.logger.warning(f"Error generating thumbnail for {video_path}: {str(e)}")
            return None

    def _generate_output_file_urls(self, video_path: str, info_file_path: str = None, thumbnail_path: str = None) -> list:
        """Generate full URLs for output files"""
        try:
            from videotrans.configure.enhanced_config import enhanced_config

            # Get base URL from configuration (same method used in api.py)
            base_url = enhanced_config.get_system_setting('server_host', 'http://127.0.0.1:9011')

            # Extract the relative path from target_dir
            # target_dir format: {ROOT_DIR}/apidata/{uuid}
            # We need to extract the part after ROOT_DIR for the URL
            import os
            from videotrans.configure import config

            # Get the relative path from ROOT_DIR
            relative_target_dir = os.path.relpath(self.target_dir, config.ROOT_DIR)

            output_urls = []

            # Add video file URL
            if video_path and Path(video_path).exists():
                video_filename = Path(video_path).name
                video_url = f"{base_url}/{relative_target_dir}/{video_filename}"
                output_urls.append(video_url)

            # Add info file URL if it exists
            if info_file_path and Path(info_file_path).exists():
                info_filename = Path(info_file_path).name
                info_url = f"{base_url}/{relative_target_dir}/{info_filename}"
                output_urls.append(info_url)

            # Add thumbnail URL if it exists
            if thumbnail_path and Path(thumbnail_path).exists():
                thumbnail_filename = Path(thumbnail_path).name
                thumbnail_url = f"{base_url}/{relative_target_dir}/{thumbnail_filename}"
                output_urls.append(thumbnail_url)

            return output_urls

        except Exception as e:
            config.logger.warning(f"Failed to generate output file URLs: {e}")
            # Fallback to filenames only
            output_files = []
            if video_path:
                output_files.append(Path(video_path).name)
            if info_file_path:
                output_files.append(Path(info_file_path).name)
            if thumbnail_path:
                output_files.append(Path(thumbnail_path).name)
            return output_files

    def _move_to_target_dir(self) -> str:
        """Move generated video to target directory"""
        try:
            if not self.video_path or not Path(self.video_path).exists():
                raise Exception("No video file to move")

            # Generate final filename
            timestamp = int(time.time())
            original_name = Path(self.video_path).name
            name_parts = original_name.rsplit('.', 1)
            if len(name_parts) == 2:
                base_name, ext = name_parts
                final_name = f"generated_video_{timestamp}.{ext}"
            else:
                final_name = f"generated_video_{timestamp}.mp4"

            final_path = os.path.join(self.target_dir, final_name)

            # Move file
            import shutil
            shutil.move(self.video_path, final_path)

            return final_path

        except Exception as e:
            config.logger.exception(e, exc_info=True)
            raise Exception(f"Failed to move video file: {str(e)}")

    def _create_info_file(self, video_path: str, thumbnail_path: str = None) -> str:
        """Create information file about the generated video"""
        try:
            # Build generated files list
            generated_files = [
                f"- {Path(video_path).name} (Generated Video)",
                "- video_info.txt (This file)"
            ]

            # Add thumbnail to the list if it exists
            if thumbnail_path and Path(thumbnail_path).exists():
                generated_files.insert(1, f"- {Path(thumbnail_path).name} (Video Thumbnail)")

            info_content = f"""Video Generation Task Completed
Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}
Task ID: {self.uuid}
Service: {videogen.VIDEO_GEN_TYPES.get(self.videogen_type, 'Unknown')}
Model: {self.model}
Prompt: {self.prompt}
Image Input: {self.image_path if self.image_path else 'None'}
Output Video: {Path(video_path).name}
{f'Thumbnail: {Path(thumbnail_path).name}' if thumbnail_path and Path(thumbnail_path).exists() else ''}

Generated Files:
{chr(10).join(generated_files)}
"""

            info_file = os.path.join(self.target_dir, 'video_info.txt')
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(info_content)

            return info_file

        except Exception as e:
            config.logger.warning(f"Failed to create info file: {e}")
            return None

    def _signal(self, text: str, type: str = 'logs'):
        """Send progress signal"""
        try:
            tools.set_process(text=text, uuid=self.uuid, type=type)
        except Exception as e:
            config.logger.warning(f"Failed to send signal: {e}")

        # Also log
        config.logger.info(f"[VideoGen {self.uuid}] {text}")

        # Update instance status if available
        if hasattr(self, 'status_text'):
            self.status_text = text

    def stop(self):
        """Stop video generation task"""
        try:
            if self.videogen_service:
                self.videogen_service.cancel()
            self._signal("Video generation task stopped")
        except Exception as e:
            config.logger.warning(f"Error stopping video generation: {e}")

    def get_progress(self) -> Dict[str, Any]:
        """Get current progress information"""
        progress_info = {
            "uuid": self.uuid,
            "type": "video_generation",
            "status": "running",
            "progress": 0,
            "error": self.error
        }

        if self.videogen_service:
            service_status = self.videogen_service.get_status()
            progress_info.update({
                "status": service_status.get("status", "running"),
                "progress": service_status.get("progress", 0),
                "videogen_task_id": service_status.get("task_id", ""),
                "video_url": service_status.get("video_url", "")
            })

        return progress_info
