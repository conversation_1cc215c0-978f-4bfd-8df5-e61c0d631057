import re
import shutil
import uuid as uuid_lib
from pathlib import Path
from typing import Dict, Optional

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config
from videotrans.configure._base import BaseCon
from videotrans.util import tools


class BaseTask(BaseCon):

    def __init__(self, cfg: Dict = None, obj: Dict = None, user_id: str = None):
        # 任务id
        super().__init__()
        # 配置信息
        self.cfg = cfg or {}
        if obj:
            self.cfg.update(obj)

        # User context for database operations
        self.user_id = user_id

        # 名字规范化处理后，应该删除的
        self.shound_del_name = None

        # Initialize or get UUID
        if "uuid" in self.cfg and self.cfg['uuid']:
            self.uuid = self.cfg['uuid']
        else:
            self.uuid = str(uuid_lib.uuid4())
            self.cfg['uuid'] = self.uuid

        # 进度
        self.precent = 1
        self.status_text = config.transobj['ing']
        # 存储处理好待配音信息
        self.queue_tts = []
        # 本次任务结束标识
        self.hasend = False

        # Database integration
        self.task_manager = None
        self._db_task_created = False
        self._current_stage = None

        # Initialize database integration if user_id is provided
        if self.user_id:
            self._init_database_integration()

        # 预处理，prepare 全部需要
        self.shound_del = False
        # 是否需要语音识别
        self.shoud_recogn = False
        # 是否需要字幕翻译
        self.shoud_trans = False
        # 是否需要配音
        self.shoud_dubbing = False
        # 是否需要人声分离
        self.shoud_separate = False
        # 是否需要嵌入配音或字幕
        self.shoud_hebing = False
        # 最后一步hebing move_emd 全部需要

    def _init_database_integration(self):
        """Initialize database integration for task persistence"""
        try:
            from videotrans.database.task_manager import task_manager
            self.task_manager = task_manager

            # Create database record for this task
            task_type = self.__class__.__name__
            task_name = self.cfg.get('name', f'{task_type}-{self.uuid[:8]}')

            # Check if task already exists in database
            existing_task = self.task_manager.get_task(self.user_id, self.uuid)
            if not existing_task:
                # Create new task record
                uuid = self.task_manager.create_task(
                    user_id=self.user_id,
                    task_type=task_type,
                    name=task_name,
                    config=self.cfg
                )
                self._db_task_created = True
                self.uuid = uuid
                self._log_event("Task created in database", "info")
            else:
                self._db_task_created = True
                self.uuid = existing_task.get('uuid')
                self._log_event("Task found in database", "info")

        except Exception as e:
            config.logger.error(f"Failed to initialize database integration: {str(e)}")
            # Continue without database integration
            self.task_manager = None

    def _update_progress(self, progress: float, text: str = None, stage: str = None):
        """Update task progress in both memory and database"""
        # Update local progress
        self.precent = progress
        if text:
            self.status_text = text

        # Update current stage
        if stage:
            self._current_stage = stage

        # Update database if available
        if self.task_manager and self.user_id and self._db_task_created:
            try:
                self.task_manager.update_task_progress(
                    user_id=self.user_id,
                    task_uuid=self.uuid,
                    progress=progress,
                    status_text=text,
                    stage=stage
                )
            except Exception as e:
                config.logger.error(f"Failed to update task progress in database: {str(e)}")

        # Send signal for existing progress reporting
        if text:
            self._signal(text=text, type='logs')

    def _update_status(self, status: str, error_message: str = None):
        """Update task status in database"""
        if self.task_manager and self.user_id and self._db_task_created:
            try:
                self.task_manager.update_task_status(
                    user_id=self.user_id,
                    task_uuid=self.uuid,
                    status=status,
                    error_message=error_message
                )
                self._log_event(f"Task status updated to: {status}", "info")
            except Exception as e:
                config.logger.error(f"Failed to update task status in database: {str(e)}")

    def _log_event(self, message: str, level: str = 'info', stage: str = None):
        """Log task event to database"""
        if self.task_manager and self.user_id and self._db_task_created:
            try:
                self.task_manager.log_task_event(
                    user_id=self.user_id,
                    task_uuid=self.uuid,
                    message=message,
                    level=level,
                    stage=stage or self._current_stage
                )
            except Exception as e:
                config.logger.error(f"Failed to log task event to database: {str(e)}")

    def _update_output_files(self, output_files: list):
        """Update task output files in database"""
        if self.task_manager and self.user_id and self._db_task_created:
            try:
                self.task_manager.update_task_output_files(
                    user_id=self.user_id,
                    task_uuid=self.uuid,
                    output_files=output_files
                )
                self._log_event(f"Updated output files: {len(output_files)} files", "info")
            except Exception as e:
                config.logger.error(f"Failed to update task output files in database: {str(e)}")

    # 预先处理，例如从视频中拆分音频、人声背景分离、转码等
    def prepare(self):
        """Prepare stage - override in subclasses"""
        self._current_stage = 'prepare'
        self._update_status('running')
        self._log_event("Starting prepare stage", "info", "prepare")
        # Subclasses should call super().prepare() and then implement their logic


    # 语音识别创建原始语言字幕
    def recogn(self):
        """Recognition stage - override in subclasses"""
        self._current_stage = 'recogn'
        self._log_event("Starting recognition stage", "info", "recogn")
        # Subclasses should call super().recogn() and then implement their logic

    # 将原始语言字幕翻译到目标语言字幕
    def trans(self):
        """Translation stage - override in subclasses"""
        self._current_stage = 'trans'
        self._log_event("Starting translation stage", "info", "trans")
        # Subclasses should call super().trans() and then implement their logic

    # 根据 queue_tts 进行配音
    def dubbing(self):
        """Dubbing stage - override in subclasses"""
        self._current_stage = 'dubbing'
        self._log_event("Starting dubbing stage", "info", "dubbing")
        # Subclasses should call super().dubbing() and then implement their logic

    # 配音加速、视频慢速对齐
    def align(self):
        """Alignment stage - override in subclasses"""
        self._current_stage = 'align'
        self._log_event("Starting alignment stage", "info", "align")
        # Subclasses should call super().align() and then implement their logic

    # 视频、音频、字幕合并生成结果文件
    def assembling(self):
        """Assembly stage - override in subclasses"""
        self._current_stage = 'assembling'
        self._log_event("Starting assembly stage", "info", "assembling")
        # Subclasses should call super().assembling() and then implement their logic

    # 删除临时文件，移动或复制，发送成功消息
    def task_done(self):
        """Task completion - override in subclasses"""
        self._current_stage = 'done'
        self._update_status('completed')
        self._log_event("Task completed successfully", "info", "done")
        # Subclasses should call super().task_done() and then implement their logic

    def _signal(self, **kwargs):
        """Override _signal to include database logging"""
        # Call parent _signal for existing functionality
        super()._signal(**kwargs)

        # Log to database if available
        if self.task_manager and self.user_id and self._db_task_created:
            message = kwargs.get('text', '')
            log_type = kwargs.get('type', 'logs')

            # Map signal types to log levels
            level_mapping = {
                'error': 'error',
                'logs': 'info',
                'succeed': 'info',
                'end': 'info',
                'stop': 'warning'
            }

            level = level_mapping.get(log_type, 'info')

            if message:
                self._log_event(message, level)

            # Update task status based on signal type
            if log_type == 'error':
                self._update_status('failed', message)
            elif log_type in ['succeed', 'end']:
                self._update_status('completed')
            elif log_type == 'stop':
                self._update_status('stopped')

    # 字幕是否存在并且有效
    def _srt_vail(self, file):
        if not file:
            return False
        if not tools.vail_file(file):
            return False
        try:
            tools.get_subtitle_from_srt(file)
        except Exception:
            Path(file).unlink(missing_ok=True)
            return False
        return True

    # 删掉尺寸为0的无效文件
    def _unlink_size0(self, file):
        if not file:
            return
        p = Path(file)
        if p.exists() and p.stat().st_size == 0:
            p.unlink(missing_ok=True)

    # 保存字幕文件 到目标文件夹
    def _save_srt_target(self, srtstr, file):
        # 是字幕列表形式，重新组装
        try:
            tools.save_srt(srtstr, file)
        except Exception as e:
            raise
        self._signal(text=Path(file).read_text(encoding='utf-8'), type='replace_subtitle')
        return True


    def _check_target_sub(self,source_srt_list,target_srt_list):
        import re,copy

        if len(source_srt_list)==1 or len(target_srt_list)==1:
            target_srt_list[0]['line']=1
            return target_srt_list[:1]
        source_len=len(source_srt_list)
        target_len=len(target_srt_list)
        config.logger.info(f'{source_srt_list=}')
        config.logger.info(f'{target_srt_list=}')
        for i,it in enumerate(source_srt_list):
            tmp=copy.deepcopy(it)
            if i>target_len-1:
                # 超出目标字幕长度
                tmp['text']='  '
                print(f'#1 {i=}')
            elif re.sub(r'\D','',it['time']) == re.sub(r'\D','',target_srt_list[i]['time']):
                # 正常时间码相等
                tmp['text']=target_srt_list[i]['text']
                print(f'#2 {i=}')
            elif i==0 and source_srt_list[1]['time']==target_srt_list[1]['time']:
                # 下一行时间码相同
                tmp['text']=target_srt_list[i]['text']
                print(f'#3 {i=}')
            elif i==source_len-1 and source_srt_list[i-1]['time']==target_srt_list[i-1]['time']:
                # 上一行时间码相同
                tmp['text']=target_srt_list[i]['text']
                print(f'#4 {i=}')
            elif i>0 and i<source_len-1 and target_len>i+1 and  source_srt_list[i-1]['time']==target_srt_list[i-1]['time'] and source_srt_list[i+1]['time']==target_srt_list[i+1]['time']:
                # 上下两行时间码相同
                tmp['text']=target_srt_list[i]['text']
                print(f'#5 {i=}')
            else:
                print(f'#6 {i=}')
                # 其他情况清空目标字幕文字
                tmp['text']='  '
            if i > len(target_srt_list)-1:
                target_srt_list.append(tmp)
            else:
                target_srt_list[i]=tmp
        config.logger.info(f'chulihou,{target_srt_list=}')
        return target_srt_list

    # 完整流程判断是否需退出，子功能需重写
    def _exit(self):
        if config.exit_soft or config.current_status != 'ing':
            return True
        return False

    def _handle_error(self, error: Exception, stage: str = None):
        """Handle task errors with database logging"""
        error_message = str(error)
        self.hasend = True

        # Log error to database
        if self.task_manager and self.user_id and self._db_task_created:
            self._log_event(f"Error in {stage or self._current_stage}: {error_message}", "error", stage)
            self._update_status('failed', error_message)

        # Send error signal for existing error handling
        self._signal(text=error_message, type='error')

        # Send notification if configured
        if hasattr(self.cfg, 'get') and self.cfg.get('basename'):
            tools.send_notification(error_message, f'{self.cfg["basename"]}')

    @classmethod
    def create_with_user_context(cls, cfg: Dict = None, obj: Dict = None, user_id: str = None):
        """Factory method to create task with user context"""
        return cls(cfg=cfg, obj=obj, user_id=user_id)

    def get_database_task_info(self) -> Optional[Dict]:
        """Get task information from database"""
        if self.task_manager and self.user_id and self._db_task_created:
            try:
                return self.task_manager.get_task(self.user_id, self.uuid)
            except Exception as e:
                config.logger.error(f"Failed to get task info from database: {str(e)}")
        return None
