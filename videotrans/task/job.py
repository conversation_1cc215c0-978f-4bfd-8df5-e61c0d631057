import time
from threading import Thread

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config
from videotrans.task._base import BaseTask
from videotrans.util.tools import set_process


# 当前 uuid 是否已停止
def task_is_stop(uuid) -> bool:
    if uuid in config.stoped_uuid_set:
        return True
    return False


# 预处理线程，所有任务均需要执行，也是入口
"""
prepare_queue
regcon_queue
trans_queue
dubb_queue
align_queue
assemb_queue
videogen_queue
"""


class WorkerPrepare(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return
            if len(config.prepare_queue) < 1:
                time.sleep(0.5)
                continue
            try:
                trk: BaseTask = config.prepare_queue.pop(0)
            except:
                continue
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during prepare queue", "warning", "prepare")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting prepare stage from worker thread", "info", "prepare")

                trk.prepare()

                # Log stage completion and next queue assignment
                next_queue = None
                if trk.shoud_recogn:
                    config.regcon_queue.append(trk)
                    next_queue = "recognition"
                elif trk.shoud_trans:
                    config.trans_queue.append(trk)
                    next_queue = "translation"
                elif trk.shoud_dubbing:
                    config.dubb_queue.append(trk)
                    next_queue = "dubbing"
                else:
                    config.assemb_queue.append(trk)
                    next_queue = "assembly"

                # Log queue transition to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event(f"Prepare stage completed, moved to {next_queue} queue", "info", "prepare")

            except Exception as e:
                config.logger.exception(e, exc_info=True)
                error_msg = f'{config.transobj["yuchulichucuo"]}:' + str(e)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "prepare")
                else:
                    # Fallback to existing error handling
                    set_process(text=error_msg, type='error', uuid=trk.uuid)


class WorkerRegcon(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return

            if len(config.regcon_queue) < 1:
                time.sleep(0.5)
                continue
            trk = config.regcon_queue.pop(0)
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during recognition queue", "warning", "recogn")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting recognition stage from worker thread", "info", "recogn")

                trk.recogn()

                # Log stage completion and next queue assignment
                next_queue = None
                if trk.shoud_trans:
                    config.trans_queue.append(trk)
                    next_queue = "translation"
                elif trk.shoud_dubbing:
                    config.dubb_queue.append(trk)
                    next_queue = "dubbing"
                else:
                    config.assemb_queue.append(trk)
                    next_queue = "assembly"

                # Log queue transition to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event(f"Recognition stage completed, moved to {next_queue} queue", "info", "recogn")

            except Exception as e:
                config.logger.exception(e, exc_info=True)
                error_msg = f'{config.transobj["shibiechucuo"]}:' + str(e)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "recogn")
                else:
                    # Fallback to existing error handling
                    set_process(text=error_msg, type='error', uuid=trk.uuid)


class WorkerTrans(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return
            if len(config.trans_queue) < 1:
                time.sleep(0.5)
                continue
            trk = config.trans_queue.pop(0)
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during translation queue", "warning", "trans")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting translation stage from worker thread", "info", "trans")

                trk.trans()

                # Log stage completion and next queue assignment
                next_queue = None
                if trk.shoud_dubbing:
                    config.dubb_queue.append(trk)
                    next_queue = "dubbing"
                else:
                    config.assemb_queue.append(trk)
                    next_queue = "assembly"

                # Log queue transition to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event(f"Translation stage completed, moved to {next_queue} queue", "info", "trans")

            except Exception as e:
                msg = f'{config.transobj["fanyichucuo"]}:' + str(e)
                config.logger.exception(e, exc_info=True)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "trans")
                else:
                    # Fallback to existing error handling
                    set_process(text=msg, type='error', uuid=trk.uuid)


class WorkerDubb(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return
            if len(config.dubb_queue) < 1:
                time.sleep(0.5)
                continue
            trk = config.dubb_queue.pop(0)
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during dubbing queue", "warning", "dubbing")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting dubbing stage from worker thread", "info", "dubbing")

                trk.dubbing()
                config.align_queue.append(trk)

                # Log queue transition to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Dubbing stage completed, moved to alignment queue", "info", "dubbing")

            except Exception as e:
                msg = f'{config.transobj["peiyinchucuo"]}:' + str(e)
                config.logger.exception(e, exc_info=True)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "dubbing")
                else:
                    # Fallback to existing error handling
                    set_process(text=msg, type='error', uuid=trk.uuid)


class WorkerAlign(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return
            if len(config.align_queue) < 1:
                time.sleep(0.5)
                continue
            trk = config.align_queue.pop(0)
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during alignment queue", "warning", "align")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting alignment stage from worker thread", "info", "align")

                trk.align()

                # Log queue transition to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Alignment stage completed, moved to assembly queue", "info", "align")

            except Exception as e:
                msg = f'{config.transobj["peiyinchucuo"]}:' + str(e)
                config.logger.exception(e, exc_info=True)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "align")
                else:
                    # Fallback to existing error handling
                    set_process(text=msg, type='error', uuid=trk.uuid)
            else:
                config.assemb_queue.append(trk)


class WorkerAssemb(Thread):
    def __init__(self, *, parent=None):
        super().__init__()

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return
            if len(config.assemb_queue) < 1:
                time.sleep(0.5)
                continue
            trk = config.assemb_queue.pop(0)
            if task_is_stop(trk.uuid):
                # Log task stop to database if available
                if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._update_status('stopped')
                    trk._log_event("Task stopped by user during assembly queue", "warning", "assembling")
                continue
            try:
                # Log stage start to database
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Starting assembly stage from worker thread", "info", "assembling")

                trk.assembling()

                # Log task completion
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Assembly stage completed, finalizing task", "info", "assembling")

                trk.task_done()

            except Exception as e:
                msg = f'{config.transobj["hebingchucuo"]}:' + str(e)
                config.logger.exception(e, exc_info=True)

                # Enhanced error handling with database logging
                if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._handle_error(e, "assembling")
                else:
                    # Fallback to existing error handling
                    set_process(text=msg, type='error', uuid=trk.uuid)


class WorkerVideoGen(Thread):
    def __init__(self, *, parent=None):
        super().__init__()
        # Get concurrency setting
        self.max_workers = int(enhanced_config.get_system_setting('videogen_thread', 5))
        self.active_tasks = {}  # Track active tasks by UUID

    def run(self) -> None:
        while 1:
            if config.exit_soft:
                return

            # Check if we can process more tasks
            if len(self.active_tasks) >= self.max_workers:
                # Check if any tasks have completed
                completed = [uuid for uuid, task in self.active_tasks.items()
                            if not task.is_alive()]
                for uuid in completed:
                    del self.active_tasks[uuid]

            # Process new tasks if capacity available
            if len(self.active_tasks) < self.max_workers and len(config.videogen_queue) > 0:
                trk = config.videogen_queue.pop(0)
                if task_is_stop(trk.uuid):
                    # Log task stop to database if available
                    if hasattr(trk, '_update_status') and hasattr(trk, 'user_id') and trk.user_id:
                        trk._update_status('stopped')
                        trk._log_event("Task stopped by user during video generation queue", "warning", "videogen")
                    continue

                # Start task in a separate thread
                task_thread = Thread(target=self._process_task, args=(trk,))
                task_thread.daemon = True
                task_thread.start()
                self.active_tasks[trk.uuid] = task_thread

            time.sleep(0.5)

    def _process_task(self, trk):
        try:
            # Log stage start to database
            if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                trk._log_event("Starting video generation from worker thread", "info", "videogen")

            success = trk.run()
            if success:
                # Video generation completed successfully
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Video generation completed successfully", "info", "videogen")
                trk.task_done() if hasattr(trk, 'task_done') else None
            else:
                # Video generation failed, error already logged in trk.run()
                if hasattr(trk, '_log_event') and hasattr(trk, 'user_id') and trk.user_id:
                    trk._log_event("Video generation failed", "error", "videogen")
        except Exception as e:
            msg = f'Video generation error: {str(e)}'
            config.logger.exception(e, exc_info=True)

            # Enhanced error handling with database logging
            if hasattr(trk, '_handle_error') and hasattr(trk, 'user_id') and trk.user_id:
                trk._handle_error(e, "videogen")
            else:
                # Fallback to existing error handling
                set_process(text=msg, type='error', uuid=trk.uuid)


def start_thread(parent=None):
    WorkerPrepare(parent=parent).start()
    WorkerRegcon(parent=parent).start()
    WorkerTrans(parent=parent).start()
    WorkerDubb(parent=parent).start()
    WorkerAlign(parent=parent).start()
    WorkerAssemb(parent=parent).start()
    WorkerVideoGen(parent=parent).start()
