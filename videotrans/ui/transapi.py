# run again.  Do not edit this file unless you know what you are doing.
from PySide6 import QtWidgets, QtCore
from PySide6.QtCore import QMetaObject, QRect, QSize, Qt
from PySide6.QtWidgets import QLabel, QLineEdit, QPushButton, QSizePolicy

from videotrans.configure import config
from videotrans.util import tools


class Ui_transapiform(object):
    def setupUi(self, transapiform):
        self.has_done = False
        if not transapiform.objectName():
            transapiform.setObjectName("transapiform")
        transapiform.setWindowModality(Qt.NonModal)
        transapiform.resize(600, 400)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(transapiform.sizePolicy().hasHeightForWidth())
        transapiform.setSizePolicy(sizePolicy)
        transapiform.setMaximumSize(QSize(600, 400))
        v1=QtWidgets.QVBoxLayout(transapiform)

        h1=QtWidgets.QHBoxLayout()
        self.label = QLabel()
        self.label.setObjectName("label")
        self.label.setMinimumSize(QSize(150, 35))
        self.api_url = QLineEdit()
        self.api_url.setObjectName("api_url")
        self.api_url.setMinimumSize(QSize(0, 35))
        h1.addWidget(self.label)
        h1.addWidget(self.api_url)
        v1.addLayout(h1)


        h2=QtWidgets.QHBoxLayout()
        self.label_3 = QLabel()
        self.label_3.setObjectName("miyue")
        self.label_3.setMinimumSize(QSize(150, 35))

        self.miyue = QLineEdit()
        self.miyue.setObjectName("miyue")
        h2.addWidget(self.label_3)
        h2.addWidget(self.miyue)
        v1.addLayout(h2)

        self.tips = QtWidgets.QPlainTextEdit()
        self.tips.setObjectName("tips")
        self.tips.setReadOnly(True)
        v1.addWidget(self.tips)


        h3=QtWidgets.QHBoxLayout()
        self.save = QPushButton()
        self.save.setObjectName("save")
        self.save.setMinimumSize(QSize(0, 35))

        self.test = QPushButton(transapiform)
        self.test.setObjectName("test")
        self.test.setMinimumSize(QSize(0, 35))

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/transapi'))
        h3.addWidget(self.save)
        h3.addWidget(self.test)
        h3.addWidget(help_btn)

        v1.addLayout(h3)

        self.retranslateUi(transapiform)

        QMetaObject.connectSlotsByName(transapiform)

    # setupUi

    def retranslateUi(self, transapiform):
        if config.defaulelang == 'zh':
            tips = """
将以GET请求向填写的API地址发送application/www-urlencode数据：
text:需要翻译的文本/字符串
source_language:原始文字语言代码zh,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it/字符串
target_language:目标文字语言代码zh,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it/字符串
期待从接口返回json格式数据：
{
    code:0=成功时，>0的数字代表失败 , msg:ok=成功时，其他为失败原因, text:翻译后的文本
}
基于cloudflare和m2m100实现的免费翻译API见: github.com/jianchang512/translate-api
"""
        else:
            tips = """
The application/www-urlencode data will be sent as a GET request to the filled API address:
text:text/string to be translated
source_language:original text language code zh,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it/string
target_language:target_language code zh,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it/string
Expect data to be returned from the interface in json format:
{
    code:0=on success  numbers >0 represent failures, msg:ok=success  others are failure reasons,text:Translated text
}
Usage: github.com/jianchang512/translate-api
"""
        transapiform.setWindowTitle(
            "自定义翻译API/无编码能力勿使用该功能" if config.defaulelang == 'zh' else "Customizing the Translate API")
        self.label_3.setText("密钥" if config.defaulelang == 'zh' else "Secret")
        self.miyue.setPlaceholderText("填写密钥")

        self.tips.setPlainText(tips)

        self.save.setText("保存" if config.defaulelang == 'zh' else "Save")
        self.api_url.setPlaceholderText(
            "填写http开头的翻译api地址" if config.defaulelang == 'zh' else "Fill in the full address starting with http")
        self.label.setText("自定义翻译API" if config.defaulelang == 'zh' else "Translate API")
        self.test.setText("测试Api" if config.defaulelang == 'zh' else "Test API")
    # retranslateUi
