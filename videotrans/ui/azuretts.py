# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_azurettsform(object):
    def setupUi(self, azurettsform):
        self.has_done = False
        azurettsform.setObjectName("azurettsform")
        azurettsform.setWindowModality(QtCore.Qt.NonModal)
        azurettsform.resize(400, 250)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(azurettsform.sizePolicy().hasHeightForWidth())
        azurettsform.setSizePolicy(sizePolicy)
        azurettsform.setMaximumSize(QtCore.QSize(400, 250))


        self.verticalLayout = QtWidgets.QVBoxLayout(azurettsform)
        self.verticalLayout.setObjectName("verticalLayout")

        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(azurettsform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.speech_region = QtWidgets.QLineEdit(azurettsform)
        self.speech_region.setPlaceholderText(
            "如果不知怎么填请留空" if config.defaulelang == 'zh' else "Leave blank if you don't know how to fill in")
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.speech_region.sizePolicy().hasHeightForWidth())
        self.speech_region.setSizePolicy(sizePolicy)
        self.speech_region.setMinimumSize(QtCore.QSize(210, 35))
        self.speech_region.setObjectName("speech_region")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.speech_region)

        self.formLayout_22 = QtWidgets.QFormLayout()
        self.formLayout_22.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_22.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_22.setObjectName("formLayout_22")
        self.label22 = QtWidgets.QLabel(azurettsform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label22.sizePolicy().hasHeightForWidth())
        self.label22.setSizePolicy(sizePolicy)
        self.label22.setMinimumSize(QtCore.QSize(100, 35))
        self.label22.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label22.setObjectName("label22")
        self.formLayout_22.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label22)
        self.speech_key = QtWidgets.QLineEdit(azurettsform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.speech_key.sizePolicy().hasHeightForWidth())
        self.speech_key.setSizePolicy(sizePolicy)
        self.speech_key.setMinimumSize(QtCore.QSize(210, 35))
        self.speech_key.setObjectName("speech_key")
        self.formLayout_22.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.speech_key)

        self.verticalLayout.addLayout(self.formLayout_22)

        self.formLayout_222 = QtWidgets.QFormLayout()
        self.formLayout_222.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_222.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_222.setObjectName("formLayout_222")

        self.label222 = QtWidgets.QLabel(azurettsform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label222.sizePolicy().hasHeightForWidth())
        self.label222.setSizePolicy(sizePolicy)
        self.label222.setMinimumSize(QtCore.QSize(100, 35))
        self.label222.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label222.setObjectName("label222")
        self.formLayout_222.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label222)

        self.azuretts_area = QtWidgets.QComboBox(azurettsform)

        self.azuretts_area.setObjectName("azuretts_area")
        self.azuretts_area.addItems([
            "EastAsia",
            "SoutheastAsia",
            "AustraliaEast",
            "AustraliaSoutheast",
            "BrazilSouth",
            "CanadaCentral",
            "CanadaEast",
            "WestEurope",
            "NorthEurope",
            "CentralIndia",
            "SouthIndia",
            "WestIndia",
            "JapanEast",
            "JapanWest",
            "KoreaCentral",
            "KoreaSouth",
            "UkWest",
            "UkSouth",
            "NorthCentralUs",
            "EastUs",
            "WestUs2",
            "SouthCentralUs",
            "CentralUs",
            "EastUs2",
            "WestUs",
            "WestCentralUs",
            "GermanyCentral",
            "GermanyNortheast",
            "ChinaNorth",
            "ChinaEast",
            "USGovArizona",
            "USGovTexas",
            "USGovIowa",
            "USGovVirginia",
            "USDoDCentral",
            "USDoDEast"
        ])

        self.azuretts_area.setMinimumSize(QtCore.QSize(210, 40))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)

        self.azuretts_area.setSizePolicy(sizePolicy)

        self.formLayout_222.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.azuretts_area)

        self.verticalLayout.addLayout(self.formLayout_222)
        self.verticalLayout.addLayout(self.formLayout_2)

        self.save = QtWidgets.QPushButton(azurettsform)
        self.save.setMinimumSize(QtCore.QSize(0, 35))
        self.save.setObjectName("save")

        self.test = QtWidgets.QPushButton(azurettsform)
        self.test.setMinimumSize(QtCore.QSize(0, 35))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/azuretts'))

        hv = QtWidgets.QHBoxLayout()
        hv.addWidget(self.save)
        hv.addWidget(self.test)
        hv.addWidget(help_btn)

        self.verticalLayout.addLayout(hv)



        self.retranslateUi(azurettsform)
        QtCore.QMetaObject.connectSlotsByName(azurettsform)

    def retranslateUi(self, azurettsform):
        azurettsform.setWindowTitle("AzureTTS")
        self.label.setText("自定义区域URL" if config.defaulelang == 'zh' else "Your SPEECH REGION")
        self.label22.setText("授权key" if config.defaulelang == 'zh' else "SPEECH_KEY")
        self.label222.setText("选择区域" if config.defaulelang == 'zh' else "REGION")
        self.save.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试' if config.defaulelang == 'zh' else "Test")
