# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_azureform(object):
    def setupUi(self, azureform):
        self.has_done = False
        azureform.setObjectName("azureform")
        azureform.setWindowModality(QtCore.Qt.NonModal)
        azureform.resize(600, 580)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(azureform.sizePolicy().hasHeightForWidth())
        azureform.setSizePolicy(sizePolicy)
        azureform.setMaximumSize(QtCore.QSize(600, 580))

        v1=QtWidgets.QVBoxLayout(azureform)

        h1=QtWidgets.QHBoxLayout()
        self.label = QtWidgets.QLabel()
        self.label.setMinimumSize(QtCore.QSize(0, 35))
        self.label.setObjectName("label")
        self.azure_api = QtWidgets.QLineEdit()
        self.azure_api.setMinimumSize(QtCore.QSize(0, 35))
        self.azure_api.setObjectName("azure_api")
        h1.addWidget(self.label)
        h1.addWidget(self.azure_api)
        v1.addLayout(h1)

        h2=QtWidgets.QHBoxLayout()
        self.label_2 = QtWidgets.QLabel()
        self.label_2.setMinimumSize(QtCore.QSize(0, 35))
        self.label_2.setObjectName("label_2")

        self.azure_key = QtWidgets.QLineEdit()
        self.azure_key.setMinimumSize(QtCore.QSize(0, 35))
        self.azure_key.setObjectName("azure_key")
        h2.addWidget(self.label_2)
        h2.addWidget(self.azure_key)
        v1.addLayout(h2)

        h3=QtWidgets.QHBoxLayout()
        self.label_version = QtWidgets.QLabel()
        self.label_version.setObjectName("label_version")
        self.azure_version = QtWidgets.QComboBox()
        self.azure_version.setMinimumSize(QtCore.QSize(0, 35))
        self.azure_version.setObjectName("azure_version")
        self.azure_version.addItems([
            "2025-02-01-preview",
            "2025-01-01-preview",
            "2024-12-01-preview",
            "2024-09-01-preview",
            "2024-08-01-preview",
            "2024-07-01-preview",
            "2024-06-01",
            "2024-05-01-preview",
            "2024-04-01-preview",
            "2024-03-01-preview",
            "2024-02-01"
        ])
        h3.addWidget(self.label_version)
        h3.addWidget(self.azure_version)
        v1.addLayout(h3)

        h4=QtWidgets.QHBoxLayout()
        self.label_3 = QtWidgets.QLabel()
        self.label_3.setObjectName("label_3")
        self.azure_model = QtWidgets.QComboBox()
        self.azure_model.setMinimumSize(QtCore.QSize(0, 35))
        self.azure_model.setObjectName("azure_model")
        h4.addWidget(self.label_3)
        h4.addWidget(self.azure_model)
        v1.addLayout(h4)

        self.label_allmodels = QtWidgets.QLabel()
        self.label_allmodels.setGeometry(QtCore.QRect(10, 200, 571, 21))
        self.label_allmodels.setObjectName("label_allmodels")
        self.label_allmodels.setText(
            '填写所有可用模型，以英文逗号分隔，填写后可在上方选择' if config.defaulelang == 'zh' else 'Fill in all available models, separated by commas. After filling in, you can select them above')
        v1.addWidget(self.label_allmodels)

        self.edit_allmodels = QtWidgets.QPlainTextEdit()
        self.edit_allmodels.setGeometry(QtCore.QRect(10, 230, 571, 100))
        self.edit_allmodels.setObjectName("edit_allmodels")
        v1.addWidget(self.edit_allmodels)

        self.label_4 = QtWidgets.QLabel()
        self.label_4.setObjectName("label_4")
        self.azure_template = QtWidgets.QPlainTextEdit()
        self.azure_template.setObjectName("azure_template")
        v1.addWidget(self.label_4)
        v1.addWidget(self.azure_template)

        self.set_azure = QtWidgets.QPushButton()
        self.set_azure.setMinimumSize(QtCore.QSize(0, 35))
        self.set_azure.setObjectName("set_azure")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/azure'))

        h5=QtWidgets.QHBoxLayout()
        h5.addWidget(self.set_azure)
        h5.addWidget(help_btn)

        v1.addLayout(h5)

        self.retranslateUi(azureform)
        QtCore.QMetaObject.connectSlotsByName(azureform)

    def retranslateUi(self, azureform):
        azureform.setWindowTitle("AzureGPT")
        self.label_3.setText("Azure Model")
        self.label_version.setText("API Version")
        self.azure_template.setPlaceholderText("prompt")
        self.label_4.setText(
            "{lang}代表目标语言名称，不要删除。" if config.defaulelang == 'zh' else "{lang} represents the target language name, do not delete it.")
        self.set_azure.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.azure_api.setPlaceholderText('填写你的api地址' if config.defaulelang == 'zh' else "Api url")
        self.azure_key.setPlaceholderText('填写你的密钥' if config.defaulelang == 'zh' else "Secret key")
        self.label.setText("API URL")
        self.label_2.setText("Azure  Key ")
