# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_freeaiform(object):
    def setupUi(self, freeaiform):
        self.has_done = False
        freeaiform.setObjectName("freeaiform")
        freeaiform.setWindowModality(QtCore.Qt.NonModal)
        freeaiform.resize(600, 600)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(freeaiform.sizePolicy().hasHeightForWidth())
        freeaiform.setSizePolicy(sizePolicy)
        freeaiform.setMaximumSize(QtCore.QSize(600, 600))

        v1=QtWidgets.QVBoxLayout(freeaiform)
        h2=QtWidgets.QHBoxLayout()
        h3=QtWidgets.QHBoxLayout()
        h4=QtWidgets.QHBoxLayout()


        self.label_0 = QtWidgets.QPushButton()
        self.label_0.setGeometry(QtCore.QRect(10, 10, 580, 35))
        self.label_0.setStyleSheet("background-color: rgba(255, 255, 255,0);text-align:left")
        self.label_0.setText(
            '智谱AI的glm-4-flash模型及硅基流动的qwen2.5-7b模型可免费使用，在此填写api key')
        v1.addWidget(self.label_0)


        self.label_2 = QtWidgets.QLabel(freeaiform)
        self.label_2.setMinimumSize(QtCore.QSize(0, 35))
        self.label_2.setSizeIncrement(QtCore.QSize(0, 35))
        self.label_2.setObjectName("label_2")
        self.zhipu_key = QtWidgets.QLineEdit(freeaiform)
        self.zhipu_key.setMinimumSize(QtCore.QSize(0, 35))
        self.zhipu_key.setObjectName("zhipu_key")
        h2.addWidget(self.label_2)
        h2.addWidget(self.zhipu_key)
        v1.addLayout(h2)

        self.label_3 = QtWidgets.QLabel(freeaiform)
        self.label_3.setObjectName("label_3")
        self.guiji_key = QtWidgets.QLineEdit(freeaiform)
        self.guiji_key.setMinimumSize(QtCore.QSize(0, 35))
        self.guiji_key.setObjectName("guiji_key")
        h3.addWidget(self.label_3)
        h3.addWidget(self.guiji_key)
        v1.addLayout(h3)



        self.label_4 = QtWidgets.QLabel(freeaiform)
        self.label_4.setObjectName("label_4")

        self.template = QtWidgets.QPlainTextEdit(freeaiform)
        self.template.setObjectName("template")
        v1.addWidget(self.label_4)
        v1.addWidget(self.template)


        self.set = QtWidgets.QPushButton(freeaiform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/freeai'))

        h4.addWidget(self.set)
        h4.addWidget(help_btn)
        v1.addLayout(h4)

        self.retranslateUi(freeaiform)
        QtCore.QMetaObject.connectSlotsByName(freeaiform)

    def retranslateUi(self, freeaiform):
        freeaiform.setWindowTitle("GLM-4-flash / Qwen2.5-7b 免费AI模型")
        self.label_2.setText("智谱AI API Key")
        self.label_3.setText("硅基流动 API Key")
        self.template.setPlaceholderText("prompt")
        self.label_4.setText(
            "{lang}代表目标语言名称，不要删除。")
        self.set.setText('保存' )
        self.zhipu_key.setPlaceholderText("在此填写智谱AI的 API Key")
        self.guiji_key.setPlaceholderText("在此填写硅基流动的 API Key")
