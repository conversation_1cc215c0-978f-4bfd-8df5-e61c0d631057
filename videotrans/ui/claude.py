# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_claudeform(object):
    def setupUi(self, claudeform):
        self.has_done = False
        claudeform.setObjectName("claudeform")
        claudeform.setWindowModality(QtCore.Qt.NonModal)
        claudeform.resize(600, 600)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(claudeform.sizePolicy().hasHeightForWidth())
        claudeform.setSizePolicy(sizePolicy)
        claudeform.setMaximumSize(QtCore.QSize(600, 600))

        v1=QtWidgets.QVBoxLayout(claudeform)
        h1=QtWidgets.QHBoxLayout()
        h2=QtWidgets.QHBoxLayout()
        h3=QtWidgets.QHBoxLayout()
        h4=QtWidgets.QHBoxLayout()


        self.label_0 = QtWidgets.QPushButton()
        self.label_0.setGeometry(QtCore.QRect(10, 10, 580, 35))
        self.label_0.setStyleSheet("background-color: rgba(255, 255, 255,0);text-align:left")
        self.label_0.setText(
            'Claude API在此使用' if config.defaulelang == 'zh' else 'Claude API used here')
        v1.addWidget(self.label_0)


        self.label = QtWidgets.QLabel(claudeform)
        self.label.setMinimumSize(QtCore.QSize(0, 35))
        self.label.setObjectName("label")
        self.api = QtWidgets.QLineEdit(claudeform)
        self.api.setMinimumSize(QtCore.QSize(0, 35))
        self.api.setObjectName("api")
        h1.addWidget(self.label)
        h1.addWidget(self.api)
        v1.addLayout(h1)

        self.label_2 = QtWidgets.QLabel(claudeform)
        self.label_2.setMinimumSize(QtCore.QSize(0, 35))
        self.label_2.setSizeIncrement(QtCore.QSize(0, 35))
        self.label_2.setObjectName("label_2")
        self.key = QtWidgets.QLineEdit(claudeform)
        self.key.setMinimumSize(QtCore.QSize(0, 35))
        self.key.setObjectName("key")
        h2.addWidget(self.label_2)
        h2.addWidget(self.key)
        v1.addLayout(h2)

        self.label_3 = QtWidgets.QLabel(claudeform)
        self.label_3.setObjectName("label_3")
        self.model = QtWidgets.QComboBox(claudeform)
        self.model.setMinimumSize(QtCore.QSize(0, 35))
        self.model.setObjectName("model")
        h3.addWidget(self.label_3)
        h3.addWidget(self.model)
        v1.addLayout(h3)

        self.label_allmodels = QtWidgets.QLabel(claudeform)
        self.label_allmodels.setObjectName("label_allmodels")
        self.label_allmodels.setText(
            '填写所有可用模型，以英文逗号分隔，填写后可在上方选择' if config.defaulelang == 'zh' else 'Fill in all available models, separated by commas. After filling in, you can select them above')
        v1.addWidget(self.label_allmodels)

        self.edit_allmodels = QtWidgets.QPlainTextEdit(claudeform)
        self.edit_allmodels.setObjectName("edit_allmodels")
        v1.addWidget(self.edit_allmodels)

        self.label_4 = QtWidgets.QLabel(claudeform)
        self.label_4.setObjectName("label_4")

        self.template = QtWidgets.QPlainTextEdit(claudeform)
        self.template.setObjectName("template")
        v1.addWidget(self.label_4)
        v1.addWidget(self.template)


        self.set = QtWidgets.QPushButton(claudeform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(claudeform)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")
        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/claude'))

        h4.addWidget(self.set)
        h4.addWidget(self.test)
        h4.addWidget(help_btn)
        v1.addLayout(h4)

        self.retranslateUi(claudeform)
        QtCore.QMetaObject.connectSlotsByName(claudeform)

    def retranslateUi(self, claudeform):
        claudeform.setWindowTitle("Claude API")
        self.label_3.setText('选择模型' if config.defaulelang == 'zh' else "Model")
        self.template.setPlaceholderText("prompt")
        self.label_4.setText(
            "{lang}代表目标语言名称，不要删除。" if config.defaulelang == 'zh' else "{lang} represents the target language name, do not delete it.")
        self.set.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试..' if config.defaulelang == 'zh' else "Test..")
        self.api.setPlaceholderText(
            '若使用Claude官方接口，无需填写;第三方api在此填写' if config.defaulelang == 'zh' else 'If using the official Claude interface, there is no need to fill it out; Fill in the third-party API here')
        self.api.setToolTip(
            '若使用Claude官方接口，无需填写;第三方api在此填写' if config.defaulelang == 'zh' else 'If using the official Claude interface, there is no need to fill it out; Fill in the third-party API here')
        self.key.setPlaceholderText("Secret key")
        self.key.setToolTip(
            "必须是付费账号，免费账号频率受限无法使用" if config.defaulelang == 'zh' else 'Must be a paid account, free account frequency is limited and cannot be used')
        self.label.setText("API URL")
        self.label_2.setText("SK")
