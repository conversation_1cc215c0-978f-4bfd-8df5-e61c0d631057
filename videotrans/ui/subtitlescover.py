# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config


class Ui_subtitlescover(object):
    def setupUi(self, subtitlescover):
        self.subtitlefiles = []
        self.has_done = False
        subtitlescover.setObjectName("subtitlescover")
        subtitlescover.setWindowModality(QtCore.Qt.NonModal)
        subtitlescover.resize(500, 400)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(subtitlescover.sizePolicy().hasHeightForWidth())
        subtitlescover.setSizePolicy(sizePolicy)

        v1 = QtWidgets.QVBoxLayout(subtitlescover)

        self.formLayout_2 = QtWidgets.QHBoxLayout()
        self.formLayout_2.setObjectName("formLayout_2")
        self.formLayout_2.setAlignment(QtCore.Qt.AlignVCenter)

        self.selectbtn = QtWidgets.QPushButton(subtitlescover)
        self.selectbtn.setMinimumSize(QtCore.QSize(150, 35))
        self.selectbtn.setObjectName("selectbtn")
        self.selectbtn.setCursor(Qt.PointingHandCursor)

        self.pathdir = QtWidgets.QLineEdit(subtitlescover)
        self.pathdir.setMinimumSize(QtCore.QSize(0, 35))
        self.pathdir.setObjectName("pathdir")
        self.pathdir.setReadOnly(True)

        self.formLayout_2.addWidget(self.selectbtn)
        self.formLayout_2.addWidget(self.pathdir)
        v1.addLayout(self.formLayout_2)

        # sk
        self.formLayout_3 = QtWidgets.QHBoxLayout()
        self.formLayout_3.setAlignment(QtCore.Qt.AlignVCenter)
        self.formLayout_3.setObjectName("formLayout_3")

        self.labelformat = QtWidgets.QLabel(subtitlescover)
        self.labelformat.setMinimumSize(QtCore.QSize(0, 35))
        self.labelformat.setObjectName("label")

        self.formatlist = QtWidgets.QComboBox(subtitlescover)
        self.formatlist.setFixedHeight(40)
        self.formatlist.setFixedWidth(320)
        self.formatlist.setObjectName("formatlist")
        self.formatlist.addItems([
            "srt",
            "ass",
            "vtt"
        ])

        self.formLayout_3.addWidget(self.labelformat)
        self.formLayout_3.addStretch()
        self.formLayout_3.addWidget(self.formatlist)
        v1.addLayout(self.formLayout_3)

        self.startbtn = QtWidgets.QPushButton(subtitlescover)
        self.startbtn.setMinimumSize(QtCore.QSize(200, 35))
        self.startbtn.setObjectName("startbtn")
        self.startbtn.setCursor(Qt.PointingHandCursor)

        self.opendir = QtWidgets.QPushButton(subtitlescover)
        self.opendir.setMinimumSize(QtCore.QSize(0, 35))
        self.opendir.setStyleSheet('''background-color:transparent''')
        self.opendir.setObjectName("opendir")
        self.opendir.setCursor(Qt.PointingHandCursor)

        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")
        self.layout_btn.addStretch()
        self.layout_btn.addWidget(self.startbtn)
        self.layout_btn.addStretch()

        self.opendir_layout = QtWidgets.QHBoxLayout()
        self.opendir_layout.setObjectName("opendir_layout")
        self.opendir_layout.addStretch()
        self.opendir_layout.addWidget(self.opendir)
        self.opendir_layout.addStretch()

        v1.addLayout(self.layout_btn)
        v1.addStretch()
        v1.addLayout(self.opendir_layout)

        self.retranslateUi(subtitlescover)
        QtCore.QMetaObject.connectSlotsByName(subtitlescover)

    def retranslateUi(self, subtitlescover):
        subtitlescover.setWindowTitle('字幕多格式转换' if config.defaulelang == 'zh' else 'Subtitle Conversion')

        self.selectbtn.setText(
            '选择要转换的字幕/可多选' if config.defaulelang == 'zh' else 'Select files to be converted/multiple selections possible')

        self.labelformat.setText('要转换到的字幕格式' if config.defaulelang == 'zh' else 'Target format')

        self.pathdir.setPlaceholderText(
            '选择要转换的字幕/可多选' if config.defaulelang == 'zh' else 'Select files to be converted/multiple selections possible')

        self.startbtn.setText('开始转换' if config.defaulelang == 'zh' else 'Start')
        self.opendir.setText('打开结果目录' if config.defaulelang == 'zh' else 'Open the results catalog')
