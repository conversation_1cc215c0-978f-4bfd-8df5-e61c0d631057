# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QLabel

from videotrans.configure import config
from videotrans.util import tools


class Ui_ottform(object):
    def setupUi(self, ottform):
        self.has_done = False
        ottform.setObjectName("ottform")
        ottform.setWindowModality(QtCore.Qt.NonModal)
        ottform.resize(400, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ottform.sizePolicy().hasHeightForWidth())
        ottform.setSizePolicy(sizePolicy)
        ottform.setMaximumSize(QtCore.QSize(400, 300))

        self.verticalLayout = QtWidgets.QVBoxLayout(ottform)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(ottform)
        self.label.setMinimumSize(QtCore.QSize(0, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.ott_address = QtWidgets.QLineEdit(ottform)
        self.ott_address.setMinimumSize(QtCore.QSize(0, 35))
        self.ott_address.setObjectName("ott_address")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.ott_address)
        self.verticalLayout.addLayout(self.formLayout_2)

        self.set_ott = QtWidgets.QPushButton(ottform)
        self.set_ott.setMinimumSize(QtCore.QSize(0, 35))
        self.set_ott.setObjectName("set_ott")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/ott'))

        h1=QtWidgets.QHBoxLayout()
        h1.addWidget(self.set_ott)
        h1.addWidget(help_btn)
        self.verticalLayout.addLayout(h1)

        self.retranslateUi(ottform)
        QtCore.QMetaObject.connectSlotsByName(ottform)

    def retranslateUi(self, ottform):
        ottform.setWindowTitle("OTT离线翻译" if config.defaulelang == 'zh' else 'Offline Text Translate')
        self.label.setText("OTT_api")
        self.ott_address.setPlaceholderText(
            '在此填写你的OTT项目部署后的http地址' if config.defaulelang == 'zh' else 'Fill in the HTTP address for your OTT project deployment here')
        self.set_ott.setText('保存' if config.defaulelang == 'zh' else "Save")
