# run again.  Do not edit this file unless you know what you are doing.

from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QLabel

from videotrans.configure import config
from videotrans.util import tools


class Ui_chatttsform(object):
    def setupUi(self, chattts):
        self.has_done = False
        chattts.setObjectName("chattts")
        chattts.setWindowModality(QtCore.Qt.NonModal)
        chattts.resize(500, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(chattts.sizePolicy().hasHeightForWidth())
        chattts.setSizePolicy(sizePolicy)
        chattts.setMaximumSize(QtCore.QSize(500, 300))

        self.verticalLayout = QtWidgets.QVBoxLayout(chattts)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(chattts)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.chattts_address = QtWidgets.QLineEdit(chattts)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.chattts_address.sizePolicy().hasHeightForWidth())
        self.chattts_address.setSizePolicy(sizePolicy)
        self.chattts_address.setMinimumSize(QtCore.QSize(400, 35))
        self.chattts_address.setObjectName("chattts_address")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.chattts_address)

        self.formLayout_3 = QtWidgets.QFormLayout()
        self.formLayout_3.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_3.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_3.setObjectName("formLayout_3")
        self.label3 = QtWidgets.QLabel(chattts)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label3.sizePolicy().hasHeightForWidth())
        self.label3.setSizePolicy(sizePolicy)
        self.label3.setMinimumSize(QtCore.QSize(100, 35))
        self.label3.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label3.setObjectName("label3")

        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label3)
        self.chattts_voice = QtWidgets.QLineEdit(chattts)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.chattts_voice.sizePolicy().hasHeightForWidth())
        self.chattts_voice.setSizePolicy(sizePolicy)
        self.chattts_voice.setMinimumSize(QtCore.QSize(400, 35))
        self.chattts_voice.setObjectName("chattts_voice")

        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.chattts_voice)

        self.verticalLayout.addLayout(self.formLayout_2)
        self.verticalLayout.addLayout(self.formLayout_3)


        self.set_chattts = QtWidgets.QPushButton(chattts)
        self.set_chattts.setMinimumSize(QtCore.QSize(0, 35))
        self.set_chattts.setObjectName("set_chattts")

        self.test = QtWidgets.QPushButton(chattts)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/chattts'))

        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")

        self.layout_btn.addWidget(self.set_chattts)
        self.layout_btn.addWidget(self.test)
        self.layout_btn.addWidget(help_btn)

        self.verticalLayout.addLayout(self.layout_btn)


        self.retranslateUi(chattts)
        QtCore.QMetaObject.connectSlotsByName(chattts)

    def retranslateUi(self, chattts):
        chattts.setWindowTitle("ChatTTS API")
        self.label.setText("http地址" if config.defaulelang == 'zh' else 'ChatTTS URL')
        self.label3.setText("音色值" if config.defaulelang == 'zh' else 'ChatTTS Voice')
        self.chattts_address.setPlaceholderText(
            '填写 ChatTTS webUI 项目启动后的http地址' if config.defaulelang == 'zh' else 'Fill in the HTTP address after the ChatTTS webUI program starts')
        self.set_chattts.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试' if config.defaulelang == 'zh' else "Test")
