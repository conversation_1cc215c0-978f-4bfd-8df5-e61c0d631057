# run again.  Do not edit this file unless you know what you are doing.
from PySide6 import QtWidgets, QtCore
from PySide6.QtCore import QMetaObject, QRect, QSize, Qt
from PySide6.QtWidgets import QLabel, QLineEdit, QPlainTextEdit, QPushButton, QSizePolicy

from videotrans.configure import config
from videotrans.util import tools


class Ui_fishttsform(object):
    def setupUi(self, fishttsform):
        self.has_done = False
        if not fishttsform.objectName():
            fishttsform.setObjectName("fishttsform")
        fishttsform.setWindowModality(Qt.NonModal)
        fishttsform.resize(600, 500)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(fishttsform.sizePolicy().hasHeightForWidth())
        fishttsform.setSizePolicy(sizePolicy)
        fishttsform.setMaximumSize(QSize(600, 500))

        v1=QtWidgets.QVBoxLayout(fishttsform)

        self.label = QLabel(fishttsform)
        self.label.setObjectName("label")
        self.label.setMinimumSize(QSize(0, 35))
        self.api_url = QLineEdit(fishttsform)
        self.api_url.setObjectName("api_url")
        self.api_url.setMinimumSize(QSize(0, 35))
        h1=QtWidgets.QHBoxLayout()
        h1.addWidget(self.label)
        h1.addWidget(self.api_url)
        v1.addLayout(h1)



        self.label_4 = QLabel(fishttsform)
        self.label_4.setObjectName("label_4")
        self.label_4.setText('参考音频路径名称#音频对应文字内容  一行一组')
        v1.addWidget(self.label_4)

        self.role = QPlainTextEdit(fishttsform)
        self.role.setObjectName("role")
        self.role.setMinimumHeight(100)
        self.role.setReadOnly(False)
        v1.addWidget(self.role)

        self.label_5 = QLabel(fishttsform)
        self.label_5.setObjectName("label_5")
        self.label_5.setText('API请求说明')
        v1.addWidget(self.label_5)

        self.tips = QPlainTextEdit(fishttsform)
        self.tips.setObjectName("tips")
        self.tips.setMinimumHeight(150)
        self.tips.setReadOnly(True)
        v1.addWidget(self.tips)

        h2=QtWidgets.QHBoxLayout()

        self.save = QPushButton(fishttsform)
        self.save.setObjectName("save")
        self.save.setGeometry(QRect(10, 450, 93, 35))
        self.save.setMinimumSize(QSize(0, 35))

        self.test = QPushButton(fishttsform)
        self.test.setObjectName("test")
        self.test.setGeometry(QRect(490, 450, 93, 35))
        self.test.setMinimumSize(QSize(0, 35))

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/fishtts'))

        h2.addWidget(self.save)
        h2.addWidget(self.test)
        h2.addWidget(help_btn)
        v1.addLayout(h2)

        self.retranslateUi(fishttsform)

        QMetaObject.connectSlotsByName(fishttsform)

    # setupUi

    def retranslateUi(self, fishttsform):
        tips = """
Fish-speech TTS 开源地址 https://github.com/fishaudio/fish-speech

将以POST请求向填写的API地址发送application/json数据：

FishTTS自带 tools/api_server.py，可接受请求

本工具将向填写的API地址发送以下3个参数

text:需要合成的文本/字符串
references[0][audio]:参考音频路径，请放在本软件的根目录下，例如 1.wav或 wavs/1.wav
references[0][text]:参考音频中的语音文本


请求失败时返回json格式数据
      
请求成功时返回音频流
"""

        fishttsform.setWindowTitle("Fish-speech API/fish-speech >=1.5.0")
        self.role.setPlaceholderText("在此填写参考音频信息,格式如下\n例如：一行一组\n123.wav#你好啊我的朋友")
        self.tips.setPlainText(tips)
        self.save.setText("保存" if config.defaulelang == 'zh' else "Save")
        self.api_url.setPlaceholderText("填写http开头的API,Fish-speech 1.5.0默认 http://127.0.0.1:8080/v1/tts")
        self.label.setText("Fish-speech API")
        self.test.setText("测试Api" if config.defaulelang == 'zh' else "Test API")
    # retranslateUi
