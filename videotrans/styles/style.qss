QToolBar {  margin:0;    padding:0;    border-image:none;} QToolBar QToolButton {background-color: #32414B;  height:26px;  margin-bottom:0px;  margin-top:3px;  margin-left:1px;  margin-right:0px;  text-align: left;}QToolBar QToolButton:hover {border: 1px solid #148CD2;}QToolBar QToolButton:checked {background-color: #19232D;  border: 1px solid #148CD2;}QToolBar QToolButton:checked:hover {border: 1px solid #339933;}QLabel{  color: #bbbbbb;}QLineEdit:hover,QComboBox:hover{  border-color: #148cd2;} QLineEdit,QComboBox{  background-color: #161E26;    border-color: #32414B;}QLineEdit:disabled {  background-color: transparent;    border-color: transparent;}QComboBox:disabled{  background-color: transparent;    border-color: #273442;}QScrollArea QPushButton{  background-color: rgba(50, 65, 75, 0.5);    border-radius: 0;    opacity: 0.1;    text-align:left;    padding-left:5px;}QScrollArea QPushButton:hover{  background-color: #19232D;}QPlainTextEdit:read-only{  color:#999999;}* {padding: 0px;  margin: 0px;  border: 0px;  border-style: none;  border-image: none;  outline: 0;}QToolBar > * {margin: 0px;  padding: 0px;}QWidget {background-color: #19232D;  border: 0px solid #455364;  padding: 0px;  color: #DFE1E2;  selection-background-color: #346792;  selection-color: #DFE1E2;}QWidget:disabled {background-color: #19232D;  color: #788D9C;  selection-background-color: #26486B;  selection-color: #788D9C;}QWidget::item:selected {background-color: #346792;}QWidget::item:hover:!selected {background-color: #1A72BB;}QMainWindow::separator {background-color: #455364;  border: 0px solid #19232D;  spacing: 0px;  padding: 2px;}QMainWindow::separator:hover {background-color: #60798B;  border: 0px solid #1A72BB;}QMainWindow::separator:horizontal {width: 3px;  margin-top: 2px;  margin-bottom: 2px;  image: url(":/qss_icons/dark/rc/toolbar_separator_vertical.png");}QMainWindow::separator:vertical {height: 5px;  margin-left: 2px;  margin-right: 2px;  image: url(":/qss_icons/dark/rc/toolbar_separator_horizontal.png");}QToolTip {background-color: #346792;  color: #DFE1E2;  border: none;  padding: 0px;}QStatusBar {border: 1px solid #455364;  background: #455364;}QStatusBar::item {border: none;}QStatusBar QToolTip {background-color: #1A72BB;  border: 1px solid #19232D;  color: #19232D;  padding: 0px;  opacity: 230;}QStatusBar QLabel {background: transparent;}QCheckBox {background-color: #19232D;  color: #DFE1E2;  spacing: 4px;  outline: none;  padding-top: 4px;  padding-bottom: 4px;}QCheckBox:focus {border: none;}QCheckBox QWidget:disabled {background-color: #19232D;  color: #788D9C;}QCheckBox::indicator {margin-left: 2px;  height: 14px;  width: 14px;}QCheckBox::indicator:unchecked {image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");}QCheckBox::indicator:unchecked:hover, QCheckBox::indicator:unchecked:focus, QCheckBox::indicator:unchecked:pressed {image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");}QCheckBox::indicator:unchecked:disabled {image: url(":/qss_icons/dark/rc/checkbox_unchecked_disabled.png");}QCheckBox::indicator:checked {image: url(":/qss_icons/dark/rc/checkbox_checked.png");}QCheckBox::indicator:checked:hover, QCheckBox::indicator:checked:focus, QCheckBox::indicator:checked:pressed { image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");}QCheckBox::indicator:checked:disabled {image: url(":/qss_icons/dark/rc/checkbox_checked_disabled.png");}QCheckBox::indicator:indeterminate {image: url(":/qss_icons/dark/rc/checkbox_indeterminate.png");}QCheckBox::indicator:indeterminate:disabled {image: url(":/qss_icons/dark/rc/checkbox_indeterminate_disabled.png");}QCheckBox::indicator:indeterminate:focus, QCheckBox::indicator:indeterminate:hover, QCheckBox::indicator:indeterminate:pressed {image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");}QMenuBar {background-color: #455364;  padding: 2px;  border: 1px solid #19232D;  color: #DFE1E2;  selection-background-color: #1A72BB;}QMenuBar:focus {border: 1px solid #346792;}QMenuBar::item {background: transparent;  padding: 4px;}QMenuBar::item:selected {padding: 4px;  background: transparent;  border: 0px solid #455364;  background-color: #1A72BB;}QMenuBar::item:pressed {padding: 4px;  border: 0px solid #455364;  background-color: #1A72BB;  color: #DFE1E2;  margin-bottom: 0px;  padding-bottom: 0px;}QMenu {border: 0px solid #455364;  color: #DFE1E2;  margin: 0px;  background-color: #37414F;  selection-background-color: #1A72BB;}QMenu::separator {height: 1px;  background-color: #60798B;  color: #DFE1E2;}QMenu::item {background-color: #37414F;  padding: 4px 24px 4px 28px;  border: 1px transparent #455364;}QMenu::item:selected {color: #DFE1E2;  background-color: #1A72BB;}QMenu::item:pressed {background-color: #1A72BB;}QMenu::icon {padding-left: 10px;  width: 14px;  height: 14px;}QMenu::indicator {padding-left: 8px;  width: 12px;  height: 12px;}QMenu::indicator:non-exclusive:unchecked {image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");}QMenu::indicator:non-exclusive:unchecked:hover, QMenu::indicator:non-exclusive:unchecked:focus, QMenu::indicator:non-exclusive:unchecked:pressed {border: none;  image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");}QMenu::indicator:non-exclusive:unchecked:disabled {image: url(":/qss_icons/dark/rc/checkbox_unchecked_disabled.png");}QMenu::indicator:non-exclusive:checked {image: url(":/qss_icons/dark/rc/checkbox_checked.png");}QMenu::indicator:non-exclusive:checked:hover, QMenu::indicator:non-exclusive:checked:focus, QMenu::indicator:non-exclusive:checked:pressed {border: none;  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");}QMenu::indicator:non-exclusive:checked:disabled {image: url(":/qss_icons/dark/rc/checkbox_checked_disabled.png");}QMenu::indicator:non-exclusive:indeterminate {image: url(":/qss_icons/dark/rc/checkbox_indeterminate.png");}QMenu::indicator:non-exclusive:indeterminate:disabled {image: url(":/qss_icons/dark/rc/checkbox_indeterminate_disabled.png");}QMenu::indicator:non-exclusive:indeterminate:focus, QMenu::indicator:non-exclusive:indeterminate:hover, QMenu::indicator:non-exclusive:indeterminate:pressed {image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");}QMenu::indicator:exclusive:unchecked {image: url(":/qss_icons/dark/rc/radio_unchecked.png");}QMenu::indicator:exclusive:unchecked:hover, QMenu::indicator:exclusive:unchecked:focus, QMenu::indicator:exclusive:unchecked:pressed {border: none;  outline: none;  image: url(":/qss_icons/dark/rc/radio_unchecked_focus.png");}QMenu::indicator:exclusive:unchecked:disabled {image: url(":/qss_icons/dark/rc/radio_unchecked_disabled.png");}QMenu::indicator:exclusive:checked {border: none;  outline: none;  image: url(":/qss_icons/dark/rc/radio_checked.png");}QMenu::indicator:exclusive:checked:hover, QMenu::indicator:exclusive:checked:focus, QMenu::indicator:exclusive:checked:pressed {border: none;  outline: none;  image: url(":/qss_icons/dark/rc/radio_checked_focus.png");}QMenu::indicator:exclusive:checked:disabled {outline: none;  image: url(":/qss_icons/dark/rc/radio_checked_disabled.png");}QMenu::right-arrow {margin: 5px;  padding-left: 12px;  image: url(":/qss_icons/dark/rc/arrow_right.png");  height: 12px;  width: 12px;}QScrollArea QWidget QWidget:disabled {background-color: #19232D;}QScrollBar:horizontal {height: 16px;  margin: 2px 16px 2px 16px;  border: 1px solid #455364;  border-radius: 4px;  background-color: #19232D;}QScrollBar:vertical {background-color: #19232D;  width: 16px;  margin: 16px 2px 16px 2px;  border: 1px solid #455364;  border-radius: 4px;}QScrollBar::handle:horizontal {background-color: #60798B;  border: 1px solid #455364;  border-radius: 4px;  min-width: 8px;}QScrollBar::handle:horizontal:hover {background-color: #346792;  border: #346792;  border-radius: 4px;  min-width: 8px;}QScrollBar::handle:horizontal:focus {border: 1px solid #1A72BB;}QScrollBar::handle:vertical {background-color: #60798B;  border: 1px solid #455364;  min-height: 8px;  border-radius: 4px;}QScrollBar::handle:vertical:hover {background-color: #346792;  border: #346792;  border-radius: 4px;  min-height: 8px;}QScrollBar::handle:vertical:focus {border: 1px solid #1A72BB;}QScrollBar::add-line:horizontal {margin: 0px 0px 0px 0px;  border-image: url(":/qss_icons/dark/rc/arrow_right_disabled.png");  height: 12px;  width: 12px;  subcontrol-position: right;  subcontrol-origin: margin;}QScrollBar::add-line:horizontal:hover, QScrollBar::add-line:horizontal:on {border-image: url(":/qss_icons/dark/rc/arrow_right.png");  height: 12px;  width: 12px;  subcontrol-position: right;  subcontrol-origin: margin;}QScrollBar::add-line:vertical {margin: 3px 0px 3px 0px;  border-image: url(":/qss_icons/dark/rc/arrow_down_disabled.png");  height: 12px;  width: 12px;  subcontrol-position: bottom;  subcontrol-origin: margin;}QScrollBar::add-line:vertical:hover, QScrollBar::add-line:vertical:on {border-image: url(":/qss_icons/dark/rc/arrow_down.png");  height: 12px;  width: 12px;  subcontrol-position: bottom;  subcontrol-origin: margin;}QScrollBar::sub-line:horizontal {margin: 0px 3px 0px 3px;  border-image: url(":/qss_icons/dark/rc/arrow_left_disabled.png");  height: 12px;  width: 12px;  subcontrol-position: left;  subcontrol-origin: margin;}QScrollBar::sub-line:horizontal:hover, QScrollBar::sub-line:horizontal:on {border-image: url(":/qss_icons/dark/rc/arrow_left.png");  height: 12px;  width: 12px;  subcontrol-position: left;  subcontrol-origin: margin;}QScrollBar::sub-line:vertical {margin: 3px 0px 3px 0px;  border-image: url(":/qss_icons/dark/rc/arrow_up_disabled.png");  height: 12px;  width: 12px;  subcontrol-position: top;  subcontrol-origin: margin;}QScrollBar::sub-line:vertical:hover, QScrollBar::sub-line:vertical:on {border-image: url(":/qss_icons/dark/rc/arrow_up.png");  height: 12px;  width: 12px;  subcontrol-position: top;  subcontrol-origin: margin;}QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal {background: none;}QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {background: none;}QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {background: none;}QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {background: none;}QTextEdit {background-color: #19232D;  color: #DFE1E2;  border-radius: 4px;  border: 1px solid #455364;}QTextEdit:focus {border: 1px solid #1A72BB;}QTextEdit:selected {background: #346792;  color: #455364;}QPlainTextEdit {background-color: #19232D;  color: #DFE1E2;  border-radius: 4px;  border: 1px solid #455364;}QPlainTextEdit:focus {border: 1px solid #1A72BB;}QPlainTextEdit:selected {background: #346792;  color: #455364;}QToolBar {background-color: #455364;  border-bottom: 1px solid #19232D;  padding: 1px;  font-weight: bold;  spacing: 2px;}QToolBar:disabled {background-color: #455364;}QToolBar::handle:horizontal {width: 16px;  image: url(":/qss_icons/dark/rc/toolbar_move_horizontal.png");}QToolBar::handle:vertical {height: 16px;  image: url(":/qss_icons/dark/rc/toolbar_move_vertical.png");}QToolBar::separator:horizontal {width: 16px;  image: url(":/qss_icons/dark/rc/toolbar_separator_horizontal.png");}QToolBar::separator:vertical {height: 16px;  image: url(":/qss_icons/dark/rc/toolbar_separator_vertical.png");}QLabel {background-color: #19232D;  border: 0px solid #455364;  padding: 2px;  margin: 0px;  color: #DFE1E2;}QLabel:disabled {background-color: #19232D;  border: 0px solid #455364;  color: #788D9C;}QTextBrowser {background-color: #19232D;  border: 1px solid #455364;  color: #DFE1E2;  border-radius: 4px;}QTextBrowser:disabled {background-color: #19232D;  border: 1px solid #455364;  color: #788D9C;  border-radius: 4px;}QTextBrowser:hover, QTextBrowser:!hover, QTextBrowser:selected, QTextBrowser:pressed {border: 1px solid #455364;}QProgressBar {background-color: #19232D;  border: 1px solid #455364;  color: #DFE1E2;  border-radius: 4px;  text-align: center;}QProgressBar:disabled {background-color: #19232D;  border: 1px solid #455364;  color: #788D9C;  border-radius: 4px;  text-align: center;}QProgressBar::chunk {background-color: #346792;  color: #19232D;  border-radius: 4px;}QProgressBar::chunk:disabled {background-color: #26486B;  color: #788D9C;  border-radius: 4px;}QPushButton {background-color: #455364;  color: #DFE1E2;  border-radius: 4px;  padding: 2px;  outline: none;  border: none;}QPushButton:disabled {background-color: #455364;  color: #788D9C;  border-radius: 4px;  padding: 2px;}QPushButton:checked {background-color: #60798B;  border-radius: 4px;  padding: 2px;  outline: none;}QPushButton:checked:disabled {background-color: #60798B;  color: #788D9C;  border-radius: 4px;  padding: 2px;  outline: none;}QPushButton:checked:selected {background: #60798B;}QPushButton:hover {background-color: #54687A;  color: #DFE1E2;}QPushButton:pressed {background-color: #60798B;}QPushButton:selected {background: #60798B;  color: #DFE1E2;}QPushButton::menu-indicator {subcontrol-origin: padding;  subcontrol-position: bottom right;  bottom: 4px;}QDialogButtonBox QPushButton {min-width: 80px;}QToolButton {background-color: #455364;  color: #DFE1E2;  border-radius: 4px;  padding: 2px;  outline: none;  border: none;}QToolButton:disabled {background-color: #455364;  color: #788D9C;  border-radius: 4px;  padding: 2px;}QToolButton:checked {background-color: #60798B;  border-radius: 4px;  padding: 2px;  outline: none;}QToolButton:checked:disabled {background-color: #60798B;  color: #788D9C;  border-radius: 4px;  padding: 2px;  outline: none;}QToolButton:checked:hover {background-color: #54687A;  color: #DFE1E2;}QToolButton:checked:pressed {background-color: #60798B;}QToolButton:checked:selected {background: #60798B;  color: #DFE1E2;}QToolButton:hover {background-color: #54687A;  color: #DFE1E2;}QToolButton:pressed {background-color: #60798B;}QToolButton:selected {background: #60798B;  color: #DFE1E2;}QToolButton[popupMode="0"] {padding-right: 2px;}QToolButton[popupMode="1"] {padding-right: 20px;}QToolButton[popupMode="1"]::menu-button {border: none;}QToolButton[popupMode="1"]::menu-button:hover {border: none;  border-left: 1px solid #455364;  border-radius: 0;}QToolButton[popupMode="2"] {/* Only for InstantPopup */  padding-right: 2px;}QToolButton::menu-button {padding: 2px;  border-radius: 4px;  width: 12px;  border: none;  outline: none;}QToolButton::menu-button:hover {border: 1px solid #346792;}QToolButton::menu-button:checked:hover {border: 1px solid #346792;}QToolButton::menu-indicator {image: url(":/qss_icons/dark/rc/arrow_down.png");  height: 8px;  width: 8px;  top: 0;  left: -2px;}QToolButton::menu-arrow {image: url(":/qss_icons/dark/rc/arrow_down.png");  height: 8px;  width: 8px;}QToolButton::menu-arrow:hover {image: url(":/qss_icons/dark/rc/arrow_down_focus.png");}QComboBox {border: 1px solid #455364;  border-radius: 4px;  selection-background-color: #346792;  padding-left: 4px;  padding-right: 4px;  min-height: 1.5em;}QComboBox QAbstractItemView {border: 1px solid #455364;  border-radius: 0;  background-color: #19232D;  selection-background-color: #346792;}QComboBox QAbstractItemView:hover {background-color: #19232D;  color: #DFE1E2;}QComboBox QAbstractItemView:selected {background: #346792;  color: #455364;}QComboBox QAbstractItemView:alternate {background: #19232D;}QComboBox:disabled {background-color: #19232D;  color: #788D9C;}QComboBox:hover {border: 1px solid #346792;}QComboBox:focus {border: 1px solid #1A72BB;}QComboBox:on {selection-background-color: #346792;}QComboBox::indicator {border: none;  border-radius: 0;  background-color: transparent;  selection-background-color: transparent;  color: transparent;  selection-color: transparent;}QComboBox::indicator:alternate {background: #19232D;}QComboBox::item:alternate {background: #19232D;}QComboBox::drop-down {subcontrol-origin: padding;  subcontrol-position: top right;  width: 12px;  border-left: 1px solid #455364;}QComboBox::down-arrow {image: url(":/qss_icons/dark/rc/arrow_down_disabled.png");  height: 8px;  width: 8px;}QComboBox::down-arrow:on, QComboBox::down-arrow:hover, QComboBox::down-arrow:focus {image: url(":/qss_icons/dark/rc/arrow_down.png");}QLineEdit {background-color: #19232D;  padding-top: 2px;  padding-bottom: 2px;  padding-left: 4px;  padding-right: 4px;  border-style: solid;  border: 1px solid #455364;  border-radius: 4px;  color: #DFE1E2;}QLineEdit:disabled {background-color: #19232D;  color: #788D9C;}QLineEdit:hover {border: 1px solid #346792;  color: #DFE1E2;}QLineEdit:focus {border: 1px solid #1A72BB;}QLineEdit:selected {background-color: #346792;  color: #455364;}QTabWidget {padding: 2px;  selection-background-color: #455364;}QTabWidget QWidget {border-radius: 4px;}QTabWidget::pane {border: 1px solid #455364;  border-radius: 4px;  margin: 0px;  padding: 0px;}QTabWidget::pane:selected {background-color: #455364;  border: 1px solid #346792;}QTabBar, QDockWidget QTabBar {qproperty-drawBase: 0;  border-radius: 4px;  margin: 0px;  padding: 2px;  border: 0;}QTabBar::close-button, QDockWidget QTabBar::close-button {border: 0;  margin: 0;  padding: 4px;  image: url(":/qss_icons/dark/rc/window_close.png");}QTabBar::close-button:hover, QDockWidget QTabBar::close-button:hover {image: url(":/qss_icons/dark/rc/window_close_focus.png");}QTabBar::close-button:pressed, QDockWidget QTabBar::close-button:pressed {image: url(":/qss_icons/dark/rc/window_close_pressed.png");}QTabBar::tab:top:selected:disabled, QDockWidget QTabBar::tab:top:selected:disabled {border-bottom: 3px solid #26486B;  color: #788D9C;  background-color: #455364;}QTabBar::tab:bottom:selected:disabled, QDockWidget QTabBar::tab:bottom:selected:disabled {border-top: 3px solid #26486B;  color: #788D9C;  background-color: #455364;}QTabBar::tab:left:selected:disabled, QDockWidget QTabBar::tab:left:selected:disabled {border-right: 3px solid #26486B;  color: #788D9C;  background-color: #455364;}QTabBar::tab:right:selected:disabled, QDockWidget QTabBar::tab:right:selected:disabled {border-left: 3px solid #26486B;  color: #788D9C;  background-color: #455364;}QTabBar::tab:top:!selected:disabled, QDockWidget QTabBar::tab:top:!selected:disabled {border-bottom: 3px solid #19232D;  color: #788D9C;  background-color: #19232D;}QTabBar::tab:bottom:!selected:disabled, QDockWidget QTabBar::tab:bottom:!selected:disabled {border-top: 3px solid #19232D;  color: #788D9C;  background-color: #19232D;}QTabBar::tab:left:!selected:disabled, QDockWidget QTabBar::tab:left:!selected:disabled {border-right: 3px solid #19232D;  color: #788D9C;  background-color: #19232D;}QTabBar::tab:right:!selected:disabled, QDockWidget QTabBar::tab:right:!selected:disabled {border-left: 3px solid #19232D;  color: #788D9C;  background-color: #19232D;}QTabBar::tab:top:!selected, QDockWidget QTabBar::tab:top:!selected {border-bottom: 2px solid #19232D;  margin-top: 2px;}QTabBar::tab:bottom:!selected, QDockWidget QTabBar::tab:bottom:!selected {border-top: 2px solid #19232D;  margin-bottom: 2px;}QTabBar::tab:left:!selected, QDockWidget QTabBar::tab:left:!selected {border-left: 2px solid #19232D;  margin-right: 2px;}QTabBar::tab:right:!selected, QDockWidget QTabBar::tab:right:!selected {border-right: 2px solid #19232D;  margin-left: 2px;}QTabBar::tab:top, QDockWidget QTabBar::tab:top {background-color: #455364;  margin-left: 2px;  padding-left: 4px;  padding-right: 4px;  padding-top: 2px;  padding-bottom: 2px;  min-width: 5px;  border-bottom: 3px solid #455364;  border-top-left-radius: 4px;  border-top-right-radius: 4px;}QTabBar::tab:top:selected, QDockWidget QTabBar::tab:top:selected {background-color: #54687A;  border-bottom: 3px solid #259AE9;  border-top-left-radius: 4px;  border-top-right-radius: 4px;}QTabBar::tab:top:!selected:hover, QDockWidget QTabBar::tab:top:!selected:hover {border: 1px solid #1A72BB;  border-bottom: 3px solid #1A72BB;  padding-left: 3px;  padding-right: 3px;}QTabBar::tab:bottom, QDockWidget QTabBar::tab:bottom {border-top: 3px solid #455364;  background-color: #455364;  margin-left: 2px;  padding-left: 4px;  padding-right: 4px;  padding-top: 2px;  padding-bottom: 2px;  border-bottom-left-radius: 4px;  border-bottom-right-radius: 4px;  min-width: 5px;}QTabBar::tab:bottom:selected, QDockWidget QTabBar::tab:bottom:selected {background-color: #54687A;  border-top: 3px solid #259AE9;  border-bottom-left-radius: 4px;  border-bottom-right-radius: 4px;}QTabBar::tab:bottom:!selected:hover, QDockWidget QTabBar::tab:bottom:!selected:hover {border: 1px solid #1A72BB;  border-top: 3px solid #1A72BB;  padding-left: 3px;  padding-right: 3px;}QTabBar::tab:left, QDockWidget QTabBar::tab:left {background-color: #455364;  margin-top: 2px;  padding-left: 2px;  padding-right: 2px;  padding-top: 4px;  padding-bottom: 4px;  border-top-left-radius: 4px;  border-bottom-left-radius: 4px;  min-height: 5px;}QTabBar::tab:left:selected, QDockWidget QTabBar::tab:left:selected {background-color: #54687A;  border-right: 3px solid #259AE9;}QTabBar::tab:left:!selected:hover, QDockWidget QTabBar::tab:left:!selected:hover {border: 1px solid #1A72BB;  border-right: 3px solid #1A72BB;  margin-right: 0px;  padding-right: -1px;}QTabBar::tab:right, QDockWidget QTabBar::tab:right {background-color: #455364;  margin-top: 2px;  padding-left: 2px;  padding-right: 2px;  padding-top: 4px;  padding-bottom: 4px;  border-top-right-radius: 4px;  border-bottom-right-radius: 4px;  min-height: 5px;}QTabBar::tab:right:selected, QDockWidget QTabBar::tab:right:selected {background-color: #54687A;  border-left: 3px solid #259AE9;}QTabBar::tab:right:!selected:hover, QDockWidget QTabBar::tab:right:!selected:hover {border: 1px solid #1A72BB;  border-left: 3px solid #1A72BB;  margin-left: 0px;  padding-left: 0px;}QTabBar QToolButton, QDockWidget QTabBar QToolButton {background-color: #455364;  height: 12px;  width: 12px;}QTabBar QToolButton:pressed, QDockWidget QTabBar QToolButton:pressed {background-color: #455364;}QTabBar QToolButton:pressed:hover, QDockWidget QTabBar QToolButton:pressed:hover {border: 1px solid #346792;}QTabBar QToolButton::left-arrow:enabled, QDockWidget QTabBar QToolButton::left-arrow:enabled {image: url(":/qss_icons/dark/rc/arrow_left.png");}QTabBar QToolButton::left-arrow:disabled, QDockWidget QTabBar QToolButton::left-arrow:disabled {image: url(":/qss_icons/dark/rc/arrow_left_disabled.png");}QTabBar QToolButton::right-arrow:enabled, QDockWidget QTabBar QToolButton::right-arrow:enabled {image: url(":/qss_icons/dark/rc/arrow_right.png");}QTabBar QToolButton::right-arrow:disabled, QDockWidget QTabBar QToolButton::right-arrow:disabled {image: url(":/qss_icons/dark/rc/arrow_right_disabled.png");}QSplitter {background-color: #455364;  spacing: 0px;  padding: 0px;  margin: 0px;}QSplitter::handle {background-color: #455364;  border: 0px solid #19232D;  spacing: 0px;  padding: 1px;  margin: 0px;}QSplitter::handle:hover {background-color: #9DA9B5;}QSplitter::handle:horizontal {width: 5px;  image: url(":/qss_icons/dark/rc/line_vertical.png");}QSplitter::handle:vertical {height: 5px;  image: url(":/qss_icons/dark/rc/line_horizontal.png");}
QAbstractSpinBox{background-color: #19232D;border: 1px solid #455364;color: #DFE1E2;
padding-top: 2px;
padding-bottom: 2px;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 4px;}