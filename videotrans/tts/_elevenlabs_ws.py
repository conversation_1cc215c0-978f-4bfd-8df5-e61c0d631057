import asyncio
import base64
import copy
import json
import os
import time
import threading
from pathlib import Path
from typing import Dict, Any

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config, ConfigValidator
from videotrans.tts._base import BaseTTS
from videotrans.util import tools


class ElevenLabsWebSocketTTS(BaseTTS):
    """
    ElevenLabs WebSocket TTS implementation using multi-context streaming
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.copydata = copy.deepcopy(self.queue_tts)
        self.api_key = enhanced_config.get_api_key('elevenlabstts')
        self.model_id = enhanced_config.config_manager.get('elevenlabstts_models', 'eleven_turbo_v2_5')
        self.voice_id = None
        self.websocket = None
        self.audio_chunks = {}  # Store audio chunks by context_id
        self.completed_contexts = set()
        self.error_contexts = set()
        
        # Voice settings
        self.voice_settings = {
            "stability": 0.5,
            "similarity_boost": 0.8,
            "style": 0.0,
            "use_speaker_boost": True
        }
        
        # Apply rate adjustment if specified
        if self.rate and self.rate != '+0%':
            # Convert rate to speed multiplier (ElevenLabs uses speed, not rate)
            rate_value = float(self.rate.replace('%', '')) / 100.0
            self.voice_settings["speed"] = max(0.25, min(4.0, 1.0 + rate_value))

    def _get_voice_id_from_role(self, role: str) -> str:
        """Get voice ID from role using elevenlabs.json"""
        try:
            elevenlabs_json_path = os.path.join(config.ROOT_DIR, 'elevenlabs.json')
            if not Path(elevenlabs_json_path).exists():
                raise Exception("elevenlabs.json not found. Please configure ElevenLabs voices first.")
            
            with open(elevenlabs_json_path, 'r', encoding='utf-8') as f:
                voice_data = json.load(f)
            
            if role not in voice_data:
                raise Exception(f"Voice role '{role}' not found in elevenlabs.json")
            
            return voice_data[role]['voice_id']
        except Exception as e:
            config.logger.error(f"Error getting voice ID for role {role}: {e}")
            raise

    def _exec(self) -> None:
        """Execute TTS processing using WebSocket"""
        if not self.copydata:
            return
            
        # Get voice ID from the first item's role
        # first_role = self.copydata[0].get('role', '')
        self.voice_id = self.copydata[0].get('role', '')#self._get_voice_id_from_role(first_role)
        
        # Run the async WebSocket processing
        asyncio.run(self._process_websocket())

    async def _process_websocket(self) -> None:
        """Process all TTS items using WebSocket connection"""
        ws_url = f"wss://api.elevenlabs.io/v1/text-to-speech/{self.voice_id}/multi-stream-input"
        
        # Add query parameters
        params = {
            "model_id": self.model_id,
            "enable_logging": "true",
            "enable_ssml_parsing": "false",
            "output_format": "mp3_44100_128",
            "inactivity_timeout": "30",
            "sync_alignment": "false",
            "auto_mode": "false",
            "apply_text_normalization": "auto"
        }
        
        # Build query string
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        ws_url_with_params = f"{ws_url}?{query_string}"

        # Create headers with API key
        headers = [("xi-api-key", self.api_key)]

        try:
            async with websockets.connect(
                ws_url_with_params,
                additional_headers=headers,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                self.websocket = websocket
                
                # Start receiving messages
                receive_task = asyncio.create_task(self._receive_messages())
                
                # Process each TTS item
                for i, data_item in enumerate(self.copydata):
                    if self._exit():
                        break
                        
                    try:
                        await self._process_single_item(data_item, i)
                        # Small delay between items to avoid overwhelming the API
                        await asyncio.sleep(0.1)
                    except Exception as e:
                        config.logger.error(f"Error processing item {i}: {e}")
                        self.error = str(e)
                
                # Close the WebSocket connection
                await self._send_close_socket()
                
                # Wait for receive task to complete
                try:
                    await asyncio.wait_for(receive_task, timeout=10.0)
                except asyncio.TimeoutError:
                    config.logger.warning("Receive task timed out")
                    receive_task.cancel()
                    
        except Exception as e:
            config.logger.error(f"WebSocket connection error: {e}")
            self.error = str(e)
            raise

    async def _process_single_item(self, data_item: Dict[str, Any], index: int) -> None:
        """Process a single TTS item"""
        if tools.vail_file(data_item['filename']):
            self.has_done += 1
            self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
            return
            
        text = data_item['text'].strip()
        if not text:
            return
            
        context_id = f"ctx_{index}"
        self.audio_chunks[context_id] = []

        # Send text with voice settings and flush in one message
        await self._send_text_with_flush(text, context_id, is_first=True)

        # Wait for audio completion
        await self._wait_for_context_completion(context_id, data_item['filename'])

    async def _send_text_with_flush(self, text: str, context_id: str, is_first: bool = False) -> None:
        """Send text message with flush to generate audio immediately"""
        message = {
            "text": text,
            "context_id": context_id,
            "flush": True
        }

        # Include voice settings only for the first message of each context
        if is_first:
            message["voice_settings"] = self.voice_settings

        await self.websocket.send(json.dumps(message))
        config.logger.debug(f"Sent text+flush message for context {context_id}: {text[:50]}...")

    async def _send_text_message(self, text: str, context_id: str, is_first: bool = False) -> None:
        """Send text message to WebSocket"""
        message = {
            "text": text,
            "context_id": context_id
        }

        # Include voice settings only for the first message of each context
        if is_first:
            message["voice_settings"] = self.voice_settings

        await self.websocket.send(json.dumps(message))
        config.logger.debug(f"Sent text message for context {context_id}: {text[:50]}...")

    async def _send_flush_message(self, context_id: str) -> None:
        """Send flush message to generate audio for context"""
        message = {
            "context_id": context_id,
            "flush": True
        }
        await self.websocket.send(json.dumps(message))
        config.logger.debug(f"Sent flush message for context {context_id}")

    async def _send_close_socket(self) -> None:
        """Send close socket message"""
        message = {"close_socket": True}
        await self.websocket.send(json.dumps(message))
        config.logger.debug("Sent close socket message")

    async def _receive_messages(self) -> None:
        """Receive and process messages from WebSocket"""
        try:
            async for message in self.websocket:
                if self._exit():
                    break

                try:
                    config.logger.debug(f"Raw WebSocket message received: {message}")
                    data = json.loads(message)
                    await self._handle_received_message(data)
                except json.JSONDecodeError as e:
                    config.logger.error(f"Failed to decode WebSocket message: {e}")
                    config.logger.error(f"Raw message was: {message}")
                except Exception as e:
                    config.logger.error(f"Error handling received message: {e}")
                    config.logger.error(f"Message was: {message}")

        except ConnectionClosed:
            config.logger.info("WebSocket connection closed")
        except WebSocketException as e:
            config.logger.error(f"WebSocket error: {e}")
        except Exception as e:
            config.logger.error(f"Unexpected error in receive_messages: {e}")

    async def _handle_received_message(self, data: Dict[str, Any]) -> None:
        """Handle received message from WebSocket"""
        context_id = data.get("contextId") or data.get("context_id")

        if "audio" in data and data["audio"]:
            # Audio chunk received
            audio_base64 = data["audio"]
            try:
                audio_data = base64.b64decode(audio_base64)

                if context_id and context_id in self.audio_chunks:
                    self.audio_chunks[context_id].append(audio_data)
                    config.logger.debug(f"Received audio chunk for context {context_id}, size: {len(audio_data)}")
            except Exception as e:
                config.logger.error(f"Error decoding audio data for context {context_id}: {e}")

        elif data.get("isFinal") or data.get("is_final"):
            # Final message for context
            if context_id:
                self.completed_contexts.add(context_id)
                config.logger.debug(f"Context {context_id} completed")

        elif "error" in data:
            # Error message
            error_msg = data.get("error", "Unknown error")
            config.logger.error(f"WebSocket error: {error_msg}")
            if context_id:
                self.error_contexts.add(context_id)
            self.error = error_msg

        # Log all received messages for debugging
        config.logger.debug(f"Received WebSocket message: {data}")

    async def _wait_for_context_completion(self, context_id: str, filename: str, timeout: float = 60.0) -> None:
        """Wait for context to complete and save audio file"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self._exit():
                return

            if context_id in self.completed_contexts:
                # Save audio file
                if context_id in self.audio_chunks and self.audio_chunks[context_id]:
                    self._save_audio_file(self.audio_chunks[context_id], filename)
                    self.has_done += 1
                    self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
                    config.logger.debug(f"Saved audio for context {context_id} to {filename}")
                else:
                    config.logger.warning(f"No audio chunks received for context {context_id}")
                    # Still count as done even if no audio received
                    self.has_done += 1
                    self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
                return

            if context_id in self.error_contexts:
                config.logger.error(f"Error occurred for context {context_id}")
                # Count as done to avoid hanging
                self.has_done += 1
                self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
                return

            # Check if we have audio chunks even without final message
            if (context_id in self.audio_chunks and
                self.audio_chunks[context_id] and
                time.time() - start_time > 10.0):  # Wait at least 10 seconds
                config.logger.info(f"Saving audio for context {context_id} without final message")
                self._save_audio_file(self.audio_chunks[context_id], filename)
                self.has_done += 1
                self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
                return

            await asyncio.sleep(0.1)

        # Timeout - try to save any audio we have
        if context_id in self.audio_chunks and self.audio_chunks[context_id]:
            config.logger.warning(f"Timeout for context {context_id}, but saving available audio")
            self._save_audio_file(self.audio_chunks[context_id], filename)
            self.has_done += 1
            self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
        else:
            config.logger.error(f"Timeout waiting for context {context_id} completion")
            self.error = f"Timeout waiting for audio generation for context {context_id}"
            self.has_done += 1
            self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")

    def _save_audio_file(self, audio_chunks: list, filename: str) -> None:
        """Save audio chunks to file"""
        try:
            with open(filename, 'wb') as f:
                for chunk in audio_chunks:
                    f.write(chunk)
            config.logger.debug(f"Successfully saved audio file: {filename}")
        except Exception as e:
            config.logger.error(f"Error saving audio file {filename}: {e}")
            self.error = f"Error saving audio file: {e}"
