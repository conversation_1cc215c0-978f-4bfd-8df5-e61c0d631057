import copy
import json
import os
import re
import time

import httpx
import requests
from elevenlabs import ElevenLabs,VoiceSettings


from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config, ConfigValidator
from videotrans.tts._base import BaseTTS
from videotrans.util import tools


# 单个线程执行，防止远端限制

class ElevenLabsC(BaseTTS):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.copydata = copy.deepcopy(self.queue_tts)
        pro = self._set_proxy(type='set')
        if pro:
            self.proxies = pro

    def get_elevenlabs_token(self):
        """
        Get ElevenLabs access token using Firebase authentication.
        Flow:
        1. Try to refresh using stored refresh token
        2. If refresh fails, login with username/password
        3. Update database config with new tokens
        4. Return access token
        """
        try:
            firebase_api_key = enhanced_config.config_manager.get('elevenlabstts_firebase_api_key', '')
            if not firebase_api_key:
                raise Exception('Firebase API key not configured for ElevenLabs TTS')

            # First try to refresh using refresh token
            refresh_token = enhanced_config.config_manager.get('elevenlabstts_refresh_token', '')
            if refresh_token:
                try:
                    access_token = self._refresh_firebase_token(firebase_api_key, refresh_token)
                    if access_token:
                        # Update access token in config
                        enhanced_config.config_manager.set('elevenlabstts_access_token', access_token)
                        config.logger.info('ElevenLabs access token refreshed successfully')
                        return access_token
                except Exception as e:
                    config.logger.warning(f'Token refresh failed, trying login: {str(e)}')

            # If refresh failed, try login with username/password
            username = enhanced_config.config_manager.get('elevenlabstts_username', '')
            password = enhanced_config.config_manager.get('elevenlabstts_password', '')

            if not username or not password:
                raise Exception('ElevenLabs username/password not configured')

            # Login with Firebase
            login_response = self._firebase_login(firebase_api_key, username, password)

            # Update tokens in database config
            enhanced_config.config_manager.set('elevenlabstts_access_token', login_response['idToken'])
            enhanced_config.config_manager.set('elevenlabstts_refresh_token', login_response['refreshToken'])

            config.logger.info('ElevenLabs tokens updated successfully via login')
            return login_response['idToken']

        except Exception as e:
            error_msg = f'Failed to get ElevenLabs token: {str(e)}'
            config.logger.error(error_msg)
            raise Exception(error_msg)

    def _refresh_firebase_token(self, api_key, refresh_token):
        """Refresh Firebase access token using refresh token"""
        url = f'https://securetoken.googleapis.com/v1/token?key={api_key}'

        payload = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token
        }

        headers = {
            'Content-Type': 'application/json',
            'Referer': 'https://elevenlabs.io'
        }

        response = requests.post(url, json=payload, headers=headers, proxies=self.proxies if hasattr(self, 'proxies') else None, timeout=30)

        if response.status_code == 200:
            data = response.json()
            # Update refresh token if provided
            if 'refresh_token' in data:
                enhanced_config.config_manager.set('elevenlabstts_refresh_token', data['refresh_token'])
            return data.get('access_token') or data.get('id_token')
        else:
            raise Exception(f'Token refresh failed: {response.status_code} {response.text}')

    def _firebase_login(self, api_key, email, password):
        """Login to Firebase using email/password"""
        url = f'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={api_key}'

        payload = {
            'email': email,
            'password': password,
            'returnSecureToken': True
        }

        headers = {
            'Content-Type': 'application/json',
            'Referer': 'https://elevenlabs.io'
        }

        response = requests.post(url, json=payload, headers=headers, proxies=self.proxies if hasattr(self, 'proxies') else None, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
            error_message = error_data.get('error', {}).get('message', response.text)
            raise Exception(f'Firebase login failed: {error_message}')

    # 强制单个线程执行，防止频繁并发失败
    def _exec(self):
        prev_text = None
        speed=1.0
        if self.rate and self.rate !='+0%':
            speed+=float(self.rate.replace('%',''))

        while len(self.copydata) > 0:
            if self._exit():
                return
            try:
                data_item = self.copydata.pop(0)
                # if tools.vail_file(data_item['filename']):
                #     prev_text = data_item['text']
                #     continue
            except:
                return

            text = data_item['text'].strip()
            role = data_item['role']

            # Retry counter for token refresh
            retry_count = 0
            max_retries = 2

            while retry_count <= max_retries:
                try:
                    client = ElevenLabs(
                        # api_key=enhanced_config.get_api_key('elevenlabstts'),
                        httpx_client=httpx.Client(proxy=self.proxies) if self.proxies else None
                    )

                    access_token = enhanced_config.config_manager.get('elevenlabstts_access_token', '12345')
                    response = client.text_to_speech.convert(
                        text=text,
                        voice_id=role,
                        model_id="eleven_v3",
                        # previous_text=prev_text,
                        output_format="mp3_44100_128",
                        # next_text=self.copydata[0]['text'] if len(self.copydata) > 0 else None,
                        apply_text_normalization='auto',
                        voice_settings=VoiceSettings(
                            speed=speed,
                            stability=0,
                            similarity_boost=0,
                            style=0,
                            use_speaker_boost=True
                        ),
                        request_options={
                            "additional_headers": {
                                "Authorization": f"Bearer {access_token}"
                            }
                        },
                    )
                    with open(data_item['filename'], 'wb') as f:
                        for chunk in response:
                            if chunk:
                                f.write(chunk)
                    if self.inst and self.inst.precent < 80:
                        self.inst.precent += 0.1
                    self.error = ''
                    self.has_done += 1
                    prev_text = text
                    break  # Success, exit retry loop

                except Exception as e:
                    error = str(e)
                    print(error)
                    config.logger.error(error)
                    self.error = error

                    # Check for invalid authorization header error
                    if "invalid_authorization_header" in error.lower() and retry_count < max_retries:
                        try:
                            config.logger.info(f'Attempting to refresh ElevenLabs token (attempt {retry_count + 1})')
                            new_token = self.get_elevenlabs_token()
                            if new_token:
                                retry_count += 1
                                continue  # Retry with new token
                        except Exception as token_error:
                            config.logger.error(f'Token refresh failed: {str(token_error)}')
                            break  # Exit retry loop on token refresh failure

                    # Handle rate limiting
                    if error and re.search(r'rate|limit', error, re.I) is not None:
                        self._signal(
                            text='超过频率限制，等待60s后重试' if config.defaulelang == 'zh' else 'Frequency limit exceeded, wait 60s and retry')
                        self.copydata.append(data_item)
                        time.sleep(60)
                        break  # Exit retry loop and continue with next item

                    # For other errors, exit retry loop
                    break

            # Signal progress update
            self._signal(text=f'{config.transobj["kaishipeiyin"]} {self.has_done}/{self.len}')
            time.sleep(self.wait_sec)


class ElevenLabsClone():

    def __init__(self, input_file_path, output_file_path, source_language, target_language):
        self.input_file_path = input_file_path
        self.output_file_path = output_file_path
        self.source_language = source_language
        self.target_language = target_language
        pro = self._set_proxy(type='set')
        if pro:
            self.proxies =  pro
        self.client = ElevenLabs(
            api_key=enhanced_config.get_api_key('elevenlabstts'),
            httpx_client=httpx.Client(proxy=self.proxies) if pro else None
        )

    def _set_proxy(self, type='set'):
        if type == 'del' and self.shound_del:
            try:
                del os.environ['http_proxy']
                del os.environ['https_proxy']
                del os.environ['all_proxy']
            except:
                pass
            self.shound_del = False
            return

        if type == 'set':
            raw_proxy = os.environ.get('http_proxy') or os.environ.get('https_proxy')
            if raw_proxy:
                return raw_proxy
            if not raw_proxy:
                proxy = tools.set_proxy()
                if proxy:
                    self.shound_del = True
                    os.environ['http_proxy'] = proxy
                    os.environ['https_proxy'] = proxy
                    os.environ['all_proxy'] = proxy
                return proxy
        return None

    # 强制单个线程执行，防止频繁并发失败
    def run(self):
        # 转为mp3发送
        mp3_audio = config.TEMP_DIR + f'/elevlabs-clone-{time.time()}.mp3'
        tools.runffmpeg(['-y', '-i', self.input_file_path, mp3_audio])
        with open(mp3_audio, "rb") as audio_file:
            response = self.client.dubbing.dub_a_video_or_an_audio_file(
                file=(os.path.basename(mp3_audio), audio_file, "audio/mpeg"),
                target_lang=self.target_language[:2],
                source_lang='auto' if self.source_language == 'auto' else self.source_language[:2],
                num_speakers=0,
                watermark=False  # reduces the characters used if enabled, only works for videos not audio
            )

        dubbing_id = response.dubbing_id
        if self.wait_for_dubbing_completion(dubbing_id):
            # 返回为mp3数据，转为 原m4a格式
            with open(self.output_file_path + ".mp3", "wb") as file:
                for chunk in self.client.dubbing.get_dubbed_file(dubbing_id, self.target_language):
                    file.write(chunk)
            tools.runffmpeg(['-y', '-i', self.output_file_path + ".mp3", self.output_file_path])
            return True
        raise Exception('Dubbing Timeout ')

    def wait_for_dubbing_completion(self, dubbing_id: str) -> bool:
        MAX_ATTEMPTS = 120
        CHECK_INTERVAL = 10  # In seconds
        for _ in range(MAX_ATTEMPTS):
            metadata = self.client.dubbing.get_dubbing_project_metadata(dubbing_id)
            if metadata.status == "dubbed":
                return True
            elif metadata.status == "dubbing":
                print(
                    "Dubbing in progress... Will check status again in",
                    CHECK_INTERVAL,
                    "seconds.",
                )
                time.sleep(CHECK_INTERVAL)
            else:
                raise Exception("Dubbing failed:", metadata.error_message)

        raise Exception("Dubbing timed out")
