import json
import time
import threading
import requests
from pathlib import Path
from typing import Union, Dict

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config, ConfigValidator
from videotrans.tts._base import BaseTTS
from videotrans.util import tools


class AusyncLabTTS(BaseTTS):
    """
    AusyncLab TTS implementation with rate limiting (1 request per second)
    """

    # Class-level rate limiting variables
    _rate_limit_lock = threading.Lock()
    _last_request_time = 0

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.api_url = "https://api.ausynclab.org/api/v1/speech"
        self.headers = {
            "X-API-Key": enhanced_config.get_api_key('ausynclab'),
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # Language mapping from pyvideotrans to AusyncLab
        self.language_map = {
            'en': 'en',
            'zh': 'zh', 
            'zh-cn': 'zh',
            'zh-tw': 'zh',
            'vi': 'vi',
            'de': 'de',
            'fr': 'fr',
            'ko': 'ko',
            'ja': 'ja',
            'it': 'it',
            'pt': 'pt',
            'pl': 'pl',
            'es': 'es',
            'nl': 'nl',
            'th': 'th',
            'ms': 'ms',
            'hi': 'hi',
            'ar': 'ar',
            'he': 'he'
        }

    def _rate_limit_wait(self) -> None:
        """
        Ensure minimum 1 second interval between API requests to prevent rate limiting.
        Thread-safe implementation using class-level lock.
        """
        with self._rate_limit_lock:
            current_time = time.time()
            time_since_last_request = current_time - self._last_request_time

            if time_since_last_request < 1.0:
                sleep_time = 1.5 - time_since_last_request
                config.logger.info(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

            # Update last request time
            self.__class__._last_request_time = time.time()

    def _exec(self) -> None:
        """Execute TTS processing - force single thread to prevent parallel processing limits"""
        # Force single-threaded execution to prevent parallel processing limit errors
        self.dub_nums = 1
        self._local_mul_thread()

    def _item_task(self, data_item: Union[Dict, None]) -> Union[bool, None]:
        """Process individual TTS item"""
        if self._exit():
            return
        
        if not data_item or tools.vail_file(data_item['filename']):
            return
            
        try:
            text = data_item['text'].strip()
            if not text:
                return
                
            # Get voice ID from role
            voice_id = self._get_voice_id(data_item.get('role', ''))
            if not voice_id:
                self.error = f"Invalid voice role: {data_item.get('role', '')}"
                return False
                
            # Get language code
            language = self.language_map.get(self.language[:2], 'en')
            
            # Create TTS request
            audio_name = f"pyvideotrans_{int(time.time())}"
            
            # Since AusyncLab uses callback, we need to implement polling
            audio_id = self._create_tts_job(text, voice_id, language, audio_name)
            if not audio_id:
                return False
                
            # Poll for completion
            audio_url = self._wait_for_completion(audio_id)
            if not audio_url:
                return False
                
            # Download audio file
            success = self._download_audio(audio_url, data_item['filename'])
            if success:
                self.has_done += 1
                self._signal(text=f"{config.transobj['kaishipeiyin']} {self.has_done}/{self.len}")
                
        except Exception as e:
            self.error = str(e)
            config.logger.error(f"AusyncLab TTS error: {e}")
            return False

    def _get_voice_id(self, role: str) -> int:
        """Get voice ID from role name"""
        if not role:
            return 1

        # Try to parse as integer first (direct voice ID)
        try:
            return int(role)
        except (ValueError, TypeError):
            pass

        # Extract ID from format "Voice Name (ID: 123)"
        import re
        id_match = re.search(r'\(ID:\s*(\d+)\)', role)
        if id_match:
            return int(id_match.group(1))

        # Default voice IDs based on common voices
        voice_mapping = {
            'default': 1,
            'female': 1,
            'male': 2,
        }

        # Use mapping or default
        return voice_mapping.get(role.lower(), 1)

    def _create_tts_job(self, text: str, voice_id: int, language: str, audio_name: str, retry_count: int = 0) -> int:
        """Create TTS job and return audio_id"""
        max_retries = 3

        try:
            # Apply rate limiting before making the request
            self._rate_limit_wait()

            # For callback URL, we'll use a dummy URL since we're polling instead
            callback_url = "https://dummy-callback.example.com"

            payload = {
                "audio_name": audio_name,
                "text": text,
                "voice_id": voice_id,
                "speed": 1.0,
                "model_name": "myna-2",
                "language": language,
                "callback_url": callback_url
            }

            response = requests.post(
                f"{self.api_url}/text-to-speech",
                headers=self.headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("status") == 200:
                    return result.get("result", {}).get("audio_id")

            # Handle rate limit errors
            if response.status_code == 403:
                try:
                    result = response.json()
                    if (result.get("status") == 403 and
                        result.get("result", {}).get("detail", {}).get("error_code") == "parallel_process_limit"):

                        if retry_count < max_retries:
                            wait_time = (retry_count + 1) * 5  # Progressive backoff: 5, 10, 15 seconds
                            config.logger.info(f"Parallel process limit reached, retrying in {wait_time} seconds (attempt {retry_count + 1}/{max_retries})")
                            time.sleep(wait_time)
                            return self._create_tts_job(text, voice_id, language, audio_name, retry_count + 1)
                        else:
                            self.error = "Parallel process limit exceeded after maximum retries"
                            return None
                except (ValueError, KeyError):
                    pass

            self.error = f"TTS job creation failed: {response.status_code} - {response.text}"
            return None

        except Exception as e:
            self.error = f"Error creating TTS job: {str(e)}"
            return None

    def _wait_for_completion(self, audio_id: int, max_wait: int = 300) -> str:
        """Wait for TTS job completion and return audio URL"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if self._exit():
                return None
                
            try:
                # Apply rate limiting before making the status check request
                self._rate_limit_wait()

                response = requests.get(
                    f"{self.api_url}/{audio_id}",
                    headers=self.headers,
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 200:
                        audio_data = result.get("result", {})
                        state = audio_data.get("state")

                        if state == "SUCCEED":
                            return audio_data.get("audio_url")
                        elif state == "FAILED":
                            self.error = "TTS job failed"
                            return None
                        # If still processing, continue polling

                # Handle rate limit errors during status check
                elif response.status_code == 403:
                    try:
                        result = response.json()
                        if (result.get("status") == 403 and
                            result.get("result", {}).get("detail", {}).get("error_code") == "parallel_process_limit"):
                            config.logger.info("Parallel process limit during status check, waiting 5 seconds")
                            time.sleep(5)
                            continue
                    except (ValueError, KeyError):
                        pass

            except Exception as e:
                config.logger.error(f"Error polling TTS job: {e}")
                # Rate limiting will handle the delay, no additional sleep needed
                
        self.error = "TTS job timeout"
        return None

    def _download_audio(self, audio_url: str, output_path: str) -> bool:
        """Download audio file from URL and convert to MP3 if needed"""
        try:
            response = requests.get(audio_url, timeout=60)
            if response.status_code != 200:
                self.error = f"Failed to download audio: {response.status_code}"
                return False

            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            temp_wav_path = output_path + ".wav"
            with open(temp_wav_path, 'wb') as f:
                f.write(response.content)

            # Convert WAV to MP3 using existing utility
            tools.wav2mp3(temp_wav_path, output_path)

            # Clean up temporary WAV file
            try:
                Path(temp_wav_path).unlink(missing_ok=True)
            except Exception:
                pass
            return True

        except Exception as e:
            self.error = f"Error downloading audio: {str(e)}"
            return False
