"""
Authentication routes
"""
import os
import secrets
from urllib.parse import urlparse, urljoin
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, session, current_app
from flask_login import login_user, logout_user, login_required, current_user
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from google_auth_oauthlib.flow import Flow

from .forms import LoginForm, RegistrationForm, ProfileForm, ChangePasswordForm
from .utils import create_user, authenticate_user, get_user_by_google_id, generate_jwt_token
from videotrans.database.models import User, DatabaseManager
from videotrans.configure.enhanced_config import enhanced_config

# Create blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')


def is_safe_url(target):
    """Check if URL is safe for redirect"""
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login route for both web and API"""
    if current_user.is_authenticated:
        return redirect(url_for('auth.profile'))

    # Handle JSON API requests
    if request.is_json:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({
                'success': False,
                'message': 'Username and password are required'
            }), 400

        user = authenticate_user(username, password)
        if user:
            # Generate JWT token for API access
            token = generate_jwt_token(user, expires_in=2592000)
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'token': token,
                'user': user.to_dict(),
                'api_key': user.api_key
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Invalid username or password'
            }), 401

    # Handle web form requests
    form = LoginForm()
    if form.validate_on_submit():
        user = authenticate_user(form.username.data, form.password.data)
        if user:
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            if not next_page or not is_safe_url(next_page):
                next_page = url_for('auth.profile')
            return redirect(next_page)
        flash('Invalid username or password', 'error')

    return render_template('auth/login.html', form=form)


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Registration route for both web and API"""
    if current_user.is_authenticated:
        return redirect(url_for('auth.profile'))

    if enhanced_config.get('auth_register_disabled', 'false') == '1':
      return jsonify({
        'success': False,
        'message': 'Contact admin please'
      }), 400

    # Handle JSON API requests
    if request.is_json:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        first_name = data.get('first_name', '')
        last_name = data.get('last_name', '')

        if not username or not email or not password:
            return jsonify({
                'success': False,
                'message': 'Username, email, and password are required'
            }), 400

        user = create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )

        if user:
            # Generate JWT token for API access
            token = generate_jwt_token(user, expires_in=2592000)
            return jsonify({
                'success': True,
                'message': 'Registration successful',
                'token': token,
                'user': user.to_dict(),
                'api_key': user.api_key
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Username or email already exists'
            }), 400

    # Handle web form requests
    form = RegistrationForm()
    if form.validate_on_submit():
        user = create_user(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data
        )

        if user:
            flash('Registration successful! You can now log in.', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('Username or email already exists', 'error')

    return render_template('auth/register.html', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """Logout route"""
    logout_user()

    # Handle JSON API requests
    if request.is_json:
        return jsonify({
            'success': True,
            'message': 'Logout successful'
        })

    flash('You have been logged out.', 'info')
    return redirect(url_for('auth.login'))


@auth_bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    # Handle JSON API requests
    if request.is_json:
        return jsonify({
            'success': True,
            'user': current_user.to_dict(include_sensitive=True)
        })

    return render_template('auth/profile.html', user=current_user)


@auth_bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile"""
    form = ProfileForm(current_user.email)

    if form.validate_on_submit():
        db_manager = DatabaseManager()
        session = db_manager.get_session()

        try:
            user = session.query(User).filter(User.id == current_user.id).first()
            user.first_name = form.first_name.data
            user.last_name = form.last_name.data
            user.email = form.email.data
            session.commit()
            flash('Profile updated successfully!', 'success')
            return redirect(url_for('auth.profile'))
        except Exception as e:
            session.rollback()
            flash('Error updating profile', 'error')
        finally:
            session.close()

    # Pre-populate form
    form.first_name.data = current_user.first_name
    form.last_name.data = current_user.last_name
    form.email.data = current_user.email

    return render_template('auth/edit_profile.html', form=form)


@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            db_manager = DatabaseManager()
            session = db_manager.get_session()

            try:
                user = session.query(User).filter(User.id == current_user.id).first()
                user.set_password(form.password.data)
                session.commit()
                flash('Password changed successfully!', 'success')
                return redirect(url_for('auth.profile'))
            except Exception as e:
                session.rollback()
                flash('Error changing password', 'error')
            finally:
                session.close()
        else:
            flash('Current password is incorrect', 'error')

    return render_template('auth/change_password.html', form=form)


@auth_bp.route('/api-key/regenerate', methods=['POST'])
@login_required
def regenerate_api_key():
    """Regenerate API key"""
    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        user = session.query(User).filter(User.id == current_user.id).first()
        new_api_key = user.generate_api_key()
        session.commit()

        if request.is_json:
            return jsonify({
                'success': True,
                'api_key': new_api_key,
                'message': 'API key regenerated successfully'
            })

        flash('API key regenerated successfully!', 'success')
        return redirect(url_for('auth.profile'))

    except Exception as e:
        session.rollback()
        if request.is_json:
            return jsonify({
                'success': False,
                'message': 'Error regenerating API key'
            }), 500
        flash('Error regenerating API key', 'error')
        return redirect(url_for('auth.profile'))
    finally:
        session.close()


@auth_bp.route('/google')
def google_login():
    """Initiate Google OAuth login"""
    # Google OAuth configuration
    google_client_id = current_app.config.get('GOOGLE_CLIENT_ID')
    google_client_secret = current_app.config.get('GOOGLE_CLIENT_SECRET')

    if not google_client_id or not google_client_secret:
        if request.is_json:
            return jsonify({
                'success': False,
                'message': 'Google OAuth not configured'
            }), 500
        flash('Google OAuth not configured', 'error')
        return redirect(url_for('auth.login'))

    # Create flow instance
    flow = Flow.from_client_config(
        {
            "web": {
                "client_id": google_client_id,
                "client_secret": google_client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [url_for('auth.google_callback', _external=True)]
            }
        },
        scopes=['openid', 'email', 'profile']
    )

    flow.redirect_uri = url_for('auth.google_callback', _external=True)

    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true'
    )

    # Store state in session
    session['state'] = state

    return redirect(authorization_url)


@auth_bp.route('/google/callback')
def google_callback():
    """Handle Google OAuth callback"""
    # Verify state parameter
    if request.args.get('state') != session.get('state'):
        flash('Invalid state parameter', 'error')
        return redirect(url_for('auth.login'))

    # Google OAuth configuration
    google_client_id = current_app.config.get('GOOGLE_CLIENT_ID')
    google_client_secret = current_app.config.get('GOOGLE_CLIENT_SECRET')

    try:
        # Create flow instance
        flow = Flow.from_client_config(
            {
                "web": {
                    "client_id": google_client_id,
                    "client_secret": google_client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [url_for('auth.google_callback', _external=True)]
                }
            },
            scopes=['openid', 'email', 'profile']
        )

        flow.redirect_uri = url_for('auth.google_callback', _external=True)

        # Fetch token
        flow.fetch_token(authorization_response=request.url)

        # Get user info from Google
        credentials = flow.credentials
        request_session = google_requests.Request()

        # Verify the token
        id_info = id_token.verify_oauth2_token(
            credentials.id_token, request_session, google_client_id
        )

        google_id = id_info.get('sub')
        email = id_info.get('email')
        first_name = id_info.get('given_name', '')
        last_name = id_info.get('family_name', '')
        profile_picture = id_info.get('picture', '')

        # Check if user exists
        user = get_user_by_google_id(google_id)

        if not user:
            # Check if user exists with same email
            db_manager = DatabaseManager()
            session_db = db_manager.get_session()
            try:
                existing_user = session_db.query(User).filter_by(email=email).first()
                if existing_user:
                    # Link Google account to existing user
                    existing_user.google_id = google_id
                    existing_user.google_email = email
                    if not existing_user.profile_picture:
                        existing_user.profile_picture = profile_picture
                    existing_user.email_verified = True
                    session_db.commit()
                    user = existing_user
                else:
                    # Create new user
                    username = email.split('@')[0]  # Use email prefix as username
                    # Ensure username is unique
                    counter = 1
                    original_username = username
                    while session_db.query(User).filter_by(username=username).first():
                        username = f"{original_username}{counter}"
                        counter += 1

                    user = create_user(
                        username=username,
                        email=email,
                        first_name=first_name,
                        last_name=last_name,
                        google_id=google_id,
                        google_email=email,
                        profile_picture=profile_picture
                    )
            finally:
                session_db.close()

        if user:
            login_user(user, remember=True)
            flash('Successfully logged in with Google!', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('Error creating user account', 'error')
            return redirect(url_for('auth.login'))

    except Exception as e:
        print(f"Google OAuth error: {e}")
        flash('Error during Google authentication', 'error')
        return redirect(url_for('auth.login'))
