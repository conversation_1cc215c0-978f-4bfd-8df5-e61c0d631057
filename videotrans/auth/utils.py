"""
Authentication utilities
"""
import secrets
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import current_app
from flask_login import login_user
from videotrans.database.models import User, DatabaseManager
from sqlalchemy.sql import func


def create_user(username: str, email: str, password: str = None,
                first_name: str = None, last_name: str = None,
                google_id: str = None, google_email: str = None,
                profile_picture: str = None) -> Optional[User]:
    """
    Create a new user in the database

    Args:
        username: Username for the user
        email: Email address
        password: Password (for username/password auth)
        first_name: First name
        last_name: Last name
        google_id: Google user ID (for OAuth)
        google_email: Google email (for OAuth)
        profile_picture: URL to profile picture

    Returns:
        User object if successful, None otherwise
    """
    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        # Check if user already exists
        existing_user = session.query(User).filter(
            (User.username == username) | (User.email == email)
        ).first()

        if existing_user:
            return None

        # Create new user
        user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            google_id=google_id,
            google_email=google_email,
            profile_picture=profile_picture,
            is_active=True,
            email_verified=bool(google_id)  # Auto-verify if from Google
        )

        # Set password if provided
        if password:
            user.set_password(password)

        # Generate API key
        user.generate_api_key()

        session.add(user)
        session.commit()
        session.refresh(user)

        return user

    except Exception as e:
        session.rollback()
        print(f"Error creating user: {e}")
        return None
    finally:
        session.close()


def authenticate_user(username_or_email: str, password: str) -> Optional[User]:
    """
    Authenticate user with username/email and password

    Args:
        username_or_email: Username or email address
        password: Password

    Returns:
        User object if authentication successful, None otherwise
    """
    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        # Find user by username or email
        user = session.query(User).filter(
            ((User.username == username_or_email) | (User.email == username_or_email)) &
            (User.is_active == True)
        ).first()

        if user and user.check_password(password):
            # Update last login time
            user.last_login_at = func.now()
            session.commit()

            # Create a detached copy of the user to avoid session issues
            user_dict = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'api_key': user.api_key,
                'google_id': user.google_id,
                'profile_picture': user.profile_picture,
                'created_at': user.created_at,
                'updated_at': user.updated_at,
                'last_login_at': user.last_login_at
            }

            # Create a new user object that's not bound to the session
            detached_user = User()
            for key, value in user_dict.items():
                setattr(detached_user, key, value)

            return detached_user

        return None

    except Exception as e:
        print(f"Error authenticating user: {e}")
        return None
    finally:
        session.close()


def get_user_by_api_key(api_key: str) -> Optional[User]:
    """
    Get user by API key

    Args:
        api_key: API key

    Returns:
        User object if found and active, None otherwise
    """
    if not api_key:
        return None

    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        user = session.query(User).filter(
            (User.api_key == api_key) & (User.is_active == True)
        ).first()

        if user:
            # Update last login time
            user.last_login_at = func.now()
            session.commit()

            # Create a detached copy of the user to avoid session issues
            user_dict = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'api_key': user.api_key,
                'google_id': user.google_id,
                'profile_picture': user.profile_picture,
                'created_at': user.created_at,
                'updated_at': user.updated_at,
                'last_login_at': user.last_login_at
            }

            # Create a new user object that's not bound to the session
            detached_user = User()
            for key, value in user_dict.items():
                setattr(detached_user, key, value)

            return detached_user

        return None

    except Exception as e:
        print(f"Error getting user by API key: {e}")
        return None
    finally:
        session.close()


def get_user_by_google_id(google_id: str) -> Optional[User]:
    """
    Get user by Google ID

    Args:
        google_id: Google user ID

    Returns:
        User object if found, None otherwise
    """
    if not google_id:
        return None

    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        user = session.query(User).filter(
            (User.google_id == google_id) & (User.is_active == True)
        ).first()

        return user

    except Exception as e:
        print(f"Error getting user by Google ID: {e}")
        return None
    finally:
        session.close()


def generate_jwt_token(user: User, expires_in: int = 3600) -> str:
    """
    Generate JWT token for user

    Args:
        user: User object
        expires_in: Token expiration time in seconds (default: 1 hour)

    Returns:
        JWT token string
    """
    payload = {
        'user_id': user.id,
        'username': user.username,
        'email': user.email,
        'exp': datetime.utcnow() + timedelta(seconds=expires_in),
        'iat': datetime.utcnow()
    }

    secret_key = current_app.config.get('SECRET_KEY', 'dev-secret-key')
    return jwt.encode(payload, secret_key, algorithm='HS256')


def verify_jwt_token(token: str) -> Optional[Dict[Any, Any]]:
    """
    Verify JWT token and return payload

    Args:
        token: JWT token string

    Returns:
        Token payload if valid, None otherwise
    """
    try:
        secret_key = current_app.config.get('SECRET_KEY', 'dev-secret-key')
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None


def get_user_by_id(user_id: int) -> Optional[User]:
    """
    Get user by ID

    Args:
        user_id: User ID

    Returns:
        User object if found and active, None otherwise
    """
    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        user = session.query(User).filter(
            (User.id == user_id) & (User.is_active == True)
        ).first()

        if user:
            # Create a detached copy of the user to avoid session issues
            user_dict = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'api_key': user.api_key,
                'google_id': user.google_id,
                'profile_picture': user.profile_picture,
                'created_at': user.created_at,
                'updated_at': user.updated_at,
                'last_login_at': user.last_login_at
            }

            # Create a new user object that's not bound to the session
            detached_user = User()
            for key, value in user_dict.items():
                setattr(detached_user, key, value)

            return detached_user

        return None

    except Exception as e:
        print(f"Error getting user by ID: {e}")
        return None
    finally:
        session.close()


def update_user_profile(user_id: int, **kwargs) -> bool:
    """
    Update user profile

    Args:
        user_id: User ID
        **kwargs: Fields to update

    Returns:
        True if successful, False otherwise
    """
    db_manager = DatabaseManager()
    session = db_manager.get_session()

    try:
        user = session.query(User).filter(User.id == user_id).first()
        if not user:
            return False

        # Update allowed fields
        allowed_fields = ['first_name', 'last_name', 'email', 'profile_picture']
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(user, field):
                setattr(user, field, value)

        user.updated_at = func.now()
        session.commit()
        return True

    except Exception as e:
        session.rollback()
        print(f"Error updating user profile: {e}")
        return False
    finally:
        session.close()
