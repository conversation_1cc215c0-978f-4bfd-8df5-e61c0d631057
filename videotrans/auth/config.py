"""
Authentication route configuration management
"""
from typing import Dict, Any
from videotrans.configure.enhanced_config import enhanced_config


class AuthRouteConfig:
    """Configuration manager for authentication routes"""
    
    # Configuration keys for different route groups
    CONFIG_KEYS = {
        'master': 'auth_routes_enabled',
        'login': 'auth_login_enabled', 
        'register': 'auth_register_enabled',
        'google_oauth': 'auth_google_oauth_enabled',
        'profile': 'auth_profile_enabled',
        'api_key': 'auth_api_key_enabled',
        'logout': 'auth_logout_enabled'
    }
    
    # Default values for all configurations (backward compatibility)
    DEFAULT_VALUES = {
        'auth_routes_enabled': True,
        'auth_login_enabled': True,
        'auth_register_enabled': True, 
        'auth_google_oauth_enabled': True,
        'auth_profile_enabled': True,
        'auth_api_key_enabled': True,
        'auth_logout_enabled': True
    }
    
    def __init__(self):
        self._ensure_default_configs()
    
    def _ensure_default_configs(self):
        """Ensure all default configurations are set in the database"""
        try:
            for key, default_value in self.DEFAULT_VALUES.items():
                current_value = enhanced_config.get_system_setting(key)
                if current_value is None:
                    enhanced_config.set_system_setting(
                        key=key,
                        value=default_value,
                        description=f"Enable/disable {key.replace('auth_', '').replace('_enabled', '')} authentication routes"
                    )
        except Exception as e:
            print(f"Warning: Could not set default auth route configurations: {e}")
    
    def is_enabled(self, route_group: str) -> bool:
        """Check if a route group is enabled"""
        try:
            # First check master switch
            master_enabled = enhanced_config.get_system_setting(
                self.CONFIG_KEYS['master'], 
                self.DEFAULT_VALUES['auth_routes_enabled']
            )
            
            if not master_enabled:
                return False
            
            # Then check specific route group
            if route_group in self.CONFIG_KEYS:
                config_key = self.CONFIG_KEYS[route_group]
                return enhanced_config.get_system_setting(
                    config_key,
                    self.DEFAULT_VALUES.get(config_key, True)
                )
            
            return True
            
        except Exception as e:
            print(f"Warning: Error checking auth route config for {route_group}: {e}")
            # Fallback to enabled for safety
            return True
    
    def get_enabled_routes(self) -> Dict[str, bool]:
        """Get status of all route groups"""
        return {
            route_group: self.is_enabled(route_group) 
            for route_group in self.CONFIG_KEYS.keys()
        }
    
    def set_route_enabled(self, route_group: str, enabled: bool) -> bool:
        """Enable or disable a route group"""
        try:
            if route_group in self.CONFIG_KEYS:
                config_key = self.CONFIG_KEYS[route_group]
                enhanced_config.set_system_setting(
                    key=config_key,
                    value=enabled,
                    description=f"Enable/disable {route_group} authentication routes"
                )
                return True
            return False
        except Exception as e:
            print(f"Error setting auth route config for {route_group}: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        try:
            enabled_routes = self.get_enabled_routes()
            return {
                'master_enabled': enabled_routes.get('master', True),
                'route_groups': {
                    group: status for group, status in enabled_routes.items() 
                    if group != 'master'
                },
                'total_enabled': sum(1 for status in enabled_routes.values() if status),
                'total_groups': len(self.CONFIG_KEYS)
            }
        except Exception as e:
            print(f"Error getting auth config summary: {e}")
            return {
                'master_enabled': True,
                'route_groups': {},
                'total_enabled': 0,
                'total_groups': 0,
                'error': str(e)
            }


# Global auth route configuration instance
auth_route_config = AuthRouteConfig()
