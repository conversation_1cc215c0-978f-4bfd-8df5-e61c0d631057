"""
Authentication forms for Flask-WTF
"""
import re
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, EqualTo, ValidationError
from videotrans.database.models import User, DatabaseManager


class EmailValidator:
    """Custom email validator that doesn't depend on email_validator package"""

    def __init__(self, message=None):
        self.message = message or "Please enter a valid email address"

    def __call__(self, form, field):
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not field.data or not re.match(email_pattern, field.data):
            raise ValidationError(self.message)


class LoginForm(FlaskForm):
    """Login form for username/password authentication"""
    username = StringField('Username or Email', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')


class RegistrationForm(FlaskForm):
    """Registration form for new users"""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=80, message="Username must be between 3 and 80 characters")
    ])
    email = StringField('Email', validators=[
        DataRequired(),
        EmailValidator(message="Please enter a valid email address"),
        Length(max=120)
    ])
    first_name = StringField('First Name', validators=[Length(max=50)])
    last_name = StringField('Last Name', validators=[Length(max=50)])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=6, message="Password must be at least 6 characters long")
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(),
        EqualTo('password', message="Passwords must match")
    ])
    submit = SubmitField('Register')

    def validate_username(self, username):
        """Check if username is already taken"""
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        try:
            user = session.query(User).filter_by(username=username.data).first()
            if user:
                raise ValidationError('Username already taken. Please choose a different one.')
        finally:
            session.close()

    def validate_email(self, email):
        """Check if email is already registered"""
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        try:
            user = session.query(User).filter_by(email=email.data).first()
            if user:
                raise ValidationError('Email already registered. Please choose a different one.')
        finally:
            session.close()


class PasswordResetRequestForm(FlaskForm):
    """Form to request password reset"""
    email = StringField('Email', validators=[DataRequired(), EmailValidator()])
    submit = SubmitField('Request Password Reset')


class PasswordResetForm(FlaskForm):
    """Form to reset password with token"""
    password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=6, message="Password must be at least 6 characters long")
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(),
        EqualTo('password', message="Passwords must match")
    ])
    submit = SubmitField('Reset Password')


class ProfileForm(FlaskForm):
    """Form to update user profile"""
    first_name = StringField('First Name', validators=[Length(max=50)])
    last_name = StringField('Last Name', validators=[Length(max=50)])
    email = StringField('Email', validators=[
        DataRequired(),
        EmailValidator(message="Please enter a valid email address"),
        Length(max=120)
    ])
    submit = SubmitField('Update Profile')

    def __init__(self, original_email, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email

    def validate_email(self, email):
        """Check if email is already registered (excluding current user)"""
        if email.data != self.original_email:
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            try:
                user = session.query(User).filter_by(email=email.data).first()
                if user:
                    raise ValidationError('Email already registered. Please choose a different one.')
            finally:
                session.close()


class ChangePasswordForm(FlaskForm):
    """Form to change password"""
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=6, message="Password must be at least 6 characters long")
    ])
    password2 = PasswordField('Repeat New Password', validators=[
        DataRequired(),
        EqualTo('password', message="Passwords must match")
    ])
    submit = SubmitField('Change Password')
