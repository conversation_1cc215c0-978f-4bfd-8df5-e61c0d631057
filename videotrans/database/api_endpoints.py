"""
API endpoints for configuration management
"""
import json
from typing import Dict, Any, Optional
from flask import Blueprint, request, jsonify

from .config_manager import get_config_manager
from .config_watcher import get_realtime_config_manager
from .models import Setting, SettingCategory, ConfigHistory, db_manager
from .migrations import ConfigMigrator


# Create blueprint for configuration API
config_api = Blueprint('config_api', __name__, url_prefix='/api/config')


@config_api.route('/settings', methods=['GET'])
def get_all_settings():
    """Get all configuration settings"""
    try:
        config_manager = get_config_manager()
        settings = config_manager.get_all()
        return jsonify({
            'success': True,
            'data': settings,
            'count': len(settings)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/settings/<key>', methods=['GET'])
def get_setting(key: str):
    """Get a specific configuration setting"""
    try:
        config_manager = get_config_manager()
        value = config_manager.get(key)
        if value is None:
            return jsonify({
                'success': False,
                'error': f'Setting {key} not found'
            }), 404

        return jsonify({
            'success': True,
            'data': {
                'key': key,
                'value': value
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/settings/<key>', methods=['PUT'])
def update_setting(key: str):
    """Update a configuration setting"""
    try:
        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({
                'success': False,
                'error': 'Value is required'
            }), 400
        
        value = data['value']
        category = data.get('category')
        description = data.get('description')
        data_type = data.get('data_type')
        is_sensitive = data.get('is_sensitive', False)
        
        # Use realtime config manager for immediate updates
        realtime_manager = get_realtime_config_manager()
        realtime_manager.set(
            key=key,
            value=value,
            category=category,
            description=description,
            data_type=data_type,
            is_sensitive=is_sensitive
        )
        
        return jsonify({
            'success': True,
            'message': f'Setting {key} updated successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/settings/<key>', methods=['DELETE'])
def delete_setting(key: str):
    """Delete a configuration setting"""
    try:
        # Use realtime config manager for immediate updates
        realtime_manager = get_realtime_config_manager()
        success = realtime_manager.delete(key)
        if not success:
            return jsonify({
                'success': False,
                'error': f'Setting {key} not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': f'Setting {key} deleted successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/settings/batch', methods=['PUT'])
def update_multiple_settings():
    """Update multiple configuration settings"""
    try:
        data = request.get_json()
        if not data or 'settings' not in data:
            return jsonify({
                'success': False,
                'error': 'Settings data is required'
            }), 400
        
        settings = data['settings']
        updated_count = 0
        
        # Use realtime config manager for immediate updates
        realtime_manager = get_realtime_config_manager()

        for setting in settings:
            if 'key' not in setting or 'value' not in setting:
                continue

            realtime_manager.set(
                key=setting['key'],
                value=setting['value'],
                category=setting.get('category'),
                description=setting.get('description'),
                data_type=setting.get('data_type'),
                is_sensitive=setting.get('is_sensitive', False)
            )
            updated_count += 1
        
        return jsonify({
            'success': True,
            'message': f'Updated {updated_count} settings successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/categories', methods=['GET'])
def get_categories():
    """Get all setting categories"""
    try:
        with db_manager.get_session() as session:
            categories = session.query(SettingCategory).all()
            category_data = []
            
            config_manager = get_config_manager()
            for category in categories:
                settings = config_manager.get_by_category(category.name)
                category_data.append({
                    'id': category.id,
                    'name': category.name,
                    'description': category.description,
                    'display_order': category.display_order,
                    'settings_count': len(settings)
                })
        
        return jsonify({
            'success': True,
            'data': category_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/categories/<category_name>/settings', methods=['GET'])
def get_settings_by_category(category_name: str):
    """Get all settings in a specific category"""
    try:
        config_manager = get_config_manager()
        settings = config_manager.get_by_category(category_name)
        return jsonify({
            'success': True,
            'data': settings,
            'category': category_name,
            'count': len(settings)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/backup', methods=['POST'])
def create_backup():
    """Create a backup of current configuration"""
    try:
        config_manager = get_config_manager()
        settings = config_manager.get_all()

        # Create backup data
        backup_data = {
            'version': '1.0',
            'timestamp': str(db_manager.get_session().execute("SELECT datetime('now')").fetchone()[0]),
            'settings': settings
        }

        return jsonify({
            'success': True,
            'data': backup_data,
            'message': 'Configuration backup created successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/restore', methods=['POST'])
def restore_backup():
    """Restore configuration from backup"""
    try:
        data = request.get_json()
        if not data or 'settings' not in data:
            return jsonify({
                'success': False,
                'error': 'Backup data is required'
            }), 400
        
        settings = data['settings']
        restored_count = 0

        config_manager = get_config_manager()
        for key, value in settings.items():
            config_manager.set(key, value)
            restored_count += 1
        
        return jsonify({
            'success': True,
            'message': f'Restored {restored_count} settings successfully'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/migration/status', methods=['GET'])
def migration_status():
    """Check migration status"""
    try:
        migrator = ConfigMigrator()
        needs_migration = migrator.needs_migration()
        
        config_manager = get_config_manager()
        return jsonify({
            'success': True,
            'data': {
                'needs_migration': needs_migration,
                'database_available': config_manager._is_database_available()
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/migration/run', methods=['POST'])
def run_migration():
    """Run configuration migration"""
    try:
        migrator = ConfigMigrator()
        success = migrator.run_migration()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Migration completed successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Migration failed'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/history/<setting_key>', methods=['GET'])
def get_setting_history(setting_key: str):
    """Get change history for a specific setting"""
    try:
        with db_manager.get_session() as session:
            history = session.query(ConfigHistory).filter(
                ConfigHistory.setting_key == setting_key
            ).order_by(ConfigHistory.created_at.desc()).limit(50).all()
            
            history_data = []
            for record in history:
                history_data.append({
                    'id': record.id,
                    'old_value': record.old_value,
                    'new_value': record.new_value,
                    'changed_by': record.changed_by,
                    'change_reason': record.change_reason,
                    'created_at': record.created_at.isoformat()
                })
        
        return jsonify({
            'success': True,
            'data': history_data,
            'setting_key': setting_key
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/reload', methods=['POST'])
def reload_configuration():
    """Reload configuration from storage and notify all services"""
    try:
        # Reload configuration
        config_manager = get_config_manager()
        config_manager.reload()

        # Get realtime manager and trigger notifications
        realtime_manager = get_realtime_config_manager()

        # Force notification to all registered services
        all_settings = config_manager.get_all()
        notification_count = 0

        for key, value in all_settings.items():
            # Get setting details for category
            try:
                with db_manager.get_session() as session:
                    setting = session.query(Setting).filter(Setting.key == key).first()
                    category = setting.category if setting else None

                    # Trigger notification
                    realtime_manager.watcher.notifier.notify_change(key, None, value, category)
                    notification_count += 1
            except Exception:
                continue

        return jsonify({
            'success': True,
            'message': f'Configuration reloaded successfully. Notified {notification_count} settings.'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/services/reload', methods=['POST'])
def reload_services():
    """Force reload configuration for all registered services"""
    try:
        realtime_manager = get_realtime_config_manager()

        # Count registered services
        service_count = len(realtime_manager._service_instances)

        # Trigger reload for all services by sending fake notifications
        config_manager = get_config_manager()
        for key in ['chatgpt_key', 'deepl_authkey', 'azure_speech_key']:
            current_value = config_manager.get(key)
            if current_value:
                realtime_manager.watcher.notifier.notify_change(key, None, current_value, 'API Keys')

        return jsonify({
            'success': True,
            'message': f'Triggered reload for {service_count} registered services'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@config_api.route('/watcher/status', methods=['GET'])
def get_watcher_status():
    """Get configuration watcher status"""
    try:
        realtime_manager = get_realtime_config_manager()

        return jsonify({
            'success': True,
            'data': {
                'watcher_running': realtime_manager.watcher._running,
                'registered_services': len(realtime_manager._service_instances),
                'check_interval': realtime_manager.watcher.check_interval
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
