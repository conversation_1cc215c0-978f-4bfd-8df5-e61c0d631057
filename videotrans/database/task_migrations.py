"""
Database migration scripts for user-scoped task management and media library
"""
import os
import logging
from pathlib import Path
from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError

from .models import Base, db_manager, Task, TaskStage, TaskLog, Media, TaskMedia

logger = logging.getLogger(__name__)


class TaskDatabaseMigrator:
    """Handle database migrations for new task and media tables"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def check_table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database"""
        try:
            with self.db_manager.get_session() as session:
                inspector = inspect(session.bind)
                return table_name in inspector.get_table_names()
        except Exception as e:
            logger.error(f"Error checking table existence: {str(e)}")
            return False
    
    def check_column_exists(self, table_name: str, column_name: str) -> bool:
        """Check if a column exists in a table"""
        try:
            with self.db_manager.get_session() as session:
                inspector = inspect(session.bind)
                columns = [col['name'] for col in inspector.get_columns(table_name)]
                return column_name in columns
        except Exception as e:
            logger.error(f"Error checking column existence: {str(e)}")
            return False
    
    def create_all_tables(self):
        """Create all tables defined in models"""
        try:
            logger.info("Creating all database tables...")
            Base.metadata.create_all(self.db_manager.engine)
            logger.info("All tables created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {str(e)}")
            return False
    
    def migrate_task_tables(self):
        """Create task-related tables if they don't exist"""
        logger.info("Migrating task management tables...")
        
        tables_to_create = [
            ('tasks', Task),
            ('task_stages', TaskStage),
            ('task_logs', TaskLog),
            ('task_media', TaskMedia)
        ]
        
        created_tables = []
        
        for table_name, model_class in tables_to_create:
            if not self.check_table_exists(table_name):
                try:
                    logger.info(f"Creating table: {table_name}")
                    model_class.__table__.create(self.db_manager.engine)
                    created_tables.append(table_name)
                    logger.info(f"Table {table_name} created successfully")
                except Exception as e:
                    logger.error(f"Error creating table {table_name}: {str(e)}")
                    return False, created_tables
            else:
                logger.info(f"Table {table_name} already exists")
        
        return True, created_tables
    
    def migrate_media_tables(self):
        """Create media library tables if they don't exist"""
        logger.info("Migrating media library tables...")
        
        tables_to_create = [
            ('media', Media)
        ]
        
        created_tables = []
        
        for table_name, model_class in tables_to_create:
            if not self.check_table_exists(table_name):
                try:
                    logger.info(f"Creating table: {table_name}")
                    model_class.__table__.create(self.db_manager.engine)
                    created_tables.append(table_name)
                    logger.info(f"Table {table_name} created successfully")
                except Exception as e:
                    logger.error(f"Error creating table {table_name}: {str(e)}")
                    return False, created_tables
            else:
                logger.info(f"Table {table_name} already exists")
        
        return True, created_tables
    
    def create_indexes(self):
        """Create additional indexes for performance"""
        logger.info("Creating additional database indexes...")
        
        indexes = [
            # Task table indexes
            "CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON tasks(user_id, status)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_user_created ON tasks(user_id, created_at)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_user_type ON tasks(user_id, task_type)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_uuid ON tasks(uuid)",
            
            # TaskStage table indexes
            "CREATE INDEX IF NOT EXISTS idx_task_stages_task_name ON task_stages(task_uuid, stage_name)",
            "CREATE INDEX IF NOT EXISTS idx_task_stages_task_status ON task_stages(task_uuid, status)",
            
            # TaskLog table indexes
            "CREATE INDEX IF NOT EXISTS idx_task_logs_task_level ON task_logs(task_uuid, log_level)",
            "CREATE INDEX IF NOT EXISTS idx_task_logs_task_created ON task_logs(task_uuid, created_at)",
            
            # Media table indexes
            "CREATE INDEX IF NOT EXISTS idx_media_user_uploaded ON media(user_id, uploaded_at)",
            "CREATE INDEX IF NOT EXISTS idx_media_user_type ON media(user_id, mime_type)",
            "CREATE INDEX IF NOT EXISTS idx_media_user_deleted ON media(user_id, is_deleted)",
            "CREATE INDEX IF NOT EXISTS idx_media_hash ON media(file_hash)",
            
            # TaskMedia table indexes
            "CREATE INDEX IF NOT EXISTS idx_task_media_task ON task_media(task_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_task_media_media ON task_media(media_id)",
            "CREATE INDEX IF NOT EXISTS idx_task_media_type ON task_media(task_uuid, relationship_type)"
        ]
        
        created_indexes = []
        
        try:
            with self.db_manager.get_session() as session:
                for index_sql in indexes:
                    try:
                        session.execute(text(index_sql))
                        index_name = index_sql.split()[-1].split('(')[0]  # Extract index name
                        created_indexes.append(index_name)
                    except Exception as e:
                        logger.warning(f"Index creation warning: {str(e)}")
                session.commit()
            
            logger.info(f"Created {len(created_indexes)} indexes")
            return True, created_indexes
            
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")
            return False, created_indexes
    
    def run_full_migration(self):
        """Run complete migration process"""
        logger.info("Starting full task database migration...")
        
        migration_results = {
            'success': True,
            'created_tables': [],
            'created_indexes': [],
            'errors': []
        }
        
        try:
            # Initialize database first
            self.db_manager.initialize_database()
            
            # Step 1: Create task tables
            success, created_tables = self.migrate_task_tables()
            migration_results['created_tables'].extend(created_tables)
            if not success:
                migration_results['success'] = False
                migration_results['errors'].append("Failed to create task tables")
            
            # Step 2: Create media tables
            success, created_tables = self.migrate_media_tables()
            migration_results['created_tables'].extend(created_tables)
            if not success:
                migration_results['success'] = False
                migration_results['errors'].append("Failed to create media tables")
            
            # Step 3: Create indexes
            success, created_indexes = self.create_indexes()
            migration_results['created_indexes'].extend(created_indexes)
            if not success:
                migration_results['success'] = False
                migration_results['errors'].append("Failed to create indexes")
            
            if migration_results['success']:
                logger.info("Task database migration completed successfully")
            else:
                logger.error("Task database migration completed with errors")
            
            return migration_results
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            migration_results['success'] = False
            migration_results['errors'].append(str(e))
            return migration_results
    
    def rollback_migration(self, tables_to_drop: list = None):
        """Rollback migration by dropping created tables"""
        logger.warning("Rolling back task database migration...")
        
        if not tables_to_drop:
            tables_to_drop = ['task_media', 'task_logs', 'task_stages', 'tasks', 'media']
        
        dropped_tables = []
        
        try:
            with self.db_manager.get_session() as session:
                for table_name in tables_to_drop:
                    if self.check_table_exists(table_name):
                        try:
                            session.execute(text(f"DROP TABLE {table_name}"))
                            dropped_tables.append(table_name)
                            logger.info(f"Dropped table: {table_name}")
                        except Exception as e:
                            logger.error(f"Error dropping table {table_name}: {str(e)}")
                session.commit()
            
            logger.info(f"Rollback completed. Dropped {len(dropped_tables)} tables")
            return True, dropped_tables
            
        except Exception as e:
            logger.error(f"Rollback failed: {str(e)}")
            return False, dropped_tables
    
    def check_migration_needed(self):
        """Check if migration is needed"""
        task_tables = ['tasks', 'task_stages', 'task_logs', 'media', 'task_media']
        
        for table in task_tables:
            if not self.check_table_exists(table):
                return True
        
        return False


def run_task_migration():
    """Convenience function to run task migration"""
    migrator = TaskDatabaseMigrator()
    return migrator.run_full_migration()


def rollback_task_migration():
    """Convenience function to rollback task migration"""
    migrator = TaskDatabaseMigrator()
    return migrator.rollback_migration()


def check_task_migration_needed():
    """Check if task migration is needed"""
    migrator = TaskDatabaseMigrator()
    return migrator.check_migration_needed()


if __name__ == "__main__":
    # Run migration when script is executed directly
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        print("Rolling back task migration...")
        success, dropped = rollback_task_migration()
        if success:
            print(f"Rollback successful. Dropped tables: {dropped}")
        else:
            print("Rollback failed")
    elif len(sys.argv) > 1 and sys.argv[1] == "check":
        print("Checking if migration is needed...")
        needed = check_task_migration_needed()
        print(f"Migration needed: {needed}")
    else:
        print("Running task database migration...")
        result = run_task_migration()
        if result['success']:
            print("Migration successful!")
            print(f"Created tables: {result['created_tables']}")
            print(f"Created indexes: {len(result['created_indexes'])}")
        else:
            print("Migration failed!")
            print(f"Errors: {result['errors']}")
