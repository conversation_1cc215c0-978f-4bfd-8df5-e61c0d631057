"""
Database migration to add media_type column to media table
"""
import logging
from sqlalchemy import text

logger = logging.getLogger(__name__)


def migrate_add_media_type_column():
    """Add media_type column to media table and populate existing records"""
    try:
        # Import here to avoid circular imports
        from videotrans.database.models import db_manager
        from videotrans.util import tools

        with db_manager.get_session() as session:
            # Check if column already exists (PostgreSQL syntax)
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'media' AND column_name = 'media_type'
            """)).fetchone()
            
            if result.count > 0:
                logger.info("media_type column already exists, skipping migration")
                return True
            
            # Add the new column
            logger.info("Adding media_type column to media table...")
            session.execute(text("ALTER TABLE media ADD COLUMN media_type VARCHAR(20)"))
            
            # Create index for the new column
            logger.info("Creating index for media_type column...")
            session.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_media_user_media_type 
                ON media(user_id, media_type)
            """))
            
            session.commit()
            logger.info("Successfully added media_type column and index")
            
            # Now populate existing records
            logger.info("Populating media_type for existing records...")
            
            # Get all existing media records
            result = session.execute(text("""
                SELECT id, original_filename, mime_type 
                FROM media 
                WHERE media_type IS NULL
            """)).fetchall()
            
            updated_count = 0
            for row in result:
                media_id, original_filename, mime_type = row
                
                # Detect media type
                detected_type = tools.detect_media_type(original_filename, mime_type)
                
                # Update the record
                session.execute(text("""
                    UPDATE media 
                    SET media_type = :media_type 
                    WHERE id = :media_id
                """), {
                    'media_type': detected_type,
                    'media_id': media_id
                })
                
                updated_count += 1
                
                # Commit in batches of 100
                if updated_count % 100 == 0:
                    session.commit()
                    logger.info(f"Updated {updated_count} records...")
            
            # Final commit
            session.commit()
            logger.info(f"Successfully populated media_type for {updated_count} existing records")
            
            return True
            
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False


def rollback_media_type_column():
    """Remove media_type column (rollback migration)"""
    try:
        # Import here to avoid circular imports
        from videotrans.database.models import db_manager

        with db_manager.get_session() as session:
            # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
            logger.warning("Rolling back media_type column migration...")
            logger.warning("Note: SQLite doesn't support DROP COLUMN, manual intervention may be required")
            
            # For now, just set all media_type values to NULL
            session.execute(text("UPDATE media SET media_type = NULL"))
            session.commit()
            
            logger.info("Set all media_type values to NULL (partial rollback)")
            return True
            
    except Exception as e:
        logger.error(f"Rollback failed: {str(e)}")
        return False


if __name__ == "__main__":
    # Run migration when script is executed directly
    logging.basicConfig(level=logging.INFO)
    success = migrate_add_media_type_column()
    if success:
        print("Migration completed successfully!")
    else:
        print("Migration failed!")
