"""
Database migration to add name column to media table
"""
import logging
from sqlalchemy import text

logger = logging.getLogger(__name__)


def migrate_add_media_name_column():
    """Add name column to media table for user-provided display names"""
    try:
        # Import here to avoid circular imports
        from videotrans.database.models import db_manager

        with db_manager.get_session() as session:
            # Check if column already exists (PostgreSQL syntax)
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'media' AND column_name = 'name'
            """)).fetchone()
            
            if result.count > 0:
                logger.info("name column already exists, skipping migration")
                return True
            
            # Add the new column
            logger.info("Adding name column to media table...")
            session.execute(text("ALTER TABLE media ADD COLUMN name VARCHAR(255)"))
            
            # Commit the changes
            session.commit()
            logger.info("Successfully added name column to media table")
            
            return True
            
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False


def rollback_media_name_column():
    """Remove name column (rollback migration)"""
    try:
        # Import here to avoid circular imports
        from videotrans.database.models import db_manager

        with db_manager.get_session() as session:
            # Check if column exists before trying to drop it
            result = session.execute(text("""
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'media' AND column_name = 'name'
            """)).fetchone()
            
            if result.count == 0:
                logger.info("name column doesn't exist, nothing to rollback")
                return True
            
            logger.info("Rolling back name column migration...")
            session.execute(text("ALTER TABLE media DROP COLUMN name"))
            session.commit()
            
            logger.info("Successfully removed name column from media table")
            return True
            
    except Exception as e:
        logger.error(f"Rollback failed: {str(e)}")
        return False


if __name__ == "__main__":
    # Allow running this migration directly
    import sys
    import os
    
    # Add the project root to the path
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    sys.path.insert(0, project_root)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("Running media name column migration...")
    success = migrate_add_media_name_column()
    
    if success:
        print("✅ Migration completed successfully!")
        sys.exit(0)
    else:
        print("❌ Migration failed!")
        sys.exit(1)
