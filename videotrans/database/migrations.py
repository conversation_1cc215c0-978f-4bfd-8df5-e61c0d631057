"""
Migration utilities for moving from JSON to database configuration
"""
import json
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple
from sqlalchemy.exc import SQLAlchemyError

from .models import Setting, SettingCategory, ConfigHistory, db_manager
from .config_manager import ConfigManager


class ConfigMigrator:
    """Handles migration from JSON files to database"""
    
    def __init__(self):
        # Get root directory without importing config to avoid circular imports
        import os
        self.root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.params_file = f"{self.root_dir}/videotrans/params.json"
        self.cfg_file = f"{self.root_dir}/videotrans/cfg.json"
        self.backup_dir = f"{self.root_dir}/videotrans/config_backup"
    
    def needs_migration(self) -> bool:
        """Check if migration is needed"""
        try:
            with db_manager.get_session() as session:
                # Check if database has any settings
                count = session.query(Setting).count()
                return count == 0 and (
                    os.path.exists(self.params_file) or 
                    os.path.exists(self.cfg_file)
                )
        except Exception:
            return True
    
    def create_backup(self) -> bool:
        """Create backup of JSON files before migration"""
        try:
            Path(self.backup_dir).mkdir(parents=True, exist_ok=True)

            if os.path.exists(self.params_file):
                backup_params = f"{self.backup_dir}/params.json.backup"
                Path(self.params_file).rename(backup_params)
                print(f"Backed up params.json to {backup_params}")

            if os.path.exists(self.cfg_file):
                backup_cfg = f"{self.backup_dir}/cfg.json.backup"
                Path(self.cfg_file).rename(backup_cfg)
                print(f"Backed up cfg.json to {backup_cfg}")
            
            return True
        except Exception as e:
            print(f"Error creating backup: {e}")
            return False
    
    def load_json_data(self) -> Dict[str, Any]:
        """Load all data from JSON files"""
        data = {}

        # Load cfg.json backup
        cfg_backup = f"{self.backup_dir}/cfg.json.backup"
        if os.path.exists(cfg_backup):
            try:
                with open(cfg_backup, 'r', encoding='utf-8') as f:
                    cfg_data = json.load(f)
                    data.update(cfg_data)
                    print(f"Loaded {len(cfg_data)} settings from cfg.json")
            except Exception as e:
                print(f"Error loading cfg.json backup: {e}")

        # Load params.json backup
        params_backup = f"{self.backup_dir}/params.json.backup"
        if os.path.exists(params_backup):
            try:
                with open(params_backup, 'r', encoding='utf-8') as f:
                    params_data = json.load(f)
                    data.update(params_data)
                    print(f"Loaded {len(params_data)} settings from params.json")
            except Exception as e:
                print(f"Error loading params.json backup: {e}")
        return data
    
    def categorize_settings(self, data: Dict[str, Any]) -> Dict[str, List[Tuple[str, Any]]]:
        """Categorize settings based on their keys"""
        categories = {
            'API Keys': [],
            'Translation': [],
            'TTS': [],
            'Recognition': [],
            'UI': [],
            'System': [],
            'Models': [],
            'Other': []
        }
        
        for key, value in data.items():
            category = self._determine_category(key)
            categories[category].append((key, value))
        
        return categories
    
    def _determine_category(self, key: str) -> str:
        """Determine category for a setting key"""
        key_lower = key.lower()
        
        # API Keys and credentials
        if any(term in key_lower for term in ['key', 'api', 'secret', 'token', 'credential']):
            return 'API Keys'
        
        # Translation services
        if any(term in key_lower for term in ['translate', 'deepl', 'baidu', 'tencent', 'ali', 'libre']):
            return 'Translation'
        
        # TTS services
        if any(term in key_lower for term in ['tts', 'voice', 'speech', 'azure_speech', 'elevenlabs', 'openaitts']):
            return 'TTS'
        
        # Recognition services
        if any(term in key_lower for term in ['recogn', 'whisper', 'stt', 'model_name', 'deepgram']):
            return 'Recognition'
        
        # UI and display
        if any(term in key_lower for term in ['ui', 'lang', 'subtitle', 'font', 'color']):
            return 'UI'
        
        # System settings
        if any(term in key_lower for term in ['dir', 'path', 'cuda', 'proxy', 'homedir']):
            return 'System'
        
        # Model settings
        if any(term in key_lower for term in ['model', 'temperature', 'max_token']):
            return 'Models'
        
        return 'Other'
    
    def _infer_data_type(self, value: Any) -> str:
        """Infer data type from value"""
        if isinstance(value, bool):
            return 'bool'
        elif isinstance(value, int):
            return 'int'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'
    
    def _is_sensitive(self, key: str) -> bool:
        """Determine if a setting contains sensitive information"""
        key_lower = key.lower()
        return any(term in key_lower for term in [
            'key', 'secret', 'token', 'password', 'credential', 'miyue'
        ])
    
    def migrate_to_database(self) -> bool:
        """Migrate JSON data to database"""
        try:
            # Initialize database
            db_manager.initialize_database()
            
            # Load JSON data
            data = self.load_json_data()
            if not data:
                print("No data to migrate")
                return True
            
            # Categorize settings
            categorized = self.categorize_settings(data)
            
            with db_manager.get_session() as session:
                # Create categories
                for category_name in categorized.keys():
                    if categorized[category_name]:  # Only create if has settings
                        category = session.query(SettingCategory).filter(
                            SettingCategory.name == category_name
                        ).first()
                        
                        if not category:
                            category = SettingCategory(
                                name=category_name,
                                description=f"Settings related to {category_name.lower()}"
                            )
                            session.add(category)
                
                session.commit()
                
                # Migrate settings
                migrated_count = 0
                for category_name, settings in categorized.items():
                    for key, value in settings:
                        try:
                            # Check if setting already exists
                            existing = session.query(Setting).filter(Setting.key == key).first()
                            if existing:
                                continue
                            
                            setting = Setting(
                                key=key,
                                category=category_name,
                                data_type=self._infer_data_type(value),
                                is_sensitive=self._is_sensitive(key),
                                description=f"Migrated from JSON configuration"
                            )
                            setting.set_typed_value(value)
                            session.add(setting)
                            
                            # Record migration in history
                            history = ConfigHistory(
                                setting_key=key,
                                old_value=None,
                                new_value=str(value) if value is not None else None,
                                changed_by='migration',
                                change_reason='json_to_database_migration'
                            )
                            session.add(history)
                            
                            migrated_count += 1
                        except Exception as e:
                            print(f"Error migrating setting {key}: {e}")
                            continue
                
                session.commit()
                print(f"Successfully migrated {migrated_count} settings to database")
                return True
                
        except SQLAlchemyError as e:
            print(f"Database error during migration: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error during migration: {e}")
            return False
    
    def rollback_migration(self) -> bool:
        """Rollback migration by restoring JSON files"""
        try:
            # Restore backup files
            params_backup = f"{self.backup_dir}/params.json.backup"
            cfg_backup = f"{self.backup_dir}/cfg.json.backup"
            
            if os.path.exists(params_backup):
                Path(params_backup).rename(self.params_file)
                print("Restored params.json from backup")
            
            if os.path.exists(cfg_backup):
                Path(cfg_backup).rename(self.cfg_file)
                print("Restored cfg.json from backup")
            
            return True
        except Exception as e:
            print(f"Error during rollback: {e}")
            return False
    
    def run_migration(self) -> bool:
        """Run complete migration process"""
        print("Starting configuration migration from JSON to database...")
        
        if not self.needs_migration():
            print("Migration not needed - database already has settings")
            return True
        
        # Create backup
        if not self.create_backup():
            print("Failed to create backup - aborting migration")
            return False
        
        # Migrate to database
        if not self.migrate_to_database():
            print("Migration failed - attempting rollback")
            self.rollback_migration()
            return False
        
        print("Migration completed successfully!")
        return True


def run_migration():
    """Convenience function to run migration"""
    migrator = ConfigMigrator()
    return migrator.run_migration()
