"""
Task Manager for user-scoped database operations
"""
import json
import uuid as uuid_lib
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from .models import Task, TaskStage, TaskLog, Media, TaskMedia, db_manager
from ..configure.enhanced_config import enhanced_config


def _replace_home_dir_with_url(path):
  host = enhanced_config.get_system_setting('server_host', 'http://127.0.0.1:9011')
  if '/apidata/' not in path:
    return path

  return f"{host}/apidata/{path.split('/apidata/')[1]}"


class TaskManager:
    """Database-backed task management with user-scoped operations"""

    def __init__(self):
        self.db_manager = db_manager

    def create_task(self, user_id: str, task_type: str, name: str, config: Dict) -> str:
        """Create a new task record"""
        task_uuid = str(uuid_lib.uuid4())

        with self.db_manager.get_session() as session:
            task = Task(
                uuid=task_uuid,
                user_id=user_id,
                name=name,
                task_type=task_type,
                status='pending'
            )
            task.set_config(config)

            session.add(task)
            session.commit()

        return task_uuid

    def get_task(self, user_id: str, task_uuid: str, include_sensitive: bool = False) -> Optional[Dict]:
        """Get task by UUID for specific user"""
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if task:
                return self._task_to_dict(task, include_sensitive=include_sensitive)
        return None

    def get_user_tasks(self, user_id: str, status: str = None, task_type: str = None,
                      limit: int = 100, offset: int = 0, order_by: str = 'created_at',
                      order_desc: bool = True, include_sensitive: bool = False) -> List[Dict]:
        """Get all tasks for a user with filtering and pagination"""
        with self.db_manager.get_session() as session:
            query = session.query(Task).filter(Task.user_id == user_id)

            # Apply filters
            if status:
                query = query.filter(Task.status == status)
            if task_type:
                query = query.filter(Task.task_type == task_type)

            # Apply ordering
            order_column = getattr(Task, order_by, Task.created_at)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))

            # Apply pagination
            tasks = query.offset(offset).limit(limit).all()

            return [self._task_to_dict(task, include_sensitive=include_sensitive) for task in tasks]

    def update_task_status(self, user_id: str, task_uuid: str, status: str,
                          error_message: str = None) -> bool:
        """Update task status for specific user"""
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return False

            task.status = status
            task.updated_at = datetime.now()

            if status == 'running' and not task.started_at:
                task.started_at = datetime.now()
            elif status in ['completed', 'failed', 'stopped']:
                task.completed_at = datetime.now()

            if error_message:
                task.error_message = error_message

            session.commit()
            return True

    def update_task_progress(self, user_id: str, task_uuid: str, progress: float,
                           status_text: str = None, stage: str = None) -> bool:
        """Update task progress for specific user"""
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return False

            task.progress_percent = max(0.0, min(100.0, progress))
            task.updated_at = datetime.now()

            if status_text:
                task.status_text = status_text
            if stage:
                task.current_stage = stage

            session.commit()
            return True

    def log_task_event(self, user_id: str, task_uuid: str, message: str,
                      level: str = 'info', stage: str = None) -> bool:
        """Log task event for specific user"""
        with self.db_manager.get_session() as session:
            # Verify task belongs to user
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return False

            log = TaskLog(
                task_uuid=task_uuid,
                message=message,
                log_level=level,
                stage=stage
            )

            session.add(log)
            session.commit()
            return True

    def update_task_output_files(self, user_id: str, task_uuid: str, output_files: List[str]) -> bool:
        """Update task output files for specific user"""
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return False

            task.set_output_files(output_files)
            task.updated_at = datetime.now()

            session.commit()
            return True

    def get_task_logs(self, user_id: str, task_uuid: str, level: str = None,
                     limit: int = 100) -> List[Dict]:
        """Get task logs for specific user"""
        with self.db_manager.get_session() as session:
            # Verify task belongs to user
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return []

            query = session.query(TaskLog).filter(TaskLog.task_uuid == task_uuid)

            if level:
                query = query.filter(TaskLog.log_level == level)

            logs = query.order_by(desc(TaskLog.created_at)).limit(limit).all()

            return [self._log_to_dict(log) for log in logs]

    def delete_task(self, user_id: str, task_uuid: str) -> bool:
        """Delete task for specific user (force delete with all related data)"""
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(
                and_(Task.uuid == task_uuid, Task.user_id == user_id)
            ).first()

            if not task:
                return False

            try:
                # First delete TaskMedia records that reference this task
                # This prevents foreign key constraint issues
                task_media_records = session.query(TaskMedia).filter(
                    TaskMedia.task_uuid == task_uuid
                ).all()

                for task_media in task_media_records:
                    session.delete(task_media)

                # Then delete the Task record
                # This will cascade delete TaskStage and TaskLog records due to cascade="all, delete-orphan"
                session.delete(task)
                session.commit()
                return True

            except Exception as e:
                session.rollback()
                # Log the error but don't expose internal details
                print(f"Error deleting task {task_uuid}: {str(e)}")
                return False

    def get_user_task_stats(self, user_id: str) -> Dict:
        """Get task statistics for user"""
        with self.db_manager.get_session() as session:
            stats = {}

            # Count by status
            status_counts = session.query(
                Task.status, func.count(Task.id)
            ).filter(Task.user_id == user_id).group_by(Task.status).all()

            stats['by_status'] = {status: count for status, count in status_counts}

            # Count by type
            type_counts = session.query(
                Task.task_type, func.count(Task.id)
            ).filter(Task.user_id == user_id).group_by(Task.task_type).all()

            stats['by_type'] = {task_type: count for task_type, count in type_counts}

            # Total tasks
            stats['total'] = session.query(Task).filter(Task.user_id == user_id).count()

            return stats

    def _task_to_dict(self, task: Task, include_sensitive: bool = False) -> Dict:
        """Convert Task model to dictionary with optional sensitive field filtering"""
        base_dict = {
            'id': task.id,
            'uuid': task.uuid,
            'user_id': task.user_id,
            'name': task.name,
            'type': task.task_type,
            'status': task.status,
            'progress': task.progress_percent,
            'status_text': task.status_text,
            'stage': task.current_stage,
            'error_message': task.error_message,
            'retry_count': task.retry_count,
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'updated_at': task.updated_at.isoformat() if task.updated_at else None
        }

        if include_sensitive:
            # Include sensitive fields for internal use
            base_dict.update({
                'source_file': task.source_file,
                'target_dir': task.target_dir,
                'output_files': task.get_output_files(),
                'config': task.get_config()
            })
        else:
            # Safe version for API responses - filter out sensitive config fields
            config = task.get_config()
            safe_config = self._filter_sensitive_config(config) if config else {}

            base_dict.update({
                'source_file': self._safe_path(task.source_file),
                'output_files': task.get_output_files(),
                'config': safe_config
            })

        # update output file to replace home dir with host url
        if 'output_files' in base_dict:
          for i, it in enumerate(base_dict['output_files']):
            base_dict['output_files'][i] = _replace_home_dir_with_url(it)
        return base_dict

    def _filter_sensitive_config(self, config: Dict) -> Dict:
        """Filter out sensitive fields from task configuration"""
        # List of sensitive fields to exclude from API responses
        sensitive_fields = {
            'cache_folder',
            'target_dir',
            'dirname',
            'temp_dir',
            'cache_dir',
            'work_dir',
            'output_dir',
            'tmp_dir',
            'absolute_path',
            'full_path',
            'system_path'
        }

        # Create a filtered copy of the config
        safe_config = {}
        for key, value in config.items():
            if key.lower() not in sensitive_fields:
                # Also filter nested dictionaries
                if isinstance(value, dict):
                    safe_config[key] = self._filter_sensitive_config(value)
                else:
                    safe_config[key] = value

        return safe_config

    def _safe_path(self, path: str) -> str:
        """Convert absolute path to safe relative path or filename only"""
        if not path:
            return path

        # Extract just the filename for security
        import os
        return os.path.basename(path)

    def _log_to_dict(self, log: TaskLog) -> Dict:
        """Convert TaskLog model to dictionary"""
        return {
            'id': log.id,
            'task_uuid': log.task_uuid,
            'level': log.log_level,
            'message': log.message,
            'stage': log.stage,
            'created_at': log.created_at.isoformat() if log.created_at else None
        }


# Global task manager instance
task_manager = TaskManager()
