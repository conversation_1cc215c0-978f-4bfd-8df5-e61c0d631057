"""
Configuration change watcher and notification system for real-time updates
"""
import threading
import time
import weakref
from typing import Dict, Any, Callable, Set
from datetime import datetime

from .models import Setting, ConfigHistory, db_manager


class ConfigChangeNotifier:
    """Notifies listeners about configuration changes"""
    
    def __init__(self):
        self._listeners = weakref.WeakSet()
        self._category_listeners = {}
        self._key_listeners = {}
        self._lock = threading.RLock()
    
    def add_listener(self, callback: Callable[[str, Any, Any], None]):
        """Add a global configuration change listener
        
        Args:
            callback: Function called with (key, old_value, new_value)
        """
        with self._lock:
            self._listeners.add(callback)
    
    def add_category_listener(self, category: str, callback: Callable[[str, Any, Any], None]):
        """Add a category-specific configuration change listener
        
        Args:
            category: Configuration category to watch
            callback: Function called with (key, old_value, new_value)
        """
        with self._lock:
            if category not in self._category_listeners:
                self._category_listeners[category] = weakref.WeakSet()
            self._category_listeners[category].add(callback)
    
    def add_key_listener(self, key: str, callback: Callable[[str, Any, Any], None]):
        """Add a key-specific configuration change listener
        
        Args:
            key: Configuration key to watch
            callback: Function called with (key, old_value, new_value)
        """
        with self._lock:
            if key not in self._key_listeners:
                self._key_listeners[key] = weakref.WeakSet()
            self._key_listeners[key].add(callback)
    
    def notify_change(self, key: str, old_value: Any, new_value: Any, category: str = None):
        """Notify all relevant listeners about a configuration change
        
        Args:
            key: Configuration key that changed
            old_value: Previous value
            new_value: New value
            category: Configuration category (if known)
        """
        with self._lock:
            # Notify global listeners
            for listener in list(self._listeners):
                try:
                    listener(key, old_value, new_value)
                except Exception as e:
                    print(f"Error in config change listener: {e}")
            
            # Notify category-specific listeners
            if category and category in self._category_listeners:
                for listener in list(self._category_listeners[category]):
                    try:
                        listener(key, old_value, new_value)
                    except Exception as e:
                        print(f"Error in category config listener: {e}")
            
            # Notify key-specific listeners
            if key in self._key_listeners:
                for listener in list(self._key_listeners[key]):
                    try:
                        listener(key, old_value, new_value)
                    except Exception as e:
                        print(f"Error in key config listener: {e}")


class ConfigWatcher:
    """Watches for configuration changes in the database"""

    def __init__(self, check_interval: float = 2.0):
        self.check_interval = check_interval
        self.notifier = ConfigChangeNotifier()
        self._last_check = {}
        self._current_values = {}  # Cache of current values for comparison
        self._running = False
        self._thread = None
        self._lock = threading.RLock()
        self._initialized = False
    
    def start(self):
        """Start watching for configuration changes"""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._thread = threading.Thread(target=self._watch_loop, daemon=True)
            self._thread.start()
            print("Configuration watcher started")
    
    def stop(self):
        """Stop watching for configuration changes"""
        with self._lock:
            self._running = False
            if self._thread:
                self._thread.join(timeout=5)
            print("Configuration watcher stopped")
    
    def _watch_loop(self):
        """Main watching loop"""
        while self._running:
            try:
                self._check_changes()
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"Error in config watcher: {e}")
                time.sleep(self.check_interval)
    
    def _check_changes(self):
        """Check for configuration changes by comparing current database values"""
        try:
            with db_manager.get_session() as session:
                # Get all current settings from database
                current_settings = {}
                settings = session.query(Setting).all()

                for setting in settings:
                    current_settings[setting.key] = {
                        'value': setting.get_typed_value(),
                        'category': setting.category,
                        'updated_at': setting.updated_at
                    }

                # Initialize cache on first run
                if not self._initialized:
                    self._current_values = current_settings.copy()
                    self._initialized = True
                    print(f"Configuration watcher initialized with {len(current_settings)} settings")
                    return

                # Compare with cached values to detect changes
                for key, current_data in current_settings.items():
                    current_value = current_data['value']
                    category = current_data['category']

                    if key in self._current_values:
                        old_value = self._current_values[key]['value']
                        old_updated_at = self._current_values[key].get('updated_at')
                        new_updated_at = current_data['updated_at']

                        # Check if value changed or updated_at changed
                        if (current_value != old_value or
                            (new_updated_at and old_updated_at and new_updated_at > old_updated_at)):

                            print(f"🔄 Configuration change detected: {key} = {current_value}")

                            # Clear config manager cache to force reload from database
                            self._clear_config_cache()

                            # Notify listeners
                            self.notifier.notify_change(key, old_value, current_value, category)

                            # Update cache
                            self._current_values[key] = current_data
                    else:
                        # New setting added
                        print(f"➕ New configuration setting: {key} = {current_value}")
                        self._clear_config_cache()
                        self.notifier.notify_change(key, None, current_value, category)
                        self._current_values[key] = current_data

                # Check for deleted settings
                for key in list(self._current_values.keys()):
                    if key not in current_settings:
                        old_value = self._current_values[key]['value']
                        print(f"➖ Configuration setting deleted: {key}")
                        self._clear_config_cache()
                        self.notifier.notify_change(key, old_value, None, None)
                        del self._current_values[key]

        except Exception as e:
            print(f"Error checking config changes: {e}")

    def _clear_config_cache(self):
        """Clear the configuration manager cache to force reload from database"""
        try:
            from .config_manager import get_config_manager
            config_manager = get_config_manager()
            config_manager.clear_cache()
            print("🔄 Configuration cache cleared - forcing reload from database")
        except Exception as e:
            print(f"Error clearing config cache: {e}")


class RealtimeConfigManager:
    """Configuration manager with real-time update capabilities"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.watcher = ConfigWatcher()
        self._service_instances = weakref.WeakSet()
        self._setup_listeners()
    
    def _setup_listeners(self):
        """Setup configuration change listeners"""
        # Listen for API key changes
        self.watcher.notifier.add_category_listener('API Keys', self._handle_api_key_change)
        
        # Listen for service configuration changes
        self.watcher.notifier.add_category_listener('Translation', self._handle_service_config_change)
        self.watcher.notifier.add_category_listener('TTS', self._handle_service_config_change)
        self.watcher.notifier.add_category_listener('Recognition', self._handle_service_config_change)
        
        # Listen for system setting changes
        self.watcher.notifier.add_category_listener('System', self._handle_system_config_change)
    
    def _handle_api_key_change(self, key: str, old_value: Any, new_value: Any):
        """Handle API key configuration changes"""
        print(f"API key changed: {key} = {'***' if new_value else 'Not set'}")
        
        # Clear cache to force reload
        self.config_manager.clear_cache()
        
        # Notify service instances
        for instance in list(self._service_instances):
            if hasattr(instance, 'reload_config'):
                try:
                    instance.reload_config()
                except Exception as e:
                    print(f"Error reloading config for service instance: {e}")
    
    def _handle_service_config_change(self, key: str, old_value: Any, new_value: Any):
        """Handle service configuration changes"""
        print(f"Service config changed: {key} = {new_value}")
        
        # Clear cache to force reload
        self.config_manager.clear_cache()
        
        # Notify relevant service instances
        service_name = key.split('_')[0] if '_' in key else key
        for instance in list(self._service_instances):
            if hasattr(instance, 'service_name') and instance.service_name == service_name:
                if hasattr(instance, 'reload_config'):
                    try:
                        instance.reload_config()
                    except Exception as e:
                        print(f"Error reloading config for {service_name}: {e}")
    
    def _handle_system_config_change(self, key: str, old_value: Any, new_value: Any):
        """Handle system configuration changes"""
        print(f"System config changed: {key} = {new_value}")
        
        # Clear cache to force reload
        self.config_manager.clear_cache()
        
        # Update global configuration
        from videotrans.configure import config
        if hasattr(config, 'params'):
            config.params[key] = new_value
    
    def register_service_instance(self, instance):
        """Register a service instance for configuration updates"""
        self._service_instances.add(instance)
    
    def start_watching(self):
        """Start watching for configuration changes"""
        self.watcher.start()
    
    def stop_watching(self):
        """Stop watching for configuration changes"""
        self.watcher.stop()
    
    def set(self, key: str, value: Any, **kwargs):
        """Set configuration value and trigger notifications"""
        old_value = self.config_manager.get(key)
        result = self.config_manager.set(key, value, **kwargs)
        
        # Manually trigger notification for immediate effect
        category = kwargs.get('category')
        self.watcher.notifier.notify_change(key, old_value, value, category)
        
        return result
    
    def get(self, key: str, default: Any = None):
        """Get configuration value"""
        return self.config_manager.get(key, default)
    
    def get_all(self):
        """Get all configuration values"""
        return self.config_manager.get_all()
    
    def delete(self, key: str):
        """Delete configuration value and trigger notifications"""
        old_value = self.config_manager.get(key)
        result = self.config_manager.delete(key)
        
        if result:
            # Manually trigger notification
            self.watcher.notifier.notify_change(key, old_value, None)
        
        return result


# Global realtime configuration manager
_realtime_config_manager = None

def get_realtime_config_manager():
    """Get the global realtime configuration manager"""
    global _realtime_config_manager
    if _realtime_config_manager is None:
        from .config_manager import get_config_manager
        config_manager = get_config_manager()
        _realtime_config_manager = RealtimeConfigManager(config_manager)
        _realtime_config_manager.start_watching()
    return _realtime_config_manager
