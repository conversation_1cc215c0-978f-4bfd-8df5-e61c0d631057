"""
PostgreSQL configuration and connection management
"""
import os
import json
from typing import Dict, Any, Optional
from urllib.parse import urlparse


class PostgreSQLConfig:
    """PostgreSQL database configuration management"""
    
    def __init__(self):
        self.config_file = None
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """Load PostgreSQL configuration from various sources"""
        # Try to load from environment variables first
        if self._load_from_env():
            return
        
        # Try to load from config file
        if self._load_from_file():
            return
        
        # Use default configuration
        self._use_defaults()
    
    def _load_from_env(self) -> bool:
        """Load configuration from environment variables"""
        env_vars = {
            'POSTGRES_HOST': os.getenv('POSTGRES_HOST'),
            'POSTGRES_PORT': os.getenv('POSTGRES_PORT'),
            'POSTGRES_DB': os.getenv('POSTGRES_DB'),
            'POSTGRES_USER': os.getenv('POSTGRES_USER'),
            'POSTGRES_PASSWORD': os.getenv('POSTGRES_PASSWORD'),
            'DATABASE_URL': os.getenv('DATABASE_URL')
        }
        
        # If DATABASE_URL is provided, parse it
        if env_vars['DATABASE_URL']:
            try:
                parsed = urlparse(env_vars['DATABASE_URL'])
                self._config = {
                    'host': parsed.hostname,
                    'port': parsed.port or 5432,
                    'database': parsed.path.lstrip('/'),
                    'username': parsed.username,
                    'password': parsed.password,
                    'url': env_vars['DATABASE_URL']
                }
                return True
            except Exception as e:
                print(f"Error parsing DATABASE_URL: {e}")
                return False
        
        # Check if individual environment variables are set
        if all(env_vars[key] for key in ['POSTGRES_HOST', 'POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']):
            self._config = {
                'host': env_vars['POSTGRES_HOST'],
                'port': int(env_vars['POSTGRES_PORT']) if env_vars['POSTGRES_PORT'] else 5432,
                'database': env_vars['POSTGRES_DB'],
                'username': env_vars['POSTGRES_USER'],
                'password': env_vars['POSTGRES_PASSWORD']
            }
            return True
        
        return False
    
    def _load_from_file(self) -> bool:
        """Load configuration from JSON file"""
        try:
            from videotrans.configure import config
            config_file = f"{config.ROOT_DIR}/videotrans/postgres_config.json"
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                self.config_file = config_file
                return True
        except Exception as e:
            print(f"Error loading PostgreSQL config file: {e}")
        
        return False
    
    def _use_defaults(self):
        """Use default PostgreSQL configuration"""
        self._config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'pyvideotrans',
            'username': 'pyvideotrans',
            'password': 'pyvideotrans'
        }
    
    def get_database_url(self) -> str:
        """Get PostgreSQL database URL"""
        if self._config.get('url'):
            return self._config['url']
        
        return (f"postgresql://{self._config['username']}:{self._config['password']}"
                f"@{self._config['host']}:{self._config['port']}/{self._config['database']}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get configuration dictionary"""
        return self._config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file"""
        try:
            from videotrans.configure import config as app_config
            config_file = f"{app_config.ROOT_DIR}/videotrans/postgres_config.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self._config = config.copy()
            self.config_file = config_file
            return True
        except Exception as e:
            print(f"Error saving PostgreSQL config: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test PostgreSQL connection"""
        try:
            import psycopg2
            from psycopg2 import OperationalError
            
            conn = psycopg2.connect(
                host=self._config['host'],
                port=self._config['port'],
                database=self._config['database'],
                user=self._config['username'],
                password=self._config['password']
            )
            conn.close()
            return True
        except OperationalError as e:
            print(f"PostgreSQL connection failed: {e}")
            return False
        except Exception as e:
            print(f"Error testing PostgreSQL connection: {e}")
            return False
    
    def create_database_if_not_exists(self) -> bool:
        """Create database if it doesn't exist"""
        try:
            import psycopg2
            from psycopg2 import OperationalError
            from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
            
            # Connect to postgres database to create our database
            conn = psycopg2.connect(
                host=self._config['host'],
                port=self._config['port'],
                database='postgres',  # Connect to default postgres database
                user=self._config['username'],
                password=self._config['password']
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            cursor = conn.cursor()
            
            # Check if database exists
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (self._config['database'],)
            )
            
            if not cursor.fetchone():
                # Create database
                cursor.execute(f'CREATE DATABASE "{self._config["database"]}"')
                print(f"Created database: {self._config['database']}")
            
            cursor.close()
            conn.close()
            return True
            
        except OperationalError as e:
            print(f"Error creating PostgreSQL database: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error creating database: {e}")
            return False
    
    def get_connection_info(self) -> str:
        """Get human-readable connection information"""
        return (f"PostgreSQL: {self._config['username']}@{self._config['host']}:"
                f"{self._config['port']}/{self._config['database']}")


# Global PostgreSQL configuration instance
postgres_config = PostgreSQLConfig()
