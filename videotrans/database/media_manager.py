"""
Media Manager for user-scoped media library operations
"""
import os
import hashlib
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from .models import Media, TaskMedia, db_manager


class MediaManager:
    """Database-backed media library management with user-scoped operations"""

    def __init__(self):
        self.db_manager = db_manager

    def create_media_entry(self, user_id: str, filename: str, original_filename: str,
                          file_path: str, file_size: int = None, mime_type: str = None,
                          media_type: str = None, metadata: Dict = None, name: str = None) -> int:
        """Create a new media library entry

        Args:
            file_path: Can be either absolute path or relative path starting with /
        """
        with self.db_manager.get_session() as session:
            # Convert relative path to absolute path for file operations
            if file_path.startswith('/') and not file_path.startswith('/usr/lib/media'):
                # This is a relative path, convert to absolute for file operations
                absolute_file_path = '/usr/lib/media' + file_path
            else:
                # This is already an absolute path
                absolute_file_path = file_path

            # Calculate file hash for deduplication using absolute path
            file_hash = self._calculate_file_hash(absolute_file_path) if os.path.exists(absolute_file_path) else None

            # Auto-detect media type if not provided
            if not media_type:
                from videotrans.util import tools
                media_type = tools.detect_media_type(original_filename, mime_type)

            media = Media(
                user_id=user_id,
                name=name,  # User-provided display name
                filename=filename,
                original_filename=original_filename,
                file_path=file_path,
                file_size=file_size,
                mime_type=mime_type,
                media_type=media_type,
                file_hash=file_hash
            )

            if metadata:
                media.set_metadata(metadata)

            session.add(media)
            session.commit()

            return media.id

    def get_media(self, user_id: str, media_id: int, base_url: str = None) -> Optional[Dict]:
        """Get media entry by ID for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if media:
                return self._media_to_dict(media, base_url)
        return None

    def get_user_media(self, user_id: str, mime_type: str = None, media_type: str = None,
                      limit: int = 100, offset: int = 0, order_by: str = 'uploaded_at',
                      order_desc: bool = True, search: str = None, base_url: str = None) -> List[Dict]:
        """Get all media for a user with filtering and pagination"""
        with self.db_manager.get_session() as session:
            # Use explicit boolean comparison to avoid None comparison issues
            query = session.query(Media).filter(
                and_(Media.user_id == user_id, Media.is_deleted.is_(False))
            )

            # Apply filters
            if mime_type:
                query = query.filter(Media.mime_type.like(f'{mime_type}%'))

            if media_type:
                query = query.filter(Media.media_type == media_type)

            if search:
                search_term = f'%{search}%'
                query = query.filter(
                    or_(
                        Media.filename.like(search_term),
                        Media.original_filename.like(search_term),
                        Media.description.like(search_term)
                    )
                )

            # Apply ordering with field mapping and null handling
            # Map common field names to actual model fields
            field_mapping = {
                'created_at': 'uploaded_at',  # Map created_at to uploaded_at
                'date': 'uploaded_at',
                'name': 'filename',
                'size': 'file_size',
                'type': 'media_type'
            }

            # Get the actual field name
            actual_field = field_mapping.get(order_by, order_by)

            # For debugging: temporarily use a simple default ordering
            # This will help us identify if the issue is in the ordering logic
            try:
                # Validate that the field exists on the Media model
                if hasattr(Media, actual_field):
                    order_column = getattr(Media, actual_field)

                    # Apply ordering (simplified to avoid null comparison issues)
                    if order_desc:
                        query = query.order_by(desc(order_column))
                    else:
                        query = query.order_by(asc(order_column))
                else:
                    # Fallback to uploaded_at if field doesn't exist
                    if order_desc:
                        query = query.order_by(desc(Media.uploaded_at))
                    else:
                        query = query.order_by(asc(Media.uploaded_at))
            except Exception as e:
                # If ordering fails, just use ID ordering as fallback
                query = query.order_by(desc(Media.id) if order_desc else asc(Media.id))

            # Apply pagination
            try:
                media_list = query.offset(offset).limit(limit).all()
            except Exception as e:
                raise Exception(f"Database query failed: {str(e)}")

            # Convert to dict with error handling
            result = []
            for i, media in enumerate(media_list):
                try:
                    result.append(self._media_to_dict(media, base_url))
                except Exception as e:
                    raise Exception(f"Error converting media {i} (id={getattr(media, 'id', 'unknown')}) to dict: {str(e)}")

            return result

    def update_media_metadata(self, user_id: str, media_id: int,
                             duration: float = None, width: int = None, height: int = None,
                             codec: str = None, bitrate: int = None, frame_rate: float = None,
                             thumbnail_path: str = None, metadata: Dict = None) -> bool:
        """Update media metadata for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if not media:
                return False

            # Update fields if provided
            if duration is not None:
                media.duration = duration
            if width is not None:
                media.width = width
            if height is not None:
                media.height = height
            if codec is not None:
                media.codec = codec
            if bitrate is not None:
                media.bitrate = bitrate
            if frame_rate is not None:
                media.frame_rate = frame_rate
            if thumbnail_path is not None:
                media.thumbnail_path = thumbnail_path
            if metadata is not None:
                media.set_metadata(metadata)

            media.updated_at = datetime.now()
            session.commit()
            return True

    def update_media_tags(self, user_id: str, media_id: int, tags: List[str]) -> bool:
        """Update media tags for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if not media:
                return False

            media.set_tags(tags)
            media.updated_at = datetime.now()
            session.commit()
            return True

    def update_media_description(self, user_id: str, media_id: int, description: str) -> bool:
        """Update media description for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if not media:
                return False

            media.description = description
            media.updated_at = datetime.now()
            session.commit()
            return True

    def soft_delete_media(self, user_id: str, media_id: int) -> bool:
        """Soft delete media entry for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if not media:
                return False

            media.is_deleted = True
            media.deleted_at = datetime.now()
            session.commit()
            return True

    def hard_delete_media(self, user_id: str, media_id: int) -> bool:
        """Permanently delete media entry for specific user"""
        with self.db_manager.get_session() as session:
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id)
            ).first()

            if not media:
                return False

            session.delete(media)
            session.commit()
            return True

    def link_media_to_task(self, user_id: str, media_id: int, task_uuid: str,
                          relationship_type: str = 'input') -> bool:
        """Link media file to task for specific user"""
        with self.db_manager.get_session() as session:
            # Verify media belongs to user
            media = session.query(Media).filter(
                and_(Media.id == media_id, Media.user_id == user_id, Media.is_deleted == False)
            ).first()

            if not media:
                return False

            # Check if link already exists
            existing_link = session.query(TaskMedia).filter(
                and_(TaskMedia.task_uuid == task_uuid, TaskMedia.media_id == media_id,
                     TaskMedia.relationship_type == relationship_type)
            ).first()

            if existing_link:
                return True  # Already linked

            task_media = TaskMedia(
                task_uuid=task_uuid,
                media_id=media_id,
                relationship_type=relationship_type
            )

            session.add(task_media)
            session.commit()
            return True

    def get_media_by_task(self, user_id: str, task_uuid: str,
                         relationship_type: str = None, base_url: str = None) -> List[Dict]:
        """Get media files associated with a task for specific user"""
        with self.db_manager.get_session() as session:
            query = session.query(Media).join(TaskMedia).filter(
                and_(TaskMedia.task_uuid == task_uuid, Media.user_id == user_id,
                     Media.is_deleted == False)
            )

            if relationship_type:
                query = query.filter(TaskMedia.relationship_type == relationship_type)

            media_list = query.all()
            return [self._media_to_dict(media, base_url) for media in media_list]

    def get_user_media_stats(self, user_id: str) -> Dict:
        """Get media statistics for user"""
        with self.db_manager.get_session() as session:
            stats = {}

            # Total files and size
            result = session.query(
                func.count(Media.id),
                func.sum(Media.file_size)
            ).filter(and_(Media.user_id == user_id, Media.is_deleted.is_(False))).first()

            stats['total_files'] = result[0] or 0
            stats['total_size_bytes'] = result[1] or 0
            stats['total_size_mb'] = round((result[1] or 0) / (1024 * 1024), 2)

            # Count by mime type
            type_counts = session.query(
                Media.mime_type, func.count(Media.id)
            ).filter(and_(Media.user_id == user_id, Media.is_deleted.is_(False))).group_by(Media.mime_type).all()

            # Handle None mime_type values for JSON serialization
            stats['by_type'] = {(mime_type or 'unknown'): count for mime_type, count in type_counts}

            return stats

    def find_duplicate_files(self, user_id: str, base_url: str = None) -> List[List[Dict]]:
        """Find duplicate files for user based on file hash"""
        with self.db_manager.get_session() as session:
            # Find hashes that appear more than once
            duplicate_hashes = session.query(Media.file_hash).filter(
                and_(Media.user_id == user_id, Media.is_deleted == False, Media.file_hash.isnot(None))
            ).group_by(Media.file_hash).having(func.count(Media.id) > 1).all()

            duplicates = []
            for (file_hash,) in duplicate_hashes:
                media_list = session.query(Media).filter(
                    and_(Media.user_id == user_id, Media.file_hash == file_hash, Media.is_deleted == False)
                ).all()

                duplicates.append([self._media_to_dict(media, base_url) for media in media_list])

            return duplicates

    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return None

    def _media_to_dict(self, media: Media, base_url: str = None) -> Dict:
        """Convert Media model to dictionary with URLs"""
        try:
            # Generate URLs from relative paths
            file_url = None
            thumbnail_url = None

            if base_url and media.file_path:
                # Remove leading slash from relative path for URL construction
                # Handle None values safely
                if media.file_path and isinstance(media.file_path, str):
                    url_path = media.file_path[1:] if media.file_path.startswith('/') else media.file_path
                    file_url = f"{base_url}/file/{url_path}"

            if base_url and media.thumbnail_path:
                # Remove leading slash from relative path for URL construction
                # Handle None values safely
                if media.thumbnail_path and isinstance(media.thumbnail_path, str):
                    thumb_url_path = media.thumbnail_path[1:] if media.thumbnail_path.startswith('/') else media.thumbnail_path
                    thumbnail_url = f"{base_url}/file/{thumb_url_path}"

            # Safe attribute access with None checks
            def safe_str(value):
                return str(value) if value is not None else None

            def safe_isoformat(dt):
                try:
                    return dt.isoformat() if dt else None
                except (AttributeError, TypeError):
                    return None

            return {
                'id': media.id,
                'user_id': safe_str(media.user_id),
                'name': safe_str(media.name),  # User-provided display name
                'filename': safe_str(media.filename),
                'original_filename': safe_str(media.original_filename),
                'file_path': safe_str(media.file_path),  # Keep for backward compatibility
                'url': file_url,  # New field with full URL
                'file_size': media.file_size,
                'file_size_mb': media.get_file_size_mb() if hasattr(media, 'get_file_size_mb') else 0.0,
                'mime_type': safe_str(media.mime_type),
                'media_type': safe_str(media.media_type),
                'file_hash': safe_str(media.file_hash),
                'duration': media.duration,
                'width': media.width,
                'height': media.height,
                'codec': safe_str(media.codec),
                'bitrate': media.bitrate,
                'frame_rate': media.frame_rate,
                'thumbnail_path': safe_str(media.thumbnail_path),  # Keep for backward compatibility
                'thumbnail_url': thumbnail_url,  # New field with full URL
                'metadata': media.get_metadata() if hasattr(media, 'get_metadata') else {},
                'tags': media.get_tags() if hasattr(media, 'get_tags') else [],
                'description': safe_str(media.description),
                'uploaded_at': safe_isoformat(media.uploaded_at),
                'updated_at': safe_isoformat(media.updated_at)
            }
        except Exception as e:
            # If there's any error in conversion, provide minimal safe data
            return {
                'id': getattr(media, 'id', None),
                'user_id': getattr(media, 'user_id', None),
                'filename': getattr(media, 'filename', None),
                'error': f"Error converting media to dict: {str(e)}"
            }


# Global media manager instance
media_manager = MediaManager()
