"""
Database-based configuration manager with JSON fallback
"""
import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, List
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from .models import Setting, SettingCategory, ConfigHistory, db_manager


class ConfigManager:
    """
    Configuration manager that supports both database and JSON storage
    with automatic migration and fallback capabilities
    """
    
    def __init__(self, use_database: bool = True, json_fallback: bool = True):
        self.use_database = use_database
        self.json_fallback = json_fallback
        self._cache = {}
        self._cache_loaded = False
        
        # Import ROOT_DIR directly to avoid circular imports
        import os
        self.root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.params_file = f"{self.root_dir}/videotrans/params.json"
        self.cfg_file = f"{self.root_dir}/videotrans/cfg.json"
    
    def _load_cache(self) -> None:
        """Load all settings into cache"""
        if self._cache_loaded:
            return
        
        try:
            if self.use_database and self._is_database_available():
                self._load_from_database()
            else:
                self._load_from_json()
        except Exception as e:
            print(f"Error loading configuration: {e}")
            if self.json_fallback:
                self._load_from_json()
        
        self._cache_loaded = True
    
    def _is_database_available(self) -> bool:
        """Check if database is available and accessible"""
        try:
            with db_manager.get_session() as session:
                # Try a simple query to test database connectivity
                from sqlalchemy import text
                session.execute(text("SELECT 1")).fetchone()
                return True
        except Exception:
            return False
    
    def _load_from_database(self) -> None:
        """Load settings from database into cache"""
        try:
            with db_manager.get_session() as session:
                settings = session.query(Setting).all()
                for setting in settings:
                    self._cache[setting.key] = setting.get_typed_value()
        except SQLAlchemyError as e:
            print(f"Database error: {e}")
            raise
    
    def _load_from_json(self) -> None:
        """Load settings from JSON files into cache"""
        # Load params.json
        if os.path.exists(self.params_file):
            try:
                with open(self.params_file, 'r', encoding='utf-8') as f:
                    params_data = json.load(f)
                    self._cache.update(params_data)
            except Exception as e:
                print(f"Error loading params.json: {e}")
        
        # Load cfg.json
        if os.path.exists(self.cfg_file):
            try:
                with open(self.cfg_file, 'r', encoding='utf-8') as f:
                    cfg_data = json.load(f)
                    self._cache.update(cfg_data)
            except Exception as e:
                print(f"Error loading cfg.json: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value"""
        self._load_cache()
        return self._cache.get(key, default)
    
    def set(self, key: str, value: Any, category: str = None, description: str = None, 
            data_type: str = None, is_sensitive: bool = False) -> None:
        """Set a configuration value"""
        self._load_cache()
        
        # Update cache
        old_value = self._cache.get(key)
        self._cache[key] = value
        
        # Determine data type if not specified
        if data_type is None:
            data_type = self._infer_data_type(value)
        
        try:
            if self.use_database and self._is_database_available():
                self._set_in_database(key, value, category, description, data_type, is_sensitive, old_value)
            else:
                self._set_in_json(key, value)
        except Exception as e:
            print(f"Error saving configuration: {e}")
            if self.json_fallback:
                self._set_in_json(key, value)
    
    def _infer_data_type(self, value: Any) -> str:
        """Infer data type from value"""
        if isinstance(value, bool):
            return 'bool'
        elif isinstance(value, int):
            return 'int'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'
    
    def _set_in_database(self, key: str, value: Any, category: str, description: str, 
                        data_type: str, is_sensitive: bool, old_value: Any) -> None:
        """Save setting to database"""
        try:
            with db_manager.get_session() as session:
                # Get or create setting
                setting = session.query(Setting).filter(Setting.key == key).first()
                
                if setting is None:
                    setting = Setting(
                        key=key,
                        category=category,
                        description=description,
                        data_type=data_type,
                        is_sensitive=is_sensitive
                    )
                    session.add(setting)
                
                # Set the value
                setting.set_typed_value(value)
                
                # Record history if value changed
                if old_value != value:
                    history = ConfigHistory(
                        setting_key=key,
                        old_value=str(old_value) if old_value is not None else None,
                        new_value=str(value) if value is not None else None,
                        changed_by='system',
                        change_reason='config_update'
                    )
                    session.add(history)
                
                session.commit()
        except SQLAlchemyError as e:
            print(f"Database error saving {key}: {e}")
            raise
    
    def _set_in_json(self, key: str, value: Any) -> None:
        """Save setting to JSON file (fallback)"""
        # For JSON fallback, we'll save to params.json
        try:
            # Load existing data
            params_data = {}
            if os.path.exists(self.params_file):
                with open(self.params_file, 'r', encoding='utf-8') as f:
                    params_data = json.load(f)
            
            # Update and save
            params_data[key] = value
            with open(self.params_file, 'w', encoding='utf-8') as f:
                json.dump(params_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving to JSON: {e}")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values"""
        self._load_cache()
        return self._cache.copy()
    
    def get_by_category(self, category: str) -> Dict[str, Any]:
        """Get all settings in a specific category"""
        if not self.use_database or not self._is_database_available():
            # For JSON fallback, return all settings (no category support)
            return self.get_all()
        
        try:
            with db_manager.get_session() as session:
                settings = session.query(Setting).filter(Setting.category == category).all()
                return {setting.key: setting.get_typed_value() for setting in settings}
        except SQLAlchemyError:
            return {}
    
    def delete(self, key: str) -> bool:
        """Delete a configuration setting"""
        self._load_cache()
        
        if key not in self._cache:
            return False
        
        # Remove from cache
        old_value = self._cache.pop(key, None)
        
        try:
            if self.use_database and self._is_database_available():
                with db_manager.get_session() as session:
                    setting = session.query(Setting).filter(Setting.key == key).first()
                    if setting:
                        # Record deletion in history
                        history = ConfigHistory(
                            setting_key=key,
                            old_value=str(old_value) if old_value is not None else None,
                            new_value=None,
                            changed_by='system',
                            change_reason='config_delete'
                        )
                        session.add(history)
                        session.delete(setting)
                        session.commit()
            else:
                # Remove from JSON
                self._remove_from_json(key)
            return True
        except Exception as e:
            print(f"Error deleting configuration {key}: {e}")
            return False
    
    def _remove_from_json(self, key: str) -> None:
        """Remove setting from JSON file"""
        try:
            if os.path.exists(self.params_file):
                with open(self.params_file, 'r', encoding='utf-8') as f:
                    params_data = json.load(f)
                
                if key in params_data:
                    del params_data[key]
                    with open(self.params_file, 'w', encoding='utf-8') as f:
                        json.dump(params_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error removing from JSON: {e}")
    
    def clear_cache(self) -> None:
        """Clear the configuration cache"""
        self._cache.clear()
        self._cache_loaded = False
    
    def reload(self) -> None:
        """Reload configuration from storage"""
        self.clear_cache()
        self._load_cache()


# Global configuration manager instance - initialized lazily to avoid circular imports
_config_manager = None

def get_config_manager():
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

# For backward compatibility - use get_config_manager() function instead
