"""
Migration utilities for transferring data from SQLite to PostgreSQL
"""
import os
from typing import Dict, Any, List
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from .models import Setting, SettingCategory, ConfigHistory, Base
from .postgres_config import postgres_config


class SQLiteToPostgreSQLMigrator:
    """Migrates configuration data from SQLite to PostgreSQL"""
    
    def __init__(self):
        from videotrans.configure import config
        self.sqlite_path = f"{config.ROOT_DIR}/videotrans/config.db"
        self.sqlite_url = f"sqlite:///{self.sqlite_path}"
        self.postgres_url = postgres_config.get_database_url()
        
        self.sqlite_engine = None
        self.postgres_engine = None
        self.sqlite_session = None
        self.postgres_session = None
    
    def check_sqlite_exists(self) -> bool:
        """Check if SQLite database exists and has data"""
        return os.path.exists(self.sqlite_path)
    
    def check_postgres_connection(self) -> bool:
        """Check if PostgreSQL connection is working"""
        return postgres_config.test_connection()
    
    def setup_connections(self) -> bool:
        """Setup database connections"""
        try:
            # SQLite connection
            if self.check_sqlite_exists():
                self.sqlite_engine = create_engine(
                    self.sqlite_url,
                    echo=False,
                    connect_args={"check_same_thread": False}
                )
                SQLiteSession = sessionmaker(bind=self.sqlite_engine)
                self.sqlite_session = SQLiteSession()
            
            # PostgreSQL connection
            if not self.check_postgres_connection():
                print("PostgreSQL connection failed")
                return False
            
            # Ensure database exists
            postgres_config.create_database_if_not_exists()
            
            self.postgres_engine = create_engine(
                self.postgres_url,
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True
            )
            PostgresSession = sessionmaker(bind=self.postgres_engine)
            self.postgres_session = PostgresSession()
            
            # Create tables in PostgreSQL
            Base.metadata.create_all(bind=self.postgres_engine)
            
            return True
            
        except Exception as e:
            print(f"Error setting up database connections: {e}")
            return False
    
    def get_sqlite_data(self) -> Dict[str, List]:
        """Extract all data from SQLite database"""
        if not self.sqlite_session:
            return {}
        
        try:
            data = {
                'settings': [],
                'categories': [],
                'history': []
            }
            
            # Get settings
            settings = self.sqlite_session.query(Setting).all()
            for setting in settings:
                data['settings'].append({
                    'key': setting.key,
                    'value': setting.value,
                    'data_type': setting.data_type,
                    'category': setting.category,
                    'description': setting.description,
                    'default_value': setting.default_value,
                    'is_sensitive': setting.is_sensitive,
                    'created_at': setting.created_at,
                    'updated_at': setting.updated_at
                })
            
            # Get categories
            categories = self.sqlite_session.query(SettingCategory).all()
            for category in categories:
                data['categories'].append({
                    'name': category.name,
                    'description': category.description,
                    'display_order': category.display_order,
                    'created_at': category.created_at,
                    'updated_at': category.updated_at
                })
            
            # Get history
            history = self.sqlite_session.query(ConfigHistory).all()
            for record in history:
                data['history'].append({
                    'setting_key': record.setting_key,
                    'old_value': record.old_value,
                    'new_value': record.new_value,
                    'changed_by': record.changed_by,
                    'change_reason': record.change_reason,
                    'created_at': record.created_at
                })
            
            return data
            
        except Exception as e:
            print(f"Error extracting SQLite data: {e}")
            return {}
    
    def insert_postgres_data(self, data: Dict[str, List]) -> bool:
        """Insert data into PostgreSQL database"""
        if not self.postgres_session:
            return False
        
        try:
            # Clear existing data
            self.postgres_session.query(ConfigHistory).delete()
            self.postgres_session.query(Setting).delete()
            self.postgres_session.query(SettingCategory).delete()
            
            # Insert categories
            for cat_data in data.get('categories', []):
                category = SettingCategory(
                    name=cat_data['name'],
                    description=cat_data['description'],
                    display_order=cat_data['display_order']
                )
                self.postgres_session.add(category)
            
            # Insert settings
            for setting_data in data.get('settings', []):
                setting = Setting(
                    key=setting_data['key'],
                    value=setting_data['value'],
                    data_type=setting_data['data_type'],
                    category=setting_data['category'],
                    description=setting_data['description'],
                    default_value=setting_data['default_value'],
                    is_sensitive=setting_data['is_sensitive']
                )
                self.postgres_session.add(setting)
            
            # Insert history
            for hist_data in data.get('history', []):
                history = ConfigHistory(
                    setting_key=hist_data['setting_key'],
                    old_value=hist_data['old_value'],
                    new_value=hist_data['new_value'],
                    changed_by=hist_data['changed_by'],
                    change_reason=hist_data['change_reason']
                )
                self.postgres_session.add(history)
            
            self.postgres_session.commit()
            return True
            
        except Exception as e:
            print(f"Error inserting PostgreSQL data: {e}")
            self.postgres_session.rollback()
            return False
    
    def migrate(self) -> bool:
        """Perform complete migration from SQLite to PostgreSQL"""
        print("Starting migration from SQLite to PostgreSQL...")
        
        # Check prerequisites
        if not self.check_sqlite_exists():
            print("No SQLite database found to migrate")
            return False
        
        if not self.check_postgres_connection():
            print("PostgreSQL connection failed")
            return False
        
        # Setup connections
        if not self.setup_connections():
            print("Failed to setup database connections")
            return False
        
        try:
            # Extract data from SQLite
            print("Extracting data from SQLite...")
            data = self.get_sqlite_data()
            
            if not data:
                print("No data found in SQLite database")
                return False
            
            print(f"Found {len(data.get('settings', []))} settings, "
                  f"{len(data.get('categories', []))} categories, "
                  f"{len(data.get('history', []))} history records")
            
            # Insert data into PostgreSQL
            print("Inserting data into PostgreSQL...")
            if not self.insert_postgres_data(data):
                print("Failed to insert data into PostgreSQL")
                return False
            
            print("Migration completed successfully!")
            
            # Create backup of SQLite file
            self._backup_sqlite()
            
            return True
            
        except Exception as e:
            print(f"Migration failed: {e}")
            return False
        
        finally:
            self._cleanup_connections()
    
    def _backup_sqlite(self):
        """Create backup of SQLite database"""
        try:
            import shutil
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.sqlite_path}.backup_{timestamp}"
            shutil.copy2(self.sqlite_path, backup_path)
            print(f"SQLite database backed up to: {backup_path}")
        except Exception as e:
            print(f"Failed to backup SQLite database: {e}")
    
    def _cleanup_connections(self):
        """Clean up database connections"""
        try:
            if self.sqlite_session:
                self.sqlite_session.close()
            if self.postgres_session:
                self.postgres_session.close()
            if self.sqlite_engine:
                self.sqlite_engine.dispose()
            if self.postgres_engine:
                self.postgres_engine.dispose()
        except Exception as e:
            print(f"Error cleaning up connections: {e}")
    
    def verify_migration(self) -> bool:
        """Verify that migration was successful"""
        try:
            if not self.postgres_session:
                self.setup_connections()
            
            # Count records in PostgreSQL
            settings_count = self.postgres_session.query(Setting).count()
            categories_count = self.postgres_session.query(SettingCategory).count()
            history_count = self.postgres_session.query(ConfigHistory).count()
            
            print(f"PostgreSQL verification:")
            print(f"  Settings: {settings_count}")
            print(f"  Categories: {categories_count}")
            print(f"  History: {history_count}")
            
            return settings_count > 0
            
        except Exception as e:
            print(f"Error verifying migration: {e}")
            return False


def migrate_to_postgresql() -> bool:
    """Convenience function to run PostgreSQL migration"""
    migrator = SQLiteToPostgreSQLMigrator()
    return migrator.migrate()
