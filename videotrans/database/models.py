"""
Database models for configuration management, task management, media library, and user authentication
"""
import json
from datetime import datetime
from typing import Any, Optional, Union
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.sql import func
from flask_login import UserMixin
import secrets

Base = declarative_base()


class User(Base, UserMixin):
    """User model for authentication"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255))  # For username/password auth

    # Google OAuth fields
    google_id = Column(String(100), unique=True, index=True)  # Google user ID
    google_email = Column(String(120))  # Email from Google (might differ from primary email)

    # API authentication
    api_key = Column(String(64), unique=True, index=True)  # For API access
    api_key_created_at = Column(DateTime)

    # User status and metadata
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    email_verified = Column(Boolean, default=False)

    # Profile information
    first_name = Column(String(50))
    last_name = Column(String(50))
    profile_picture = Column(String(255))  # URL to profile picture

    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_login_at = Column(DateTime)

    # Relationships
    tasks = relationship("Task", back_populates="user", cascade="all, delete-orphan")
    media = relationship("Media", back_populates="user", cascade="all, delete-orphan")

    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_user_email_active', 'email', 'is_active'),
        Index('idx_user_username_active', 'username', 'is_active'),
        Index('idx_user_google_id', 'google_id'),
        Index('idx_user_api_key', 'api_key'),
    )

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"

    def get_id(self):
        """Required by Flask-Login"""
        return str(self.id)

    def generate_api_key(self):
        """Generate a new API key for the user"""
        self.api_key = secrets.token_urlsafe(32)
        self.api_key_created_at = func.now()
        return self.api_key

    def check_password(self, password):
        """Check if provided password matches the stored hash"""
        if not self.password_hash:
            return False
        try:
            from flask_bcrypt import check_password_hash
            return check_password_hash(self.password_hash, password)
        except ImportError:
            # Fallback to basic comparison (not recommended for production)
            return self.password_hash == password

    def set_password(self, password):
        """Set password hash"""
        try:
            from flask_bcrypt import generate_password_hash
            self.password_hash = generate_password_hash(password).decode('utf-8')
        except ImportError:
            # Fallback to plain text (not recommended for production)
            self.password_hash = password

    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'profile_picture': self.profile_picture,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'email_verified': self.email_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
        }

        if include_sensitive:
            data.update({
                'google_id': self.google_id,
                'google_email': self.google_email,
                'api_key': self.api_key,
                'api_key_created_at': self.api_key_created_at.isoformat() if self.api_key_created_at else None,
            })

        return data


class SettingCategory(Base):
    """Categories for organizing settings"""
    __tablename__ = 'setting_categories'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    display_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<SettingCategory(name='{self.name}')>"


class Setting(Base):
    """Individual configuration settings"""
    __tablename__ = 'settings'

    id = Column(Integer, primary_key=True)
    key = Column(String(255), unique=True, nullable=False, index=True)
    value = Column(Text)
    data_type = Column(String(50), default='string')  # string, int, float, bool, json
    category = Column(String(100))
    description = Column(Text)
    default_value = Column(Text)
    is_sensitive = Column(Boolean, default=False)  # For API keys, passwords, etc.
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Setting(key='{self.key}', value='{self.value}')>"

    def get_typed_value(self) -> Any:
        """Convert stored string value to appropriate Python type"""
        if self.value is None:
            return self.get_typed_default()

        try:
            if self.data_type == 'int':
                return int(self.value)
            elif self.data_type == 'float':
                return float(self.value)
            elif self.data_type == 'bool':
                return self.value.lower() in ('true', '1', 'yes', 'on')
            elif self.data_type == 'json':
                return json.loads(self.value)
            else:  # string
                return self.value
        except (ValueError, json.JSONDecodeError):
            # If conversion fails, return default or string value
            return self.get_typed_default() or self.value

    def get_typed_default(self) -> Any:
        """Convert stored default value to appropriate Python type"""
        if self.default_value is None:
            return None

        try:
            if self.data_type == 'int':
                return int(self.default_value)
            elif self.data_type == 'float':
                return float(self.default_value)
            elif self.data_type == 'bool':
                return self.default_value.lower() in ('true', '1', 'yes', 'on')
            elif self.data_type == 'json':
                return json.loads(self.default_value)
            else:  # string
                return self.default_value
        except (ValueError, json.JSONDecodeError):
            return self.default_value

    def set_typed_value(self, value: Any) -> None:
        """Set value with automatic type conversion"""
        if value is None:
            self.value = None
            return

        if self.data_type == 'json':
            self.value = json.dumps(value, ensure_ascii=False)
        elif self.data_type == 'bool':
            self.value = str(bool(value)).lower()
        else:
            self.value = str(value)


class ConfigHistory(Base):
    """History of configuration changes"""
    __tablename__ = 'config_history'

    id = Column(Integer, primary_key=True)
    setting_key = Column(String(255), nullable=False, index=True)
    old_value = Column(Text)
    new_value = Column(Text)
    changed_by = Column(String(100))  # user/system identifier
    change_reason = Column(String(255))  # migration, user_update, etc.
    created_at = Column(DateTime, default=func.now())

    def __repr__(self):
        return f"<ConfigHistory(key='{self.setting_key}', changed_at='{self.created_at}')>"


class DatabaseManager:
    """Database connection and session management"""

    def __init__(self, database_url: str = None, use_postgresql: bool = True):
        self.use_postgresql = use_postgresql
        self.database_url = database_url
        self._initialized = False
        self._setup_database_url()
        self._create_engine()

    def _setup_database_url(self):
        """Setup database URL based on configuration"""
        if self.database_url:
            return

        if self.use_postgresql:
            try:
                # Import here to avoid circular imports
                import sys
                import os
                sys.path.insert(0, os.path.dirname(__file__))
                from postgres_config import PostgreSQLConfig

                postgres_config = PostgreSQLConfig()
                self.database_url = postgres_config.get_database_url()
            except Exception as e:
                print(f"PostgreSQL setup failed: {e}, falling back to SQLite")
                self.use_postgresql = False

        if not self.use_postgresql:
            # Fallback to SQLite
            from videotrans.configure import config
            self.database_url = f"sqlite:///{config.ROOT_DIR}/videotrans/config.db"
            print(f"Using SQLite: {self.database_url}")

    def _create_engine(self):
        """Create database engine with appropriate settings"""
        if self.use_postgresql:
            # PostgreSQL specific settings
            self.engine = create_engine(
                self.database_url,
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600
            )
        else:
            # SQLite specific settings
            self.engine = create_engine(
                self.database_url,
                echo=False,
                connect_args={"check_same_thread": False}
            )

        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

    def initialize_database(self):
        """Create all tables if they don't exist"""
        if not self._initialized:
            if self.use_postgresql:
                # Ensure PostgreSQL database exists
                self._ensure_postgresql_database()

            Base.metadata.create_all(bind=self.engine)
            self._initialized = True

    def _ensure_postgresql_database(self):
        """Ensure PostgreSQL database exists"""
        try:
            # Import here to avoid circular imports
            import sys
            import os
            sys.path.insert(0, os.path.dirname(__file__))
            from postgres_config import PostgreSQLConfig

            postgres_config = PostgreSQLConfig()
            postgres_config.create_database_if_not_exists()
        except Exception as e:
            print(f"Error ensuring PostgreSQL database exists: {e}")

    def get_session(self) -> Session:
        """Get a database session"""
        self.initialize_database()
        return self.SessionLocal()

    def close(self):
        """Close database connections"""
        if hasattr(self, 'engine'):
            self.engine.dispose()


# Task Management Models

class Task(Base):
    """Main task record for user-scoped task management"""
    __tablename__ = 'tasks'

    id = Column(Integer, primary_key=True)
    uuid = Column(String(36), unique=True, nullable=False, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    task_type = Column(String(50), nullable=False)  # TransCreate, DubbingSrt, etc.
    status = Column(String(20), default='pending')  # pending, running, completed, failed, stopped

    # Task configuration (JSON)
    config = Column(Text)  # Serialized task configuration

    # Progress tracking
    progress_percent = Column(Float, default=0.0)
    status_text = Column(Text)
    current_stage = Column(String(50))  # prepare, recogn, trans, dubb, etc.

    # File paths
    source_file = Column(String(500))
    target_dir = Column(String(500))
    output_files = Column(Text)  # JSON array of output files

    # Timing
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Error handling
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)

    # Relationships
    user = relationship("User", back_populates="tasks")
    stages = relationship("TaskStage", back_populates="task", cascade="all, delete-orphan")
    logs = relationship("TaskLog", back_populates="task", cascade="all, delete-orphan")

    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_task_user_status', 'user_id', 'status'),
        Index('idx_task_user_created', 'user_id', 'created_at'),
        Index('idx_task_user_type', 'user_id', 'task_type'),
    )

    def __repr__(self):
        return f"<Task(uuid='{self.uuid}', user_id='{self.user_id}', type='{self.task_type}', status='{self.status}')>"

    def get_config(self) -> dict:
        """Get task configuration as dictionary"""
        if self.config:
            try:
                return json.loads(self.config)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_config(self, config_dict: dict):
        """Set task configuration from dictionary"""
        self.config = json.dumps(config_dict) if config_dict else None

    def get_output_files(self) -> list:
        """Get output files as list"""
        if self.output_files:
            try:
                return json.loads(self.output_files)
            except json.JSONDecodeError:
                return []
        return []

    def set_output_files(self, files: list):
        """Set output files from list"""
        self.output_files = json.dumps(files) if files else None


class TaskStage(Base):
    """Individual task stage records for tracking processing stages"""
    __tablename__ = 'task_stages'

    id = Column(Integer, primary_key=True)
    task_uuid = Column(String(36), ForeignKey('tasks.uuid'), nullable=False, index=True)
    stage_name = Column(String(50), nullable=False)  # prepare, recogn, trans, etc.
    status = Column(String(20), default='pending')  # pending, running, completed, failed

    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    duration_seconds = Column(Float)

    # Stage-specific data
    input_data = Column(Text)  # JSON
    output_data = Column(Text)  # JSON
    error_message = Column(Text)

    created_at = Column(DateTime, default=func.now())

    # Relationships
    task = relationship("Task", back_populates="stages")

    # Indexes
    __table_args__ = (
        Index('idx_stage_task_name', 'task_uuid', 'stage_name'),
        Index('idx_stage_task_status', 'task_uuid', 'status'),
    )

    def __repr__(self):
        return f"<TaskStage(task_uuid='{self.task_uuid}', stage='{self.stage_name}', status='{self.status}')>"

    def get_input_data(self) -> dict:
        """Get input data as dictionary"""
        if self.input_data:
            try:
                return json.loads(self.input_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_input_data(self, data: dict):
        """Set input data from dictionary"""
        self.input_data = json.dumps(data) if data else None

    def get_output_data(self) -> dict:
        """Get output data as dictionary"""
        if self.output_data:
            try:
                return json.loads(self.output_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_output_data(self, data: dict):
        """Set output data from dictionary"""
        self.output_data = json.dumps(data) if data else None


class TaskLog(Base):
    """Task execution logs for detailed tracking"""
    __tablename__ = 'task_logs'

    id = Column(Integer, primary_key=True)
    task_uuid = Column(String(36), ForeignKey('tasks.uuid'), nullable=False, index=True)
    log_level = Column(String(10), default='info')  # info, warning, error, debug
    message = Column(Text, nullable=False)
    stage = Column(String(50))  # Optional stage context

    created_at = Column(DateTime, default=func.now())

    # Relationships
    task = relationship("Task", back_populates="logs")

    # Indexes
    __table_args__ = (
        Index('idx_log_task_level', 'task_uuid', 'log_level'),
        Index('idx_log_task_created', 'task_uuid', 'created_at'),
    )

    def __repr__(self):
        return f"<TaskLog(task_uuid='{self.task_uuid}', level='{self.log_level}', message='{self.message[:50]}...')>"


# Media Library Models

class Media(Base):
    """Media library for tracking uploaded files"""
    __tablename__ = 'media'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)

    # File information
    name = Column(String(255))  # User-provided display name (optional)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # Size in bytes
    mime_type = Column(String(100))
    media_type = Column(String(20))  # 'video', 'audio', 'image', 'other'
    file_hash = Column(String(64))  # SHA-256 hash for deduplication

    # Media metadata
    duration = Column(Float)  # Duration in seconds for audio/video
    width = Column(Integer)  # Width for images/videos
    height = Column(Integer)  # Height for images/videos
    codec = Column(String(50))  # Audio/video codec
    bitrate = Column(Integer)  # Bitrate for audio/video
    frame_rate = Column(Float)  # Frame rate for videos

    # Additional metadata
    thumbnail_path = Column(String(500))  # Path to generated thumbnail
    media_metadata = Column(Text)  # JSON for additional custom metadata
    tags = Column(Text)  # JSON array of tags
    description = Column(Text)

    # Lifecycle management
    is_deleted = Column(Boolean, default=False)  # Soft delete
    deleted_at = Column(DateTime)

    # Timestamps
    uploaded_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="media")

    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_media_user_uploaded', 'user_id', 'uploaded_at'),
        Index('idx_media_user_type', 'user_id', 'mime_type'),
        Index('idx_media_user_media_type', 'user_id', 'media_type'),
        Index('idx_media_user_deleted', 'user_id', 'is_deleted'),
        Index('idx_media_hash', 'file_hash'),
    )

    def __repr__(self):
        return f"<Media(id={self.id}, user_id='{self.user_id}', filename='{self.filename}')>"

    def get_metadata(self) -> dict:
        """Get metadata as dictionary"""
        if self.media_metadata:
            try:
                return json.loads(self.media_metadata)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_metadata(self, metadata_dict: dict):
        """Set metadata from dictionary"""
        self.media_metadata = json.dumps(metadata_dict) if metadata_dict else None

    def get_tags(self) -> list:
        """Get tags as list"""
        if self.tags:
            try:
                return json.loads(self.tags)
            except json.JSONDecodeError:
                return []
        return []

    def set_tags(self, tags_list: list):
        """Set tags from list"""
        self.tags = json.dumps(tags_list) if tags_list else None

    def get_file_size_mb(self) -> float:
        """Get file size in MB"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0.0


class TaskMedia(Base):
    """Association table for tasks and media files"""
    __tablename__ = 'task_media'

    id = Column(Integer, primary_key=True)
    task_uuid = Column(String(36), ForeignKey('tasks.uuid'), nullable=False)
    media_id = Column(Integer, ForeignKey('media.id'), nullable=False)
    relationship_type = Column(String(20), default='input')  # input, output, thumbnail

    created_at = Column(DateTime, default=func.now())

    # Indexes
    __table_args__ = (
        Index('idx_task_media_task', 'task_uuid'),
        Index('idx_task_media_media', 'media_id'),
        Index('idx_task_media_type', 'task_uuid', 'relationship_type'),
    )

    def __repr__(self):
        return f"<TaskMedia(task_uuid='{self.task_uuid}', media_id={self.media_id}, type='{self.relationship_type}')>"


# Global database manager instance
db_manager = DatabaseManager()
