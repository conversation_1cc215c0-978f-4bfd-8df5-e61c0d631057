import os
import time
import uuid
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
import threading
import json

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config
from videotrans.util import tools


class BaseVideoGen(ABC):
  """Base class for video generation services"""

  def __init__(self, prompt: str = None, image_path: str = None,
               cache_folder: str = None, uuid_str: str = None,
               inst=None, **kwargs):
    """
    Initialize video generation service

    Args:
        prompt: Text prompt for video generation
        image_path: Path to image for image-to-video generation
        cache_folder: Directory for temporary files
        uuid_str: Unique identifier for this task
        inst: Instance object for progress reporting
        **kwargs: Additional service-specific parameters
    """
    self.prompt = prompt or ""
    self.image_path = image_path
    self.cache_folder = cache_folder or config.TEMP_DIR
    self.uuid = uuid_str or str(uuid.uuid4())
    self.inst = inst

    # Task management
    self.task_id = None
    self.status = "pending"  # pending, queued, running, succeeded, failed, cancelled
    self.error = ""
    self.video_url = ""
    self.local_video_path = ""
    self.progress = 0

    # Service configuration
    self.api_url = ""
    self.proxies = None
    self.timeout = 300  # 5 minutes default timeout

    # Threading
    self._stop_event = threading.Event()
    self._thread = None

    # Ensure cache folder exists
    Path(self.cache_folder).mkdir(parents=True, exist_ok=True)

  @abstractmethod
  def is_configured(self) -> bool:
    """Check if the service is properly configured"""
    pass

  @abstractmethod
  def _create_task(self) -> str:
    """Create video generation task and return task ID"""
    pass

  @abstractmethod
  def _check_status(self, task_id: str) -> Dict[str, Any]:
    """Check task status and return status information"""
    pass

  @abstractmethod
  def _download_video(self, video_url: str) -> str:
    """Download generated video and return local path"""
    pass

  def generate_video(self, async_mode: bool = True) -> Union[str, Dict[str, Any]]:
    """
    Generate video from prompt/image

    Args:
        async_mode: If True, return immediately with task info. If False, wait for completion.

    Returns:
        If async_mode=True: Dict with task_id and status
        If async_mode=False: Path to generated video file
    """
    try:
      if not self.is_configured():
        raise Exception("Video generation service is not properly configured")

      # Validate inputs
      # if not self.prompt and not self.image_path:
      #   raise Exception("Either prompt or image_path must be provided")

      # if self.image_path and not Path(self.image_path).exists():
      #   raise Exception(f"Image file not found: {self.image_path}")

      # Create task
      self._signal("Creating video generation task...")
      self.task_id = self._create_task()
      self.status = "queued"

      if async_mode:
        # Start monitoring thread
        self._thread = threading.Thread(target=self._monitor_task)
        self._thread.daemon = True
        self._thread.start()

        return {
          "task_id": self.task_id,
          "uuid": self.uuid,
          "status": self.status
        }
      else:
        # Wait for completion
        return self._wait_for_completion()

    except Exception as e:
      self.error = str(e)
      self.status = "failed"
      config.logger.exception(e, exc_info=True)
      raise

  def _monitor_task(self):
    """Monitor task progress in background thread"""
    try:
      while not self._stop_event.is_set() and self.status in ["queued", "running"]:
        status_info = self._check_status(self.task_id)
        self._update_status(status_info)

        if self.status in ["succeeded", "failed", "cancelled"]:
          break

        # Wait before next check
        self._stop_event.wait(10)  # Check every 10 seconds

    except Exception as e:
      self.error = str(e)
      self.status = "failed"
      config.logger.exception(e, exc_info=True)

  def _wait_for_completion(self, max_wait: int = 600) -> str:
    """Wait for task completion and return video path"""
    start_time = time.time()

    while time.time() - start_time < max_wait:
      if self._stop_event.is_set():
        raise Exception("Task was cancelled")

      status_info = self._check_status(self.task_id)
      self._update_status(status_info)

      if self.status == "succeeded":
        return self.local_video_path
      elif self.status == "failed":
        raise Exception(f"Video generation failed: {self.error}")
      elif self.status == "cancelled":
        raise Exception("Video generation was cancelled")

      time.sleep(10)  # Check every 10 seconds

    raise Exception(f"Video generation timed out after {max_wait} seconds")

  def _update_status(self, status_info: Dict[str, Any]):
    """Update task status from API response"""
    self.status = status_info.get("status", self.status)

    if "error" in status_info:
      self.error = status_info["error"]

    if self.status == "succeeded" and "video_url" in status_info:
      self.video_url = status_info["video_url"]
      try:
        self.local_video_path = self._download_video(self.video_url)
        self._signal(f"Video generation completed: {self.local_video_path}")
      except Exception as e:
        self.error = f"Failed to download video: {str(e)}"
        self.status = "failed"

    # Update progress
    if self.status == "queued":
      self.progress = 10
    elif self.status == "running":
      self.progress = 50
    elif self.status == "succeeded":
      self.progress = 100
    elif self.status == "failed":
      self.progress = 0

    # Signal progress
    self._signal(f"Video generation {self.status}: {self.progress}%")

  def get_status(self) -> Dict[str, Any]:
    """Get current task status"""
    return {
      "task_id": self.task_id,
      "uuid": self.uuid,
      "status": self.status,
      "progress": self.progress,
      "error": self.error,
      "video_url": self.video_url,
      "local_video_path": self.local_video_path
    }

  def cancel(self):
    """Cancel video generation task"""
    self._stop_event.set()
    self.status = "cancelled"
    if self._thread and self._thread.is_alive():
      self._thread.join(timeout=5)

  def _signal(self, text: str, type: str = 'logs', progress: int = None):
    """Send progress signal"""
    if self.inst and hasattr(self.inst, 'status_text'):
      self.inst.status_text = text

    if progress is not None:
      self.progress = progress

    # Also use tools.set_process if available
    try:
      tools.set_process(text=text, uuid=self.uuid, type=type)
    except:
      pass

    config.logger.info(f"[VideoGen {self.uuid}] {text}")

  def _exit(self) -> bool:
    """Check if should exit"""
    return self._stop_event.is_set() or config.exit_soft

  def cleanup(self):
    """Clean up temporary files"""
    try:
      if self.local_video_path and Path(self.local_video_path).exists():
        # Don't delete the final video file, just temp files
        pass
    except Exception as e:
      config.logger.warning(f"Failed to cleanup video generation files: {e}")
