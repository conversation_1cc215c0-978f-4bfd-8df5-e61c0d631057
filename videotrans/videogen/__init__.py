# Video Generation Services
from videotrans.videogen._base import BaseVideoGen
from videotrans.videogen._byteplus import BytePlusVideoGen
from videotrans.videogen._minimax import MinimaxVideoGen

__all__ = ['BaseVideoGen', 'BytePlusVideoGen', 'MinimaxVideoGen']

# Video generation service types
VIDEO_GEN_TYPES = {
    0: "BytePlus ModelArk",
    1: "Minimax",
}

def get_video_gen_service(videogen_type: int = 0, **kwargs):
    """Get video generation service instance by type"""
    if videogen_type == 0:
        return BytePlusVideoGen(**kwargs)
    elif videogen_type == 1:
        return MinimaxVideoGen(**kwargs)
    else:
        raise ValueError(f"Unsupported video generation type: {videogen_type}")

def is_allow_videogen(videogen_type: int = 0, return_str: bool = False):
    """Check if video generation service is available and configured"""
    try:
        service = get_video_gen_service(videogen_type)
        return service.is_configured()
    except Exception as e:
        if return_str:
            return str(e)
        return False

def get_videogen_types():
    """Get available video generation service types"""
    return VIDEO_GEN_TYPES
