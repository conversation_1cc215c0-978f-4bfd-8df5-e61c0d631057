import base64
import json
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
import requests
import mimetypes

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config
from videotrans.videogen._base import BaseVideoGen
from videotrans.util import tools


class BytePlusVideoGen(BaseVideoGen):
    """BytePlus ModelArk Video Generation Service"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # BytePlus API configuration
        self.api_base = "https://ark.ap-southeast.bytepluses.com/api/v3"
        self.model = "seedance-1-0-lite-t2v"  # Default model

        # Set up proxy if needed
        if not re.search('localhost', self.api_base) and not re.match(r'^https?://(\d+\.){3}\d+(:\d+)?', self.api_base):
            pro = self._set_proxy(type='set')
            if pro:
                self.proxies = pro

    def is_configured(self) -> bool:
        """Check if BytePlus service is properly configured"""
        api_key = enhanced_config.get_api_key('byteplus_modelark')
        if not api_key:
            return False
        return True

    def _get_headers(self) -> Dict[str, str]:
        """Get request headers with authentication"""
        api_key = enhanced_config.get_api_key('byteplus_modelark')
        if not api_key:
            raise Exception('BytePlus ModelArk API key not configured. Please set it in the configuration.')

        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}',
            'User-Agent': 'PyVideoTrans/1.0'
        }

    def _create_task(self) -> str:
        """Create video generation task and return task ID"""
        url = f"{self.api_base}/contents/generations/tasks"
        headers = self._get_headers()

        # Prepare content array
        content = []

        # Add text prompt if provided
        if self.prompt:
            # Check if prompt contains parameters (like --ratio 16:9)
            text_content = {
                "type": "text",
                "text": self.prompt.strip()
            }
            content.append(text_content)

        # Add image if provided
        if self.image_path:
            # image_content = self._prepare_image_content()
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": self.image_path
                }
            })

        # Prepare request payload
        payload = {
            "model": self.model,
            "content": content
        }

        # Add callback URL if configured
        callback_url = enhanced_config.config_manager.get('byteplus_callback_url', '')
        if callback_url:
            payload["callback_url"] = callback_url

        try:
            self._signal("Sending video generation request to BytePlus ModelArk...")

            response = requests.post(
                url,
                headers=headers,
                json=payload,
                proxies=self.proxies,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg = error_data['error'].get('message', error_msg)
                except:
                    error_msg = f"{error_msg}: {response.text}"
                raise Exception(error_msg)

            result = response.json()

            if 'id' not in result:
                raise Exception(f"Invalid response format: {result}")

            task_id = result['id']
            self._signal(f"Video generation task created: {task_id}")

            return task_id

        except requests.exceptions.ProxyError as e:
            proxy = None if not self.proxies else f'{list(self.proxies.values())[0]}'
            raise Exception(f'Proxy error, please check: {proxy=} {e}')
        except requests.exceptions.RequestException as e:
            raise Exception(f'Network error: {str(e)}')
        except Exception as e:
            config.logger.exception(e, exc_info=True)
            raise

    def _prepare_image_content(self) -> Dict[str, Any]:
        """Prepare image content for API request"""
        if not self.image_path or not Path(self.image_path).exists():
            raise Exception(f"Image file not found: {self.image_path}")

        # Check file size (max 10MB)
        file_size = Path(self.image_path).stat().st_size
        if file_size > 10 * 1024 * 1024:  # 10MB
            raise Exception(f"Image file too large: {file_size / (1024*1024):.1f}MB (max 10MB)")

        # Check file format
        mime_type, _ = mimetypes.guess_type(self.image_path)
        if not mime_type or not mime_type.startswith('image/'):
            raise Exception(f"Unsupported image format: {self.image_path}")

        allowed_formats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff', 'image/gif']
        if mime_type not in allowed_formats:
            raise Exception(f"Unsupported image format: {mime_type}. Supported: JPEG, PNG, WEBP, BMP, TIFF, GIF")

        # Read and encode image
        try:
            with open(self.image_path, 'rb') as f:
                image_data = f.read()

            # Encode as base64
            base64_data = base64.b64encode(image_data).decode('utf-8')

            return {
                "type": "image_url",
                "image_url": {
                    "url": f"data:{mime_type};base64,{base64_data}"
                }
            }

        except Exception as e:
            raise Exception(f"Failed to process image file: {str(e)}")

    def _check_status(self, task_id: str) -> Dict[str, Any]:
        """Check task status and return status information"""
        url = f"{self.api_base}/contents/generations/tasks/{task_id}"
        headers = self._get_headers()

        try:
            response = requests.get(
                url,
                headers=headers,
                proxies=self.proxies,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"Status check failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg = error_data['error'].get('message', error_msg)
                except:
                    error_msg = f"{error_msg}: {response.text}"
                return {"status": "failed", "error": error_msg, "task_id": task_id}

            result = response.json()

            status_info = {
                "status": result.get("status", "unknown"),
                "task_id": task_id  # Always use the task_id we passed in
            }

            # Handle different status cases
            if result.get("status") == "succeeded":
                content = result.get("content", {})
                if "video_url" in content:
                    status_info["video_url"] = content["video_url"]
                else:
                    status_info["status"] = "failed"
                    status_info["error"] = "No video URL in successful response"

            elif result.get("status") == "failed":
                status_info["error"] = result.get("error", "Video generation failed")

            return status_info

        except requests.exceptions.RequestException as e:
            return {"status": "failed", "error": f"Network error checking status: {str(e)}", "task_id": task_id}
        except Exception as e:
            config.logger.exception(e, exc_info=True)
            return {"status": "failed", "error": f"Error checking status: {str(e)}", "task_id": task_id}

    def _download_video(self, video_url: str) -> str:
        """Download generated video and return local path"""
        if not video_url:
            raise Exception("No video URL provided")

        # Generate local filename
        timestamp = int(time.time())
        filename = f"generated_video_{self.uuid}_{timestamp}.mp4"
        local_path = os.path.join(self.cache_folder, filename)

        try:
            self._signal("Downloading generated video...")

            response = requests.get(
                video_url,
                proxies=self.proxies,
                timeout=300,  # 5 minutes for video download
                stream=True
            )
            response.raise_for_status()

            # Download with progress
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        if total_size > 0:
                            progress = int((downloaded / total_size) * 100)
                            self._signal(f"Downloading video: {progress}%", progress=progress)

            # Verify file was downloaded
            if not Path(local_path).exists() or Path(local_path).stat().st_size == 0:
                raise Exception("Downloaded video file is empty or missing")

            self._signal(f"Video downloaded successfully: {local_path}")
            return local_path

        except Exception as e:
            # Clean up partial download
            if Path(local_path).exists():
                try:
                    Path(local_path).unlink()
                except:
                    pass
            raise Exception(f"Failed to download video: {str(e)}")

    def _set_proxy(self, type='set'):
        """Set up proxy configuration"""
        # This method should match the pattern used in other services
        # For now, return None - can be implemented based on existing proxy patterns
        return None

    def cancel_task(self, task_id: str = None) -> bool:
        """Cancel a video generation task"""
        if not task_id:
            task_id = self.task_id

        if not task_id:
            return False

        url = f"{self.api_base}/contents/generations/tasks/{task_id}"
        headers = self._get_headers()

        try:
            response = requests.delete(
                url,
                headers=headers,
                proxies=self.proxies,
                timeout=30
            )

            if response.status_code == 200:
                self._signal(f"Task {task_id} cancelled successfully")
                return True
            else:
                self._signal(f"Failed to cancel task {task_id}: {response.status_code}")
                return False

        except Exception as e:
            config.logger.exception(e, exc_info=True)
            self._signal(f"Error cancelling task: {str(e)}")
            return False
