import os
import re
import time
import requests
from pathlib import Path
from typing import Dict, Any

from videotrans.configure import config
from videotrans.configure.enhanced_config import enhanced_config
from videotrans.videogen._base import BaseVideoGen


class MinimaxVideoGen(BaseVideoGen):
    """Minimax Video Generation Service"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Minimax API configuration
        self.api_base = "https://api.minimax.io/v1"
        self.model = "MiniMax-Hailuo-02"  # Default model

        # Additional Minimax-specific parameters
        self.duration = kwargs.get('duration', 6)  # 6 or 10 seconds
        self.resolution = kwargs.get('resolution', '768P')  # 768P or 1080P
        self.prompt_optimizer = kwargs.get('prompt_optimizer', True)

        # Set up proxy if needed
        if not re.search('localhost', self.api_base) and not re.match(r'^https?://(\d+\.){3}\d+(:\d+)?', self.api_base):
            pro = self._set_proxy(type='set')
            if pro:
                self.proxies = pro

    def is_configured(self) -> bool:
        """Check if Minimax service is properly configured"""
        api_key = enhanced_config.get_api_key('minimax')
        if not api_key:
            return False
        return True

    def _get_headers(self) -> Dict[str, str]:
        """Get request headers with API key"""
        api_key = enhanced_config.get_api_key('minimax')
        return {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def _create_task(self) -> str:
        """Create video generation task and return task ID"""
        url = f"{self.api_base}/video_generation"
        headers = self._get_headers()

        # Prepare request payload
        payload = {
            "model": self.model,
            "duration": self.duration,
            "resolution": self.resolution,
            "prompt_optimizer": self.prompt_optimizer
        }

        # Add prompt if provided
        if self.prompt:
            payload["prompt"] = self.prompt.strip()

        # Add first frame image if provided (for image-to-video)
        if self.image_path:
            # For now, assume image_path is a URL or base64
            # In a full implementation, you might need to upload the image first
            payload["first_frame_image"] = self.image_path

        config.logger.info(f"Minimax video generation payload: {payload}")

        try:
            self._signal("Sending video generation request to Minimax...")

            response = requests.post(
                url,
                headers=headers,
                json=payload,
                proxies=self.proxies,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'base_resp' in error_data and 'status_msg' in error_data['base_resp']:
                        error_msg = error_data['base_resp']['status_msg']
                    elif 'error' in error_data:
                        error_msg = error_data['error']
                except:
                    error_msg = f"{error_msg}: {response.text}"
                raise Exception(error_msg)

            result = response.json()

            if 'task_id' not in result:
                raise Exception(f"Invalid response format: {result}")

            status_code = result.get('base_resp', {}).get('status_code', 0)
            if status_code != 0:
                raise Exception(f"API request failed with status code {result.get('base_resp', {}).get('status_msg', 'unknown error')}")

            task_id = result['task_id']
            self._signal(f"Minimax video generation task created: {task_id}")

            return task_id

        except requests.exceptions.RequestException as e:
            raise Exception(f"Network error creating task: {str(e)}")
        except Exception as e:
            config.logger.exception(e, exc_info=True)
            raise Exception(f"Error creating video generation task: {str(e)}")

    def _check_status(self, task_id: str) -> Dict[str, Any]:
        """Check task status and return status information"""
        url = f"{self.api_base}/query/video_generation"
        headers = self._get_headers()
        params = {"task_id": task_id}

        try:
            response = requests.get(
                url,
                headers=headers,
                params=params,
                proxies=self.proxies,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"Status check failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'base_resp' in error_data and 'status_msg' in error_data['base_resp']:
                        error_msg = error_data['base_resp']['status_msg']
                except:
                    error_msg = f"{error_msg}: {response.text}"
                return {"status": "failed", "error": error_msg, "task_id": task_id}

            result = response.json()

            status_info = {
                "status": result.get("status", "unknown").lower(),
                "task_id": task_id  # Always use the task_id we passed in
            }

            # Map Minimax status to our internal status
            minimax_status = result.get("status", "").lower()
            if minimax_status in ["preparing", "queueing"]:
                status_info["status"] = "queued"
            elif minimax_status == "processing":
                status_info["status"] = "running"
            elif minimax_status == "success":
                status_info["status"] = "succeeded"
                # Get file_id for download - store as video_url for base class compatibility
                file_id = result.get("file_id")
                if file_id:
                    status_info["video_url"] = file_id  # Store file_id as video_url for base class
                else:
                    status_info["status"] = "failed"
                    status_info["error"] = "No file_id in successful response"
            elif minimax_status == "fail":
                status_info["status"] = "failed"
                status_info["error"] = result.get("error", "Video generation failed")

            return status_info

        except requests.exceptions.RequestException as e:
            return {"status": "failed", "error": f"Network error checking status: {str(e)}", "task_id": task_id}
        except Exception as e:
            config.logger.exception(e, exc_info=True)
            return {"status": "failed", "error": f"Error checking status: {str(e)}", "task_id": task_id}

    def _download_video(self, video_url: str) -> str:
        """Download generated video using file_id (passed as video_url) and return local path"""
        file_id = video_url  # For Minimax, video_url is actually the file_id
        if not file_id:
            raise Exception("No file_id provided")

        # First, get the download URL using the file_id
        url = f"{self.api_base}/files/retrieve"
        headers = self._get_headers()
        params = {"file_id": file_id}

        try:
            self._signal("Getting video download URL...")

            response = requests.get(
                url,
                headers=headers,
                params=params,
                proxies=self.proxies,
                timeout=30
            )
            response.raise_for_status()

            result = response.json()

            # Extract download URL
            file_info = result.get('file', {})
            download_url = file_info.get('download_url') or file_info.get('backup_download_url')

            if not download_url:
                raise Exception("No download URL found in response")

            # Generate local filename
            timestamp = int(time.time())
            filename = f"minimax_video_{self.uuid}_{timestamp}.mp4"
            local_path = os.path.join(self.cache_folder, filename)

            self._signal("Downloading generated video...")

            # Download the video file
            video_response = requests.get(
                download_url,
                proxies=self.proxies,
                timeout=300,  # 5 minutes for video download
                stream=True
            )
            video_response.raise_for_status()

            # Download with progress
            total_size = int(video_response.headers.get('content-length', 0))
            downloaded = 0

            with open(local_path, 'wb') as f:
                for chunk in video_response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        if total_size > 0:
                            progress = int((downloaded / total_size) * 100)
                            self._signal(f"Downloading video: {progress}%", progress=progress)

            # Verify file was downloaded
            if not Path(local_path).exists() or Path(local_path).stat().st_size == 0:
                raise Exception("Downloaded video file is empty or missing")

            self._signal(f"Video downloaded successfully: {local_path}")
            return local_path

        except Exception as e:
            # Clean up partial download
            if 'local_path' in locals() and Path(local_path).exists():
                try:
                    Path(local_path).unlink()
                except:
                    pass
            raise Exception(f"Failed to download video: {str(e)}")

    def _set_proxy(self, type='set'):
        """Set up proxy configuration"""
        # This method should match the pattern used in other services
        # For now, return None - can be implemented based on existing proxy patterns
        return None
