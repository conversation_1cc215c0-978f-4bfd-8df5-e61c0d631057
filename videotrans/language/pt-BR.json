{"translate_language": {"llmduanju": "If you choose the large model segmentation, you must set the available model information in the translation settings--OpenAI API", "Succeed": "Succeed", "haspaused": "Paused", "shutdownerror": "computer shutdown error", "onlycnanden": "ChatTTS suporta apenas Chinês e Inglês", "peiyindayu31": "O número de erros de dublagem é maior que 1/3, por favor, verifique-os", "chaochu255": "O caminho e o nome do vídeo original são muito longos. Abrevie o nome do vídeo e mova-o para um caminho mais curto para evitar erros subsequentes.", "teshufuhao": "Não inclua símbolos especiais como + & ? : | etc. no caminho ou nome do vídeo original para evitar erros subsequentes.", "notjson": "A resposta retornada não são dados json válidos", "fanyicuowu2": "O número de erros de tradução é mais da metade, por favor verifique", "azureinfo": "Você configurou os valores da chave e da região do recurso de fala?", "yuchulichucuo": "Erro na etapa de pré-processamento", "shibiechucuo": "Erro no reconhecimento de fala", "fanyichucuo": "Erro na tradução da legenda", "peiyinchucuo": "Erro na dublagem", "hebingchucuo": "Erro ao combinar", "freechatgpt_tips": "ChatGPT foi patrocinado por apiskey.top para uso gratuito.", "yidaorujigewenjian": "Arquivos de legendas importados:", "dakaizimubaocunmulu": "<PERSON><PERSON>r diretório onde a tradução foi salva", "quanbuwanbi": "<PERSON><PERSON> traduz<PERSON>.", "srtgeshierror": "Erro ao formatar a legenda, verifique se o arquivo de legenda está no formato srt", "endopendir": "Clique para abrir o diretório de salvamento quando terminar", "xinshoumoshitips": "Selecione o vídeo que deseja traduzir e defina o idioma original do vídeo e o idioma para o qual deseja traduzir", "enmodelerror": "Modelos com o sufixo .en só podem ser usados se o idioma original for inglês", "openaimodelerror": "Modelos que começam com distil-whisper não podem ser selecionados ao usar modelos openai.", "qiegeshujuhaoshi": "Se o vídeo for muito grande, esta etapa será demorada, portanto, seja paciente.", "youtubehasdown": "O download come<PERSON>ou, verifique o progresso na interface pop-up de download. AVISO: A mensagem não precisa de atenção, apenas ignore!", "starting...": "O processamento começará e o progresso será mostrado em breve", "dubbing speed up": "<PERSON><PERSON><PERSON> dublagem", "video speed down": "Desacelerar vídeo", "auto_ajust": "Extensão de Voz", "audio_concat": "Connecting audio clips", "auto_ajust_tooltips": "Quando este item estiver marcado, se a dublagem for mais longa que a duração original, o trecho mudo imediatamente seguinte será utilizado primeiro para trás. Se ainda não houver tempo suficiente para colocar a dublagem, então o trecho mudo será utilizado para frente. Ajuste conforme necessário se a dublagem for automaticamente acelerada ou o vídeo for automaticamente desacelerado.", "tts tooltip": "Escolha o canal de dublagem, clone-voice/GPT-SoVITS deve primeiro preencher as informações da API no menu superior esquerdo - Configurações.", "trans tooltip": "Selecione o canal de tradução a ser usado, as interfaces oficiais do Google/Gemini/chatGPT não podem ser conectadas localmente, você deve preencher o endereço do proxy de rede à direita.", "fenlinoviceerror": "Falha ao separar novoice.mp4.", "No subtitles file": "Nenhum arquivo de legenda", "Subtitles error": "Erro ao formatar informações de legenda", "get video_info error": "Erro ao obter informações do vídeo, verifique se o vídeo pode ser reproduzido.", "nogptsovitsurl": "Você deve preencher o endereço da API do GPT-SoVITS", "nogptsovitslanguage": "GPT-SoVITS suporta apenas os idiomas <PERSON>, Inglês e Japonês", "miandailigoogle": "Usar o Google Tradutor quando não houver proxy", "ttsapi_nourl": "A URL da TTS-API personalizada deve ser preenchida antes de ser usada", "import src error": "Erro ao importar legendas, verifique se existe conteúdo no formato de legenda SRT no arquivo", "openaimodelnot": "Você escolheu o modelo openai {name}, mas o modelo {name} não existe, faça o download e coloque o arquivo pt na pasta models no diretório do software, clique na barra de menu - Ajuda e Suporte - Baixar Modelo", "recogn result is empty": "Nenhuma legenda foi reconhecida, verifique se contém voz humana e se a categoria de voz corresponde ao idioma original {lang} que você escolheu. Se ambos estiverem normais, selecione a opção 'Preservar som de fundo' no modo de função padrão e tente novamente", "pianduan": " Segmento ", "Download Models": "Baixar Modelos", "Start Separate": "Iniciar se<PERSON>", "Start Separate...": "Separando/clique em parar", "Separate End/Restart": "Separação concluída/reiniciando", "zimusrterror": "A área de legendas já possui legendas que não atendem aos requisitos do formato SRT. Exclua todo o texto da área de legendas ou importe o formato SRT correto novamente", "Export srt": "Exportar SRT", "When subtitles exist, the subtitle content can be saved to a local SRT file": "Quando existem legendas, o conteúdo da legenda pode ser salvo em um arquivo SRT local", "You must fill in the YouTube video playback page address": "Você deve preencher o endereço da página de reprodução do vídeo do YouTube", "Error merging background and dubbing": "Erro ao mesclar fundo e dublagem", "only10": "Este software suporta apenas sistemas Win10 e superiores na plataforma Windows", "sp.exeerror": "sp.exe deve estar localizado no diretório original após a descompactação (ou seja, no mesmo diretório que videotrans|models|_internal), não copie ou mova para qualquer outro local sem permissão", "meitiaozimugeshi": "Cada formato de legenda é o seguinte:\nNúmero da linha\nHora de início horas:minutos:segundos,milissegundos --> Hora de término horas:minutos:segundos,milissegundos\n<PERSON>teúdo da legenda", "Set up separate dubbing roles for each subtitle to be used": "Configurar papéis de dublagem separadOs para cada legenda a ser usada", "daoruzimutips": "Arquivos de legendas SRT existentes podem ser importados localmente, pulando as etapas de reconhecimento e tradução e usando as legendas importadas diretamente", "Click to start the next step immediately": "Clique para iniciar a próxima etapa imediatamente", "Click to pause and modify subtitles for more accurate processing": "Clique para pausar e modificar as legendas para um processamento mais preciso", "zimubianjitishi": "<PERSON><PERSON><PERSON> o período de pausa, as legendas podem ser editadas", "test google": "Testar conexão com o Google..", "Select Out Dir": "Selecionar Diretório de Saída", "downing...": "Baixando...", "start download": "Iniciar Download", "Down done succeed": "Download concluído com sucesso", "videodown..": "O vídeo está sendo desacelerado", "Dubbing": "Dublando..", "Separating background music": "Separando música de fundo", "Unable to connect to the API": "Não é possível conectar à API", "bixutianxiecloneapi": "Você deve preencher o endereço da API no menu Configurações - cloneVoice", "Save": "<PERSON><PERSON>", "Open Documents": "Abrir Documentos para Iniciantes", "Preserve the original sound in the video": "Preservar o som original no vídeo", "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices": "A voz clone não pode ser usada no modo de dublagem de legendas, pois não há vozes replicáveis", "lanjie": "Restrição ativa", "The ott project at": "O projeto OTT está em: github.com/jianchang512/ott", "No select videos": "Nenhum vídeo <PERSON>", "You must deploy and start the clone-voice service": "Você deve implantar e iniciar o serviço clone-voice (github.com/jianchang512/clone-voice), e preencher o endereço no menu Configurações - cloneVoice", "cannot connection to clone-voice service": "Não foi possível conectar ao serviço clone-voice. Você deve implantar e iniciar o serviço clone-voice (github.com/jianchang512/clone-voice), e preencher o endereço no menu Configurações - cloneVoice", "test clone voice": "Testando a conexão com a API clone-voice...", "The project at": "Clone voice em: github.com/jianchang512/clone-voice", "qianyiwenjian": "O caminho ou nome do vídeo contém espaços não ASCII. Para evitar erros, ele foi migrado para ", "Separating vocals and background music, which may take a longer time": "Separando voz e música de fundo, isso pode levar mais tempo", "mansuchucuo": "Erro ao diminuir a velocidade do vídeo automaticamente, tente desmarcar a opção 'Video auto down'", "zimuwenjianbuzhengque": "Erro no arquivo de legendas, o tamanho é 0b", "huituicpu": "Erro de execução na GPU, voltando para a execução na CPU", "zimuhangshu": "<PERSON><PERSON> da legenda ", "kaishihebing": "Iniciar a mesclagem e saída dos arquivos resultantes", "kaishishibie": "Iniciar o reconhecimento de fala", "kaishitiquyinpin": "Iniciar a extração de áudio", "endfenliyinpin": "Separate end wait recognized", "kaishiyuchuli": "Iniciar o pré-processamento do vídeo para o formato padrão", "fengeyinpinshuju": "Dividir os dados antes do reconhecimento de fala", "yuyinshibiejindu": "Progresso do reconhecimento de fala", "yuyinshibiewancheng": "Reconhecimento de fala concluído", "shipinmoweiyanchang": "Estender o final do vídeo", "shipinjiangsu": "Estender o vídeo", "bukebaoliubeijing": "É necessário ter um dublador e um vídeo de entrada para preservar a música de fundo", "xinshipinchangdu": "Novo comprimento do vídeo após a desaceleração", "peiyin-yingzimu": "Sinte<PERSON><PERSON><PERSON> dub<PERSON> + legenda fixa", "peiyin-ruanzimu": "Sinteti<PERSON><PERSON> dublage<PERSON> + legenda embutida", "onlypeiyin": "Incor<PERSON><PERSON><PERSON>, sem legendas", "onlyyingzimu": "Incorporando legendas fixas, sem dublagem", "onlyruanzimu": "Incorporando legendas suaves, sem dublagem", "tuodonghuoshuru": "Insira o texto ou arraste o arquivo SRT da legenda aqui", "ffmpegerror": "Verifique se a configuração CUDA está correta ou se o vídeo é um arquivo mp4 codificado em H264", "sjselectmp4": "Clique duas vezes para selecionar o vídeo ou arraste o vídeo aqui", "default": "<PERSON><PERSON><PERSON>", "zhishaoxuanzeyihang": "<PERSON>elo menos uma linha deve ser selecionada", "continue_action": "Ir para a próxima etapa", "xuanzejuese": "É preciso selecionar um papel de dublagem. Primeiro, selecione o idioma da legenda e depois o papel de dublagem", "tencent_key": "É preciso inserir o SecretId e SecretKey da Tencent", "qingqueren": "Por favor, confirme", "only_srt": "Nenhum idioma de destino selecionado, apenas o arquivo srt será criado. Clique em Sim para continuar ou Cancelar para cancelar", "mustberole": "É preciso selecionar um papel para ouvir", "setdeepl_authkey": "É preciso configurar o token DeepL", "setdeeplx_address": "É preciso configurar o endereço e a porta do DeepLX", "setott_address": "É preciso configurar o endereço e a porta do OTT", "subtitle_tips": "Edite a legenda aqui ou arraste o arquivo srt para cá", "waitclear": "<PERSON><PERSON><PERSON>o", "whisper_type_all": "Completo", "whisper_type_split": "Pré-dividido", "whisper_type_avg": "Divisão igual", "fenge_tips": "Completo: O modelo quebra as frases automaticamente em todo o áudio.\nPré-dividido: adequado para vídeos muito grandes, divididos em clipes de 1 minuto para reconhecer e quebrar as frases uma por uma.\nDivisão igual: corte igualmente de acordo com um número fixo de segundos, cada legenda tem o mesmo comprimento.", "processingstatusbar": "Processando vídeo: [{var1}] total, [{var2}] aguardando", "yinsekelong": "A clonagem de timbre usará github.com/jianchang512/clone-voice. Esses recursos serão usados como a voz para dublar personagens, permitindo dublagens personalizadas com qualquer timbre desejado.", "yinsekaifazhong": "A clonagem de timbre está em desenvolvimento.", "installffmpeg": "FFmpeg não encontrado. Baixe em ffmpeg.org e coloque os arquivos ffmpeg e ffprobe na raiz deste software", "hechengchucuo": "Erro ao compor o vídeo, arquivo perdido:", "queding": "Confirmar", "wait_edit_subtitle": "Aguardando edição da legenda", "autocomposing": "Após a contagem regressiva de segundos, o vídeo será sintetizado automaticamente. Clique em Pausar para interromper a contagem regressiva", "deepl_nosupport": "Não há suporte para tradução para o idioma escolhido", "deepl_authkey": "Você precisa de uma chave de autenticação DeepL", "confirmstop": "Parar esta tarefa?", "prcoessingstatusbar": "Processando vídeo: [{var1}], com [{var2}] aguardando para serem processados", "createdirerror": "Erro ao criar dire<PERSON>", "waitforend": "Compondo vídeo", "waitsubtitle": "Aguarde a edição da legenda (clique para continuar)", "baikeymust": "Insira sua chave Baidu", "chatgptkeymust": "Insira sua chave ChatGPT", "nosubtitle": "<PERSON><PERSON>", "embedsubtitle": "Incorporar Legenda", "softsubtitle": "<PERSON><PERSON>", "embedsubtitle2": "Incorporar Legenda (dupla)", "softsubtitle2": "<PERSON><PERSON> (dupla)", "modellost": "Houve um erro no download do modelo ou o download está incompleto. Faça o download novamente e os armazene no diretório 'models'.", "modelpathis": "Caminho de armazenamento do modelo:", "downloadmodel": "O modelo {name} não existe. Clique na barra de menu - Ajuda e Suporte - Baixar Modelo", "waitrole": "Obtendo lista de papéis de voz, aguarde", "selectsavedir": "Selecione um diretório para a saída", "selectmp4": "Selecione um vídeo mp4", "subtitleandvoice_role": "Sem vídeo e com legenda na área de edição, serão criados arquivos de áudio wav de dublagem. Confirmar para continuar?", "proxyerrortitle": "Erro de Proxy", "shoundselecttargetlanguage": "É necessário selecionar um idioma de destino", "proxyerrorbody": "Falha ao acessar os serviços do Google. Configure o proxy corretamente.", "softname": "pyVideoTrans Tradução e Dublagem", "anerror": "Ocorreu um erro", "selectvideodir": "Você deve selecionar o vídeo a ser traduzido", "sourenotequaltarget": "O idioma de origem e o idioma de destino não podem ser iguais", "running": "Executando...", "ing": "Executando...", "exit": "<PERSON><PERSON>", "end": "Finalizado (clique para reiniciar)", "stop": "Parar (clique para reiniciar)", "error": "<PERSON><PERSON> (clique para reiniciar)", "daoruzimu": "Importar legendas", "shitingpeiyin": "<PERSON><PERSON><PERSON> de teste", "xianqidongrenwu": "Inicie a tarefa primeiro, você poderá ouvir após a conclusão da tradução da legenda. A velocidade de dublagem e a aceleração automática entram em vigor em tempo real", "juanzhu": "Considere fazer uma doação para que o software continue sendo atualizado e mantido!", "nocuda": "Seu dispositivo não atende aos requisitos de aceleração CUDA. Confirme se é uma placa de vídeo NVIDIA e se o ambiente CUDA está configurado. Clique em Menu - Ajuda e Suporte - Ajuda CUDA.", "nocudnn": "Seu dispositivo não possui o cuDNN instalado e configurado. Consulte https://juejin.cn/post/7318704408727519270#heading-1 para instalação e reinicie o software, ou use o modelo OpenAI.", "cudatips": "Habilite se você tiver uma placa de vídeo NVIDIA e um ambiente CUDA configurado. Isso aumentará muito a velocidade de execução.", "noselectrole": "<PERSON>enhum papel selecionado, não é possível testar a escuta.", "chongtingzhong": "Escutando novamente", "shitingzhong": "Escutando/Clique para escutar novamente", "bukeshiting": "Sem conteúdo de legenda, não é possível testar a escuta.", "tiquzimu": "Defina o idioma original como o idioma falado no vídeo e o idioma de destino como o idioma para o qual você deseja traduzir.", "kaishitiquhefanyi": "Iniciar extração e tradução", "tiquzimuno": "Defina o idioma original como o idioma falado no vídeo.", "kaishitiquzimu": "Iniciar extração de legendas", "endtiquzimu": "End of voice recognition waiting for the next step", "endtrans": "End of translation wait for next step", "duiqicaozuo": "Begin processing audio screen subtitle alignment", "zimu_video": "Escolha o vídeo a ser mesclado, arraste e solte o arquivo de legendas srt na área de legendas à direita.", "zimu_peiyin": "Defina o idioma de destino como o idioma usado para as legendas e selecione o papel de dublagem.", "kaishipeiyin": "In<PERSON><PERSON>", "anzhuangvlc": "Talvez seja necessário instalar o decodificador VLC primeiro.", "jixuzhong": "Continuando a execução", "nextstep": "Passar para a próxima etapa", "tianxieazure": "É preciso preencher com a chave Azure.", "bencijieshu": "Esta tarefa está concluída", "kaishichuli": "Iniciar process<PERSON>o", "yunxingbukerole": "Em execução, não é possível alterar para sem papel de dublagem", "bixutianxie": "É preciso ser preenchido", "peiyinmoshisrt": "É preciso escolher um papel de dublagem, o idioma de destino no modo de dublagem e arrastar e soltar o arquivo de legenda srt local para a área de legendas à direita.", "hebingmoshisrt": "No modo de mesclagem, você deve selecionar um vídeo, o tipo de incorporação de legenda e arrastar e soltar o arquivo de legendas srt para a área de legendas à direita.", "fanyimoshi1": "É preciso escolher o idioma de destino para a tradução.", "bukedoubucunzai": "Vídeos e legendas não podem estar ausentes ao mesmo tempo!", "wufapeiyin": "Nenhum idioma de destino selecionado, não é possível dublar. Selecione o idioma de destino ou cancele o papel de dublagem.", "xuanzeyinpinwenjian": "Selecione arquivos de áudio e vídeo", "vlctips": "Arraste o vídeo aqui ou clique duas vezes para selecionar o vídeo.", "vlctips2": "O arquivo não existe", "xuanzeyinshipin": "Clique para selecionar ou arraste e solte os arquivos de áudio e vídeo aqui.", "tuodongdaoci": "Arraste o arquivo a ser convertido aqui e solte.", "tuodongfanyi": "Importar arquivo de legendas srt", "zhixingwc": "Execução concluída", "zhixinger": "Erro de execução", "starttrans": "Iniciar tradu<PERSON>", "yinpinhezimu": "<PERSON>elo menos um áudio ou legenda deve ser selecionado.", "bixuyinshipin": "É preciso selecionar um arquivo de áudio e vídeo válido.", "chakanerror": "Falha no pré-processamento antes do reconhecimento. Confirme se há dados de áudio no vídeo.", "srtisempty": "O conteúdo da legenda está vazio.", "savesrtto": "Escolha onde salvar o arquivo de legenda...", "neirongweikong": "O conteúdo não pode estar vazio.", "yuyanjuesebixuan": "O idioma e o papel devem ser selecionados.", "nojueselist": "Não foi possível obter a lista de papéis.", "buzhichijuese": "Este papel de voz não é suportado.", "nowenjian": "Não existem arquivos válidos.", "yinpinbuke": "O áudio não pode ser convertido para", "quanbuend": "<PERSON><PERSON> as conversões foram concluídas.", "wenbenbukeweikong": "O texto a ser traduzido não pode estar vazio.", "buzhichifanyi": "Não há suporte para tradução para o idioma de destino.", "ffmpegno": "ffmpeg não encontrado. O software não está disponível. Faça o download em ffmpeg.org e adicione às variáveis de ambiente do sistema.", "newversion": "Há uma nova versão para download.", "tingzhile": "Parado.", "geshihuazimuchucuo": "Erro ao formatar o arquivo de legenda.", "moweiyanchangshibai": "Falha ao adicionar quadros de vídeo de extensão no final. O estado original será mantido sem estender o vídeo.", "xiugaiyuanyuyan": "Aguarde para modificar as legendas em idioma original / continuar.", "jimiaohoufanyi": "Tradução automática em segundos. Você pode clicar em Pausar e editar as legendas para que a tradução seja mais precisa.", "xiugaipeiyinzimu": "Aguarde para modificar as legendas dubladas / clique em continuar.", "zidonghebingmiaohou": "Mesclagem automática em segundos. Você pode clicar em Pausar para modificar as legendas para que a dublagem seja mais precisa.", "jieshutips": "Processamento do vídeo concluído: Os materiais relevantes podem ser encontrados na pasta de destino, incluindo arquivos de legenda, arquivos de dublagem, etc.", "mubiao": "<PERSON><PERSON>r pasta de saída", "endandopen": "<PERSON><PERSON><PERSON><PERSON><PERSON>, clique para abrir", "waitforstart": "Aguardando <PERSON>", "xianxuanjuese": "Selecione o tipo de TTS e o papel primeiro.", "shezhijueseline": "<PERSON>en<PERSON> o número de linhas usando a dublagem do personagem, por exemplo, 2,3,7,8", "youzimuyouset": "Vários papéis só podem ser definidos por linha após a exibição completa das legendas na área de legendas.", "shezhijuese": "Definir Papel"}, "ui_lang": {"action_xinshoujandan": "<PERSON>do Iniciante (Simplificado)", "action_xinshoujandan_tools": "Simples de usar sem configuração, adequado para vídeos curtos, menos preciso. Para mais controle, use o modo de função padrão.", "onlyvideo": "Salvar apenas vídeo", "onlyvideo_tips": "Se este item estiver marcado, legendas, áudio e outros materiais não serão mantidos, apenas o arquivo de vídeo final será salvo.", "model_type_tips": "O modelo master é mais rápido e eficiente em recursos, mas requer a instalação e configuração do cudnn e cublas além do cuda.\nO modelo openai é mais lento e consome mais recursos, mas requer apenas a instalação do cuda.", "faster model": "faster-whisper", "openai model": "openai-whisper", "addbackbtn": "Adicionar música de fundo", "back_audio_place": "O caminho completo da música de fundo, apagando-o não o adiciona", "Download Models": "Baixar Modelos", "Download cuBLASxx.dll": "Baixar cuBLASxx.dll", "Separate vocal voice": "Separar Voz de Fundo", "SP-video Translate Dubbing": "VideoTrans - Tradução e Dublagem de Vídeo", "Multiple MP4 videos can be selected and automatically queued for processing": "Vários vídeos MP4 podem ser selecionados e automaticamente colocados em fila para processamento", "Select video..": "Selecionar vídeo..", "Select where to save the processed output resources": "Selecionar onde salvar os recursos de saída processados", "Save to..": "<PERSON><PERSON> em..", "Open target dir": "Abrir pasta de destino", "Open": "Abrir", "Translate channel": "Canal de tradução", "Proxy": "Proxy", "proxy address": "endereço do proxy", "shuoming01": "Clique para ouvir a pronúncia do personagem de dublagem atual. A geração da dublagem pode levar alguns segundos, por favor, seja paciente e aguarde.", "Trial dubbing": "<PERSON>e de dublagem", "Source lang": "Idioma de origem", "The language used for the original video pronunciation": "O idioma usado para a pronúncia original do vídeo", "Target lang": "Idioma de destino", "What language do you want to translate into": "Para qual idioma você quer traduzir?", "Dubbing role": "<PERSON><PERSON>", "No is not dubbing": "Não está dublando", "From base to large v3, the effect is getting better and better, but the speed is also getting slower and slower": "Do base ao large v3, o efeito fica cada vez melhor, mas a velocidade também fica cada vez mais lenta.", "Overall recognition is suitable for videos with or without background music and noticeable silence": "O reconhecimento geral é adequado para vídeos com ou sem música de fundo e com silêncio perceptível.", "Embed subtitles": "Legendas incorporadas", "shuoming02": "As legendas incorporadas sempre exibem legendas, independentemente de onde são reproduzidas, e não podem ser ocultadas. Se o player for compatível, as legendas suaves podem ser controladas para serem exibidas ou ocultadas no player. Se você deseja exibir legendas ao reproduzir em uma página da web, selecione legendas incorporadas.", "Silent duration": "Duração do silêncio", "default 500ms": "padr<PERSON> 500ms", "Mute duration for segmented speech, in milliseconds": "Duração do silêncio para fala segmentada, em milissegundos", "Dubbing speed": "Velocidade de dublagem", "Overall acceleration or deceleration of voice over playback": "Aceleração ou desaceleração geral da reprodução da dublagem", "Positive numbers accelerate, negative numbers decelerate, -90 to+90": "Números positivos aceleram, números negativos desaceleram, de -90 a +90", "shuoming03": "Após a tradução de diferentes idiomas sob a pronúncia de diferentes comprimentos, inevitavelmente haverá problemas de alinhamento. Através da velocidade geral da fala da dublagem, da aceleração automática da dublagem, a extensão da voz antes e depois pode ser ligeiramente aliviada. Para mais métodos e princípios, consulte os tutoriais relevantes no canto inferior esquerdo.", "Voice acceleration?": "Aceleração de voz?", "shuoming04": "A duração da pronúncia varia em diferentes idiomas após a tradução. Por exemplo, se uma frase em chinês dura 3 segundos, pode levar 5 segundos para traduzi-la para o inglês, resultando em inconsistência entre a duração e o vídeo.\nHá duas soluções:\n1. Forçar a aceleração da reprodução da dublagem para encurtar a duração da dublagem e alinhar com o vídeo.\n2. Forçar o vídeo a ser reproduzido lentamente para estender a duração e alinhar com a dublagem. Escolha apenas uma das duas opções.", "Video slow": "<PERSON><PERSON><PERSON><PERSON> lento", "shuoming05": "É necessário garantir que haja uma placa de vídeo NVIDIA e que o ambiente CUDA esteja configurado corretamente, caso contrário, não escolha.", "Enable CUDA?": "Habilitar CUDA?", "Preserve background music": "Preservar música de fundo", "If retained, the required time may be longer, please be patient and wait": "Se mantido, o tempo necessário pode ser maior, por favor, seja paciente e aguarde.", "Start": "Iniciar", "Pause": "Pausar", "Import srt": "Importar srt", "Train voice": "<PERSON><PERSON><PERSON><PERSON> voz", "Set role by line": "De<PERSON>ir papel por linha", "&Setting": "&TransText Set", "&TTSsetting": "TTS Set(&E)", "&RECOGNsetting": "Speech to Text(&R)", "&Tools": "&Tools/ADVSet", "&Help": "&<PERSON><PERSON><PERSON>", "toolBar": "Barra de ferramentas", "Video Toolbox": "Caixa de Ferramentas de Vídeo", "Go VLC Website": "Ir para o site do VLC", "FFmpeg": "FFmpeg", "Go FFmpeg website": "Ir para o site do FFmpeg", "Post issue": "GitHub Issues", "Clone Voice": "<PERSON><PERSON><PERSON>", "Documents": "Documentos", "Donating developers": "Doar para os desenvolvedores", "Standard Function Mode": "Modo de Função Padrão", "Display all options for video translation and dubbing": "<PERSON><PERSON><PERSON> as opções para tradução e dublagem de vídeo", "Export Srt From Videos": "Exportar Srt de Vídeos", "Extracting SRT subtitles in the original language from local videos": "Extraindo legendas SRT no idioma original de vídeos locais", "Merging Subtitle  Video": "Mesclando Legenda e Vídeo", "Embed locally existing SRT subtitles into the video": "Incorporar legendas SRT existentes localmente no vídeo", "Subtitle Create Dubbing": "<PERSON><PERSON><PERSON>", "Local existing SRT subtitle generation dubbing WAV files": "Geração de arquivos WAV de dublagem a partir de legendas SRT existentes localmente", "Speech Recognition Text": "Reconhecimento de Fala em Texto", "Recognize the sound in audio or video and output SRT text": "Reconhecer o som em áudio ou vídeo e gerar texto SRT", "From  Text  Into  Speech": "De Texto para Fala", "Generate audio WAV from text or SRT subtitle files": "Gerar á<PERSON>o WAV a partir de arquivos de texto ou legendas SRT", "Extract Srt And Translate": "Extrair Srt e Traduzir", "Extract SRT subtitles from local videos in the original language and translate them into SRT subtitle files in the target language": "Extrair legendas SRT de vídeos locais no idioma original e traduzi-las para arquivos de legendas SRT no idioma de destino", "Separate Video to audio": "Separar Vídeo em áudio", "Separate audio and silent videos from videos": "Separar áudio e vídeos silenciosos de vídeos", "Video Subtitles Merging": "Video Audio Srt Merging", "Merge audio, video, and subtitles into one file": "Mesclar áudi<PERSON>, vídeo e legendas em um arquivo", "Files Format Conversion": "Conversão de Formatos de Arquivos", "Convert various formats to each other": "Converter vários formatos entre si", "Mixing 2 Audio Streams": "Mixar 2 Fluxos de Áudio", "Mix two audio files into one audio file": "Mixar dois arquivos de áudio em um único arquivo de áudio", "Text  Or Srt  Translation": "Tradução em Lote de Legendas", "Translate text or subtitles": "Traduzir texto ou legendas", "Download from Youtube": "Baixar do Youtube", "Whisper model": "<PERSON><PERSON>"}, "toolbox_lang": {"import audio or video": "Importar á<PERSON> ou vídeo", "Video Toolbox": "Ferramentas de Vídeo", "Start": "Iniciar", "No voice video": "Vídeo sem voz", "Open dir": "Abrir pasta", "Audio Wav": "<PERSON><PERSON><PERSON>", "Video audio separation": "Separação de áudio e vídeo", "Video file": "Arquivo de vídeo", "Select video": "Selecionar vídeo", "Audio file": "Arquivo de áudio", "Select audio": "Selecionar áudio", "Subtitle srt": "Legenda srt", "Select srt file": "Selecionar arquivo srt", "Open output dir": "<PERSON><PERSON>r pasta de saída", "Video subtitle merging": "Mesclagem de vídeo e legenda", "Source lang": "Idioma de origem", "Whisper model": "<PERSON><PERSON>", "Save to srt..": "<PERSON>var em srt..", "Voice recognition": "Reconhecimento de voz", "Subtitle lang": "Idioma da legenda", "Select role": "Selecionar papel", "Speed change": "Alteração de velocidade", "Negative deceleration, positive acceleration": "Negativo desacelera, positivo acelera", "If so, the line number and time value will skip reading aloud": "Se marcado, o número da linha e o valor do tempo serão ignorados na leitura em voz alta", "Is srt?": "É srt?", "Automatic acceleration?": "Aceleração automática?", "Output audio name": "Nome do áudio de saída", "Set the name of the generated audio file here. If not filled in, use the time and date command": "Defina o nome do arquivo de áudio gerado aqui. Se não preenchido, usará a data e hora.", "Text to speech": "Texto para fala", "Convert mp4->": "Converter mp4->", "Convert avi->": "Converter avi->", "Convert mov->": "Converter mov->", "Convert wav->": "Converter wav->", "Convert mp3->": "Converter mp3->", "Convert aac->": "Converter aac->", "Convert m4a->": "Converter m4a->", "Conver flac->": "Converter flac->", "The conversion result is displayed here": "O resultado da conversão é exibido aqui", "Audio and video format conversion": "Conversão de formato de áudio e vídeo", "Audio file 1": "Arquivo de áudio 1", "Select the first audio file": "Selecione o primeiro arquivo de áudio", "Audio file 2": "Arquivo de áudio 2", "Select the second audio file": "Selecione o segundo arquivo de áudio", "You can customize the output file name here. If not filled in, use a date name": "Você pode personalizar o nome do arquivo de saída aqui. Se não preenchido, será usado um nome com data.", "Mixing two audio streams": "Mixando dois fluxos de áudio", "Translation channels": "Canais de tradução", "Target lang": "Idioma de destino", "Proxy": "Proxy", "Failed to access Google services. Please set up the proxy correctly": "Falha ao acessar os serviços do Google. Configure o proxy corretamente.", "Import text to be translated from a file..": "Importar texto a ser traduzido de um arquivo..", "shuoming1": "Apenas arquivos de legenda no formato SRT podem ser traduzidos. Não importe e traduza arquivos que não estejam nesse formato, caso contr<PERSON><PERSON>, um erro será reportado.", "export..": "exportar..", "Start>": "Iniciar>", "The translation result is displayed here": "O resultado da tradução é exibido aqui", "Text subtitle translation": "Tradução de legenda de texto"}, "language_code_list": {"zh-cn": "<PERSON><PERSON><PERSON>li<PERSON>", "zh-tw": "<PERSON>ês Tradici<PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "de": "Alemão", "ja": "<PERSON><PERSON><PERSON><PERSON>", "ko": "<PERSON><PERSON>", "ru": "<PERSON>", "es": "Espanhol", "th": "Tailandês", "it": "Italiano", "pt": "Português", "vi": "Vietnamita", "ar": "<PERSON><PERSON><PERSON>", "tr": "<PERSON><PERSON><PERSON>", "hi": "Hindi", "hu": "<PERSON><PERSON><PERSON><PERSON>", "uk": "Ucraniano", "id": "Indonésio", "ms": "Malaio", "kk": "Cazaque", "cs": "Tcheco", "pl": "<PERSON><PERSON><PERSON><PERSON>", "nl": "Dutch", "sv": "Swedish", "he": "Hebrew", "bn": "Bengali", "fil": "Filipino", "fi": "Finnish", "fa": "Persian", "auto": "Detection"}}