{"last_opendir": "/Users/<USER>/Videos/pyvideotrans", "cuda": false, "line_roles": {}, "only_video": false, "is_separate": false, "remove_noise": false, "target_dir": "", "source_language": "en", "target_language": "zh-cn", "subtitle_language": "chi", "translate_type": 0, "subtitle_type": 0, "listen_text_zh-cn": "你好啊，我亲爱的朋友，希望你的每一天都是美好愉快的！", "listen_text_zh-tw": "你好啊，我親愛的朋友，希望你的每一天都是美好愉快的！", "listen_text_en": "Hello, my dear friend. I hope your every day is beautiful and enjoyable!", "listen_text_fr": "Bonjour mon cher ami. J'espère que votre quotidien est beau et agréable !", "listen_text_de": "Hallo mein lieber Freund. Ich hoffe, dass Ihr Tag schön und angenehm ist!", "listen_text_ja": "こんにちは私の親愛なる友人。 あなたの毎日が美しく楽しいものでありますように！", "listen_text_ko": "안녕, 내 사랑하는 친구. 당신의 매일이 아름답고 즐겁기를 바랍니다!", "listen_text_ru": "Привет, мой дорогой друг. Ж<PERSON><PERSON><PERSON><PERSON>, чтобы каждый твой день был прекрасен и приятен!", "listen_text_es": "Hola mi querido amigo. ¡Espero que cada día sea hermoso y agradable!", "listen_text_th": "สวัสดีเพื่อนรัก. ฉันหวังว่าทุกวันของคุณจะสวยงามและสนุกสนาน!", "listen_text_it": "Ciao caro amico mio. Spero che ogni tuo giorno sia bello e divertente!", "listen_text_pt": "Olá meu querido amigo. Espero que todos os seus dias sejam lindos e agradáveis!", "listen_text_vi": "Xin chào người bạn thân yêu của tôi. Tôi hy vọng mỗi ngày của bạn đều đẹp và thú vị!", "listen_text_ar": "مرحبا صديقي العزيز. أتمنى أن يكون كل يوم جميلاً وممتعًا!", "listen_text_tr": "<PERSON><PERSON><PERSON><PERSON> sevgi<PERSON> a<PERSON>. Umar<PERSON>m her gününüz güzel ve keyifli geçer!", "listen_text_hi": "नमस्ते मेरे प्यारे दोस्त। मुझे आशा है कि आपका हर दिन सुंदर और आनंददायक हो!!", "listen_text_hu": "<PERSON>ó kedves bar<PERSON>. Remélem minden napod szép és kellemes!", "listen_text_uk": "Привіт, мій дорогий друже, сподіваюся, ти щодня прекрасна!", "listen_text_id": "<PERSON><PERSON>, te<PERSON><PERSON>, semoga kamu cantik setiap hari!", "listen_text_ms": "<PERSON><PERSON>, sa<PERSON>at saya, saya harap anda cantik setiap hari!", "listen_text_kk": "Сәлеметсіз бе, менің қымбатты досым, сендер күн сайын әдемісің деп үміттенемін!", "listen_text_cs": "<PERSON><PERSON><PERSON>, mů<PERSON> d<PERSON>, <PERSON><PERSON><PERSON><PERSON>, že jsi každý den krásná!", "listen_text_pl": "<PERSON><PERSON><PERSON>, mój drogi p<PERSON>lu, mam nad<PERSON>j<PERSON>, że jesteś piękna każdego dnia!", "listen_text_nl": "<PERSON>o mijn lieve vriend, ik hoop dat elke dag goed en fijn voor je is!!", "listen_text_sv": "Hej min kära vän, jag hoppas att varje dag är en bra och trevlig dag för dig!", "listen_text_he": "שלום, ידיד<PERSON> היקר, אני מקווה שכל יום בחייך יהיה נפלא ומאושר!", "listen_text_bn": "হ্য<PERSON><PERSON><PERSON>, আম<PERSON><PERSON> <PERSON>্রিয় বন্ধু, আ<PERSON><PERSON> আশা করি আপনার জীবনের প্রতিটি দিন চমৎকার এবং সুখী হোক!", "listen_text_fil": "Hello, kaibigan ko", "listen_text_fa": "سلام دوستای گلم امیدوارم هر روز از زندگیتون عالی و شاد باشه.", "tts_type": 0, "split_type": "all", "model_name": "tiny", "recogn_type": 0, "voice_autorate": false, "voice_role": "No", "voice_rate": "0", "video_autorate": false, "append_video": true, "deepl_authkey": "", "deepl_api": "", "deepl_gid": "", "deeplx_address": "", "deeplx_key": "", "libre_address": "", "libre_key": "", "ott_address": "", "tencent_SecretId": "", "tencent_SecretKey": "", "tencent_termlist": "", "gcloud_credential_json": "", "gcloud_language_code": "", "gcloud_voice_name": "", "gcloud_audio_encoding": "", "gcloud_ssml_gender": "", "ali_id": "", "ali_key": "", "baidu_appid": "", "baidu_miyue": "", "chatgpt_api": "", "chatgpt_key": "", "chatgpt_max_token": "4096", "chatgpt_model": "gpt-4.1", "chatgpt_template": "", "chatgpt_temperature": "0.7", "chatgpt_top_p": "1.0", "claude_api": "", "claude_key": "", "claude_model": "claude-3-5-sonnet-latest", "claude_template": "", "azure_api": "", "azure_key": "", "azure_version": "2024-06-01", "azure_model": "gpt-4.1", "azure_template": "", "gemini_key": "", "gemini_model": "gemini-2.0-flash", "gemini_template": "", "ausynclab_key": "", "gemini_ttsrole": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,Aoede,Callirrhoe,Autonoe,Enceladus,Iapetus,Umbriel,Algieba,Despina,Erinome,Algenib,Rasalgethi,Laomedeia,Achernar,Alnilam,Schedar,Gacrux,Pulcherrima,Achird,Zubenelgenubi,Vindemiatrix,Sadachbia,Sadaltager,Sulafat", "gemini_ttsstyle": "", "gemini_ttsmodel": "gemini-2.5-flash-preview-tts", "gemini_srtprompt": "# Role\nYou are a transcription assistant who efficiently transcribes audio files into text, ensuring accuracy and maintaining the sequence of the provided audio files.\n\n## Skills\n### Skill 1: Audio Transcription\n- Transcribe each audio file into text, ensuring the transcription language matches the language spoken in the audio.\n- Maintain the order of transcription results as per the sequence of the received audio files.\n- Example response format:\n  ```\n  <result>\n      <audio_text>[Transcription result for audio file 1]</audio_text>\n      <audio_text>[Transcription result for audio file 2]</audio_text>\n  </result>\n  ```\n\n## Constraints\n- Do not apologize or provide additional explanations.\n- Ensure the output is complete and includes all audio files.", "localllm_api": "", "localllm_key": "", "localllm_model": "qwen:7b", "localllm_template": "", "localllm_max_token": "4096", "localllm_temperature": "0.7", "localllm_top_p": "1.0", "zhipu_key": "", "guiji_key": "", "free_template": "", "zijiehuoshan_key": "", "zijiehuoshan_model": "", "zijiehuoshan_template": "", "ai302_key": "", "ai302_model": "", "ai302_template": "", "trans_api_url": "", "trans_secret": "", "coquitts_role": "", "coquitts_key": "", "elevenlabstts_role": [], "elevenlabstts_key": "", "elevenlabstts_models": "eleven_flash_v2_5", "openaitts_api": "", "openaitts_key": "", "openaitts_model": "tts-1", "openaitts_instructions": "", "openaitts_role": "alloy,ash,coral,echo,fable,onyx,nova,sage,shimmer,verse", "kokoro_api": "", "openairecognapi_url": "", "openairecognapi_key": "", "openairecognapi_prompt": "", "openairecognapi_model": "whisper-1", "clone_api": "", "clone_voicelist": ["clone"], "zh_recogn_api": "", "recognapi_url": "", "recognapi_key": "", "stt_url": "", "stt_model": "tiny", "sense_url": "", "ttsapi_url": "", "ttsapi_voice_role": "", "ttsapi_extra": "pyvideotrans", "ttsapi_language_boost": "auto", "ttsapi_emotion": "happy", "ai302tts_key": "", "ai302tts_model": "", "ai302tts_role": "alloy,ash,coral,echo,fable,onyx,nova,sage,shimmer,verse", "azure_speech_region": "", "azure_speech_key": "", "gptsovits_url": "", "gptsovits_role": "", "gptsovits_isv2": true, "gptsovits_extra": "pyvideotrans", "cosyvoice_url": "", "cosyvoice_role": "", "fishtts_url": "", "fishtts_role": "", "f5tts_url": "", "f5tts_model": "", "f5tts_ttstype": "F5-TTS", "f5tts_role": "", "f5tts_is_whisper": false, "doubao_appid": "", "doubao_access": "", "volcenginetts_appid": "", "volcenginetts_access": "", "volcenginetts_cluster": "", "chattts_api": "", "app_mode": "<PERSON><PERSON>z<PERSON>", "stt_source_language": 0, "stt_recogn_type": 0, "stt_model_name": "", "stt_remove_noise": false, "deepgram_apikey": "", "deepgram_utt": 200, "trans_translate_type": 0, "trans_source_language": 0, "trans_target_language": 1, "trans_out_format": 0, "dubb_source_language": 0, "dubb_tts_type": 0, "dubb_role": 0, "dubb_out_format": 0, "dubb_voice_autorate": false, "dubb_hecheng_rate": 0, "dubb_pitch_rate": 0, "dubb_volume_rate": 0}