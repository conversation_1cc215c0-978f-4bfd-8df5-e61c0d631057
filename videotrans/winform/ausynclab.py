from PySide6 import QtWidgets
from PySide6.QtCore import QThread, Signal
from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QTextEdit, QMessageBox

from videotrans.configure import config


def openwin():
    class TestWorker(QThread):
        uito = Signal(str)

        def __init__(self, *, parent=None, text=None):
            super().__init__(parent=parent)
            self.text = text

        def run(self):
            try:
                import requests
                headers = {
                    "X-API-Key": self.text,
                    "Accept": "application/json"
                }
                
                response = requests.get(
                    "https://api.ausynclab.org/api/v1/voices/list",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 200:
                        voices = result.get("result", [])
                        self.uito.emit(f"连接成功，获取到 {len(voices)} 个声音")
                    else:
                        self.uito.emit(f"API返回错误: {result}")
                else:
                    self.uito.emit(f"连接失败: HTTP {response.status_code}")
                    
            except Exception as e:
                self.uito.emit(f"连接失败: {str(e)}")

    class WinForm(QDialog):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("AusyncLab TTS 配置")
            self.setFixedSize(500, 400)
            
            # 创建布局
            layout = QVBoxLayout()
            
            # API Key输入
            key_layout = QHBoxLayout()
            key_layout.addWidget(QLabel("API Key:"))
            self.api_key_input = QLineEdit()
            self.api_key_input.setText(config.params.get("ausynclab_key", ""))
            self.api_key_input.setPlaceholderText("请输入AusyncLab API Key")
            key_layout.addWidget(self.api_key_input)
            layout.addLayout(key_layout)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            
            # 测试连接按钮
            self.test_btn = QPushButton("测试连接")
            self.test_btn.clicked.connect(self.test_connection)
            button_layout.addWidget(self.test_btn)
            
            # 保存按钮
            self.save_btn = QPushButton("保存")
            self.save_btn.clicked.connect(self.save_config)
            button_layout.addWidget(self.save_btn)
            
            layout.addLayout(button_layout)
            
            # 结果显示区域
            self.result_text = QTextEdit()
            self.result_text.setReadOnly(True)
            self.result_text.setPlainText("""
AusyncLab TTS 配置说明:

1. 注册账号: 访问 https://www.ausynclab.io/ 注册账号
2. 获取API Key: 在控制台中获取您的API Key
3. 输入API Key并点击"测试连接"验证
4. 点击"保存"保存配置

支持的语言:
- 英语 (en)
- 越南语 (vi) 
- 德语 (de)
- 法语 (fr)
- 韩语 (ko)
- 日语 (ja)
- 中文 (zh)
- 意大利语 (it)
- 葡萄牙语 (pt)
- 波兰语 (pl)
- 西班牙语 (es)
- 荷兰语 (nl)
- 泰语 (th)
- 马来语 (ms)
- 印地语 (hi)
- 阿拉伯语 (ar)
- 希伯来语 (he)
            """)
            layout.addWidget(self.result_text)
            
            self.setLayout(layout)

        def test_connection(self):
            api_key = self.api_key_input.text().strip()
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入API Key")
                return
                
            self.test_btn.setText("测试中...")
            self.test_btn.setEnabled(False)
            
            self.worker = TestWorker(parent=self, text=api_key)
            self.worker.uito.connect(self.on_test_result)
            self.worker.start()

        def on_test_result(self, result):
            self.result_text.setPlainText(result)
            self.test_btn.setText("测试连接")
            self.test_btn.setEnabled(True)

        def save_config(self):
            api_key = self.api_key_input.text().strip()
            config.params["ausynclab_key"] = api_key
            
            # 保存到配置文件
            from videotrans.util import tools
            tools.set_process(type='set_ausynclab_key')
            
            QMessageBox.information(self, "成功", "配置已保存")
            self.close()

    winform = WinForm()
    winform.show()
    winform.exec()
