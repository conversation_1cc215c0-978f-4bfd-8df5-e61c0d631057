"""
Enhanced configuration utilities for the database configuration system
"""
import re
import requests
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from videotrans.database.config_manager import get_config_manager
from videotrans.database.config_watcher import get_realtime_config_manager


class ConfigValidator:
    """Configuration validation utilities"""

    @staticmethod
    def validate_api_key(key: str, service: str = None) -> bool:
        """Validate API key format"""
        if not key or not key.strip():
            return False

        # Service-specific validation
        if service == 'openai':
            return key.startswith('sk-') and len(key) > 20
        elif service == 'deepl':
            return len(key) == 39 and ':fx' in key
        elif service == 'azure':
            return len(key) == 32 and key.isalnum()
        elif service == 'google':
            return len(key) > 30  # Google API keys are typically longer
        elif service == 'byteplus_modelark':
            return len(key) > 20  # BytePlus API keys are typically longer
        elif service == 'minimax':
            return len(key) > 20  # Minimax API keys are typically longer

        # Generic validation - at least 8 characters
        return len(key.strip()) >= 8

    @staticmethod
    def validate_url(url: str) -> bool:
        """Validate URL format"""
        if not url or not url.strip():
            return False

        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    @staticmethod
    def validate_numeric_range(value: Union[str, int, float], min_val: float = None, max_val: float = None) -> bool:
        """Validate numeric value within range"""
        try:
            num_val = float(value)
            if min_val is not None and num_val < min_val:
                return False
            if max_val is not None and num_val > max_val:
                return False
            return True
        except (ValueError, TypeError):
            return False

    @staticmethod
    def test_api_connection(service: str, config: Dict[str, Any]) -> tuple[bool, str]:
        """Test API connection for a service"""
        try:
            if service == 'openai':
                return ConfigValidator._test_openai_connection(config)
            elif service == 'deepl':
                return ConfigValidator._test_deepl_connection(config)
            elif service == 'azure':
                return ConfigValidator._test_azure_connection(config)
            else:
                return True, "Connection test not implemented for this service"
        except Exception as e:
            return False, f"Connection test failed: {str(e)}"

    @staticmethod
    def _test_openai_connection(config: Dict[str, Any]) -> tuple[bool, str]:
        """Test OpenAI API connection"""
        api_key = config.get('chatgpt_key', '')
        api_url = config.get('chatgpt_api', 'https://api.openai.com/v1')

        if not api_key:
            return False, "API key is required"

        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            response = requests.get(f"{api_url}/models", headers=headers, timeout=10)
            if response.status_code == 200:
                return True, "Connection successful"
            else:
                return False, f"API returned status {response.status_code}"
        except requests.RequestException as e:
            return False, f"Connection failed: {str(e)}"

    @staticmethod
    def _test_deepl_connection(config: Dict[str, Any]) -> tuple[bool, str]:
        """Test DeepL API connection"""
        api_key = config.get('deepl_authkey', '')

        if not api_key:
            return False, "API key is required"

        try:
            url = "https://api-free.deepl.com/v2/usage" if ':fx' in api_key else "https://api.deepl.com/v2/usage"
            headers = {'Authorization': f'DeepL-Auth-Key {api_key}'}
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                return True, "Connection successful"
            else:
                return False, f"API returned status {response.status_code}"
        except requests.RequestException as e:
            return False, f"Connection failed: {str(e)}"

    @staticmethod
    def _test_azure_connection(config: Dict[str, Any]) -> tuple[bool, str]:
        """Test Azure API connection"""
        api_key = config.get('azure_speech_key', '')
        region = config.get('azure_speech_region', '')

        if not api_key or not region:
            return False, "API key and region are required"

        try:
            url = f"https://{region}.api.cognitive.microsoft.com/sts/v1.0/issuetoken"
            headers = {'Ocp-Apim-Subscription-Key': api_key}
            response = requests.post(url, headers=headers, timeout=10)
            if response.status_code == 200:
                return True, "Connection successful"
            else:
                return False, f"API returned status {response.status_code}"
        except requests.RequestException as e:
            return False, f"Connection failed: {str(e)}"


class EnhancedConfig:
    """Enhanced configuration management with categorization and validation"""

    def __init__(self):
        self.config_manager = get_config_manager()
        self.realtime_manager = get_realtime_config_manager()
        self.validator = ConfigValidator()

    def set_api_key(self, service: str, key: str, description: str = None) -> bool:
        """Set API key with proper categorization and validation"""
        if not self.validator.validate_api_key(key, service):
            return False

        key_name = f"{service}_key" if not service.endswith('_key') else service
        desc = description or f"API key for {service.title()} service"

        self.realtime_manager.set(
            key=key_name,
            value=key,
            category="API Keys",
            description=desc,
            data_type="string",
            is_sensitive=True
        )
        return True

    def get_api_key(self, service: str, default: str = "") -> str:
        """Get API key for a service"""
        key_name = f"{service}_key" if not service.endswith('_key') else service
        return self.config_manager.get(key_name, default)

    def get(self, key_name: str, default: str = "") -> str:
        """Get API key for a service"""
        return self.config_manager.get(key_name, default)

    def set_tts_config(self, service: str, config: Dict[str, Any]) -> bool:
        """Set TTS configuration with proper categorization"""
        for key, value in config.items():
            setting_key = f"{service}_{key}" if not key.startswith(service) else key

            self.realtime_manager.set(
                key=setting_key,
                value=value,
                category="TTS",
                description=f"{service.title()} TTS: {key}",
                data_type=self._infer_data_type(value)
            )
        return True

    def get_tts_config(self, service: str) -> Dict[str, Any]:
        """Get TTS configuration for a service"""
        all_settings = self.config_manager.get_by_category("TTS")
        service_settings = {}

        for key, value in all_settings.items():
            if key.startswith(service):
                clean_key = key.replace(f"{service}_", "")
                service_settings[clean_key] = value

        return service_settings

    def set_translation_config(self, service: str, config: Dict[str, Any]) -> bool:
        """Set translation configuration with proper categorization"""
        for key, value in config.items():
            setting_key = f"{service}_{key}" if not key.startswith(service) else key

            self.realtime_manager.set(
                key=setting_key,
                value=value,
                category="Translation",
                description=f"{service.title()} Translation: {key}",
                data_type=self._infer_data_type(value)
            )
        return True

    def get_translation_config(self, service: str) -> Dict[str, Any]:
        """Get translation configuration for a service"""
        all_settings = self.config_manager.get_by_category("Translation")
        service_settings = {}

        for key, value in all_settings.items():
            if key.startswith(service):
                clean_key = key.replace(f"{service}_", "")
                service_settings[clean_key] = value

        return service_settings

    def set_recognition_config(self, service: str, config: Dict[str, Any]) -> bool:
        """Set recognition configuration with proper categorization"""
        for key, value in config.items():
            setting_key = f"{service}_{key}" if not key.startswith(service) else key

            self.realtime_manager.set(
                key=setting_key,
                value=value,
                category="Recognition",
                description=f"{service.title()} Recognition: {key}",
                data_type=self._infer_data_type(value)
            )
        return True

    def get_recognition_config(self, service: str) -> Dict[str, Any]:
        """Get recognition configuration for a service"""
        all_settings = self.config_manager.get_by_category("Recognition")
        service_settings = {}

        for key, value in all_settings.items():
            if key.startswith(service):
                clean_key = key.replace(f"{service}_", "")
                service_settings[clean_key] = value

        return service_settings

    def set_videogen_config(self, service: str, config: Dict[str, Any]) -> bool:
        """Set video generation configuration with proper categorization"""
        for key, value in config.items():
            setting_key = f"{service}_{key}" if not key.startswith(service) else key

            self.realtime_manager.set(
                key=setting_key,
                value=value,
                category="Video Generation",
                description=f"{service.title()} Video Generation: {key}",
                data_type=self._infer_data_type(value)
            )
        return True

    def get_videogen_config(self, service: str) -> Dict[str, Any]:
        """Get video generation configuration for a service"""
        all_settings = self.config_manager.get_by_category("Video Generation")
        service_settings = {}

        for key, value in all_settings.items():
            if key.startswith(service):
                clean_key = key.replace(f"{service}_", "")
                service_settings[clean_key] = value

        return service_settings

    def set_ui_preference(self, key: str, value: Any, description: str = None) -> bool:
        """Set UI preference with proper categorization"""
        self.realtime_manager.set(
            key=key,
            value=value,
            category="UI",
            description=description or f"UI preference: {key}",
            data_type=self._infer_data_type(value)
        )
        return True

    def get_ui_preference(self, key: str, default: Any = None) -> Any:
        """Get UI preference"""
        return self.config_manager.get(key, default)

    def set_system_setting(self, key: str, value: Any, description: str = None) -> bool:
        """Set system setting with proper categorization"""
        self.realtime_manager.set(
            key=key,
            value=value,
            category="System",
            description=description or f"System setting: {key}",
            data_type=self._infer_data_type(value)
        )
        return True

    def get_system_setting(self, key: str, default: Any = None) -> Any:
        """Get system setting"""
        return self.config_manager.get(key, default)

    def validate_configuration(self, category: str = None) -> Dict[str, List[str]]:
        """Validate configuration and return errors"""
        errors = {}

        if category is None or category == "API Keys":
            api_errors = self._validate_api_keys()
            if api_errors:
                errors["API Keys"] = api_errors

        if category is None or category == "TTS":
            tts_errors = self._validate_tts_config()
            if tts_errors:
                errors["TTS"] = tts_errors

        if category is None or category == "Translation":
            trans_errors = self._validate_translation_config()
            if trans_errors:
                errors["Translation"] = trans_errors

        if category is None or category == "Video Generation":
            videogen_errors = self._validate_videogen_config()
            if videogen_errors:
                errors["Video Generation"] = videogen_errors

        return errors

    def _validate_api_keys(self) -> List[str]:
        """Validate API key configurations"""
        errors = []
        api_keys = self.config_manager.get_by_category("API Keys")

        for key, value in api_keys.items():
            if value and not self.validator.validate_api_key(value):
                errors.append(f"Invalid API key format: {key}")

        return errors

    def _validate_tts_config(self) -> List[str]:
        """Validate TTS configurations"""
        errors = []
        # Add TTS-specific validation logic here
        return errors

    def _validate_translation_config(self) -> List[str]:
        """Validate translation configurations"""
        errors = []
        # Add translation-specific validation logic here
        return errors

    def _validate_videogen_config(self) -> List[str]:
        """Validate video generation configurations"""
        errors = []

        # Check BytePlus ModelArk configuration
        byteplus_key = self.get_api_key('byteplus_modelark')
        if byteplus_key and not self.validator.validate_api_key(byteplus_key, 'byteplus_modelark'):
            errors.append("Invalid BytePlus ModelArk API key format")

        return errors

    def _infer_data_type(self, value: Any) -> str:
        """Infer data type from value"""
        if isinstance(value, bool):
            return 'bool'
        elif isinstance(value, int):
            return 'int'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'

    def backup_configuration(self) -> Dict[str, Any]:
        """Create a backup of all configuration"""
        return self.config_manager.get_all()

    def restore_configuration(self, backup_data: Dict[str, Any]) -> bool:
        """Restore configuration from backup"""
        try:
            for key, value in backup_data.items():
                self.config_manager.set(key, value)
            return True
        except Exception:
            return False


# Global enhanced configuration instance
enhanced_config = EnhancedConfig()
