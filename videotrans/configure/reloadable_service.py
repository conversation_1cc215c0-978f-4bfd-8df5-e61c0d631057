"""
Base class for services that support real-time configuration reloading
"""
import threading
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from .enhanced_config import enhanced_config


class ReloadableService(ABC):
    """Base class for services that can reload their configuration in real-time"""
    
    def __init__(self, service_name: str = None):
        self.service_name = service_name or self.__class__.__name__.lower()
        self._config_lock = threading.RLock()
        self._last_config = {}
        
        # Register with the realtime config manager
        enhanced_config.realtime_manager.register_service_instance(self)
    
    @abstractmethod
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration for this service. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    def _apply_config(self, config: Dict[str, Any]) -> None:
        """Apply configuration changes. Must be implemented by subclasses."""
        pass
    
    def reload_config(self) -> bool:
        """Reload configuration and apply changes"""
        try:
            with self._config_lock:
                new_config = self._load_config()
                
                # Check if configuration actually changed
                if new_config != self._last_config:
                    print(f"Reloading configuration for {self.service_name}")
                    self._apply_config(new_config)
                    self._last_config = new_config.copy()
                    return True
                
                return False
                
        except Exception as e:
            print(f"Error reloading config for {self.service_name}: {e}")
            return False
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with automatic reloading"""
        with self._config_lock:
            # Check if we need to reload
            current_value = enhanced_config.config_manager.get(key, default)
            if key not in self._last_config or self._last_config[key] != current_value:
                self.reload_config()
            
            return current_value


class ReloadableTranslator(ReloadableService):
    """Base class for translators that support real-time configuration reloading"""
    
    def __init__(self, service_name: str = None):
        super().__init__(service_name)
        self.api_key = None
        self.api_url = None
        self.model_name = None
        self.temperature = None
        self.max_tokens = None
        
        # Load initial configuration
        self.reload_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load translator configuration"""
        config = {}
        
        # Load API key
        config['api_key'] = enhanced_config.get_api_key(self.service_name)
        
        # Load service-specific configuration
        service_config = enhanced_config.get_translation_config(self.service_name)
        config.update(service_config)
        
        # Load common translation settings
        config.update({
            'temperature': enhanced_config.config_manager.get(f'{self.service_name}_temperature', 0.7),
            'max_tokens': enhanced_config.config_manager.get(f'{self.service_name}_max_token', 4096),
            'model': enhanced_config.config_manager.get(f'{self.service_name}_model', ''),
            'api_url': enhanced_config.config_manager.get(f'{self.service_name}_api', ''),
        })
        
        return config
    
    def _apply_config(self, config: Dict[str, Any]) -> None:
        """Apply translator configuration"""
        self.api_key = config.get('api_key', '')
        self.api_url = config.get('api_url', '')
        self.model_name = config.get('model', '')
        self.temperature = float(config.get('temperature', 0.7))
        self.max_tokens = int(config.get('max_tokens', 4096))
        
        # Update environment variables if needed
        import os
        if self.service_name == 'chatgpt' and self.api_key:
            os.environ['OPENAI_API_KEY'] = self.api_key


class ReloadableTTS(ReloadableService):
    """Base class for TTS services that support real-time configuration reloading"""
    
    def __init__(self, service_name: str = None):
        super().__init__(service_name)
        self.api_key = None
        self.voice = None
        self.speed = None
        self.model = None
        
        # Load initial configuration
        self.reload_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load TTS configuration"""
        config = {}
        
        # Load API key
        config['api_key'] = enhanced_config.get_api_key(self.service_name)
        
        # Load service-specific configuration
        service_config = enhanced_config.get_tts_config(self.service_name)
        config.update(service_config)
        
        # Load common TTS settings
        config.update({
            'voice': enhanced_config.config_manager.get(f'{self.service_name}_voice', ''),
            'speed': enhanced_config.config_manager.get(f'{self.service_name}_speed', 1.0),
            'model': enhanced_config.config_manager.get(f'{self.service_name}_model', ''),
        })
        
        return config
    
    def _apply_config(self, config: Dict[str, Any]) -> None:
        """Apply TTS configuration"""
        self.api_key = config.get('api_key', '')
        self.voice = config.get('voice', '')
        self.speed = float(config.get('speed', 1.0))
        self.model = config.get('model', '')


class ReloadableRecognition(ReloadableService):
    """Base class for recognition services that support real-time configuration reloading"""
    
    def __init__(self, service_name: str = None):
        super().__init__(service_name)
        self.api_key = None
        self.model = None
        self.language = None
        
        # Load initial configuration
        self.reload_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load recognition configuration"""
        config = {}
        
        # Load API key
        config['api_key'] = enhanced_config.get_api_key(self.service_name)
        
        # Load service-specific configuration
        service_config = enhanced_config.get_recognition_config(self.service_name)
        config.update(service_config)
        
        # Load common recognition settings
        config.update({
            'model': enhanced_config.config_manager.get(f'{self.service_name}_model', ''),
            'language': enhanced_config.config_manager.get(f'{self.service_name}_language', ''),
        })
        
        return config
    
    def _apply_config(self, config: Dict[str, Any]) -> None:
        """Apply recognition configuration"""
        self.api_key = config.get('api_key', '')
        self.model = config.get('model', '')
        self.language = config.get('language', '')


def make_service_reloadable(service_class, service_type: str = 'translator'):
    """Decorator to make a service class reloadable"""
    
    if service_type == 'translator':
        base_class = ReloadableTranslator
    elif service_type == 'tts':
        base_class = ReloadableTTS
    elif service_type == 'recognition':
        base_class = ReloadableRecognition
    else:
        base_class = ReloadableService
    
    class ReloadableServiceWrapper(base_class, service_class):
        def __init__(self, *args, **kwargs):
            # Extract service name from class name
            service_name = service_class.__name__.lower().replace('tts', '').replace('recognition', '')
            base_class.__init__(self, service_name)
            service_class.__init__(self, *args, **kwargs)
        
        def _load_config(self) -> Dict[str, Any]:
            """Load configuration using the base class method"""
            return super()._load_config()
        
        def _apply_config(self, config: Dict[str, Any]) -> None:
            """Apply configuration using the base class method"""
            super()._apply_config(config)
            
            # Also update the original service's attributes if they exist
            for key, value in config.items():
                if hasattr(self, key):
                    setattr(self, key, value)
    
    return ReloadableServiceWrapper
