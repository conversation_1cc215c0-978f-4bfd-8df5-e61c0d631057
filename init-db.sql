-- PostgreSQL initialization script for PyVideoTrans
-- This script sets up the basic database structure

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS pyvideotrans;

-- Set timezone
SET timezone = 'UTC';

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Grant permissions to the application user
GRANT ALL PRIVILEGES ON DATABASE pyvideotrans TO pyvideotrans;

-- Set default schema permissions
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO pyvideotrans;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO pyvideotrans;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO pyvideotrans;

-- Create a simple health check table
CREATE TABLE IF NOT EXISTS health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(50) DEFAULT 'ok',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial health check record
INSERT INTO health_check (status) VALUES ('initialized') ON CONFLICT DO NOTHING;

-- Grant permissions on the health check table
GRANT ALL PRIVILEGES ON TABLE health_check TO pyvideotrans;
GRANT ALL PRIVILEGES ON SEQUENCE health_check_id_seq TO pyvideotrans;
